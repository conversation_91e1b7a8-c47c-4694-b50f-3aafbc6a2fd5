<template>
  <div class="flight-map-container">
    <div ref="mapContainer" class="map-container"></div>
    <div class="map-legend">
      <div class="legend-item">
        <span class="legend-icon inbound"></span>
        <span class="legend-text">进港航班</span>
      </div>
      <div class="legend-item">
        <span class="legend-icon outbound"></span>
        <span class="legend-text">出港航班</span>
      </div>
      <div class="legend-item">
        <span class="legend-icon important"></span>
        <span class="legend-text">重点航班</span>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import 'echarts-gl'
import { getVisualizationData } from '@/api/ky/flight'

// 简化的中国地图数据
const chinaMapData = {
  type: "FeatureCollection",
  features: [
    {
      type: "Feature",
      properties: { name: "中国" },
      geometry: {
        type: "Polygon",
        coordinates: [[
          [73.66, 53.56], [134.77, 53.56], [134.77, 18.16], [73.66, 18.16], [73.66, 53.56]
        ]]
      }
    }
  ]
}

export default {
  name: 'FlightMap',
  data() {
    return {
      chart: null,
      flightData: [],
      timer: null
    }
  },
  mounted() {
    this.initMap()
    this.loadFlightData()
    // 每30秒刷新一次数据
    this.timer = setInterval(() => {
      this.loadFlightData()
    }, 30000)
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer)
    }
    if (this.chart) {
      this.chart.dispose()
    }
  },
  methods: {
    initMap() {
      this.chart = echarts.init(this.$refs.mapContainer)

      // 注册中国地图
      echarts.registerMap('china', chinaMapData)

      // 南京禄口机场坐标
      const airportCoord = [118.862, 32.011]
      
      const option = {
        backgroundColor: '#0f1419',
        geo: {
          map: 'china',
          roam: true,
          zoom: 1.2,
          center: [104.114129, 37.550339],
          itemStyle: {
            borderColor: '#1e90ff',
            borderWidth: 1,
            areaColor: {
              type: 'radial',
              x: 0.5,
              y: 0.5,
              r: 0.8,
              colorStops: [{
                offset: 0,
                color: 'rgba(30, 144, 255, 0.1)'
              }, {
                offset: 1,
                color: 'rgba(30, 144, 255, 0.3)'
              }]
            }
          },
          emphasis: {
            itemStyle: {
              areaColor: 'rgba(30, 144, 255, 0.5)'
            }
          }
        },
        series: [
          {
            name: '禄口机场',
            type: 'scatter',
            coordinateSystem: 'geo',
            data: [{
              name: '南京禄口国际机场',
              value: airportCoord.concat([100]),
              symbolSize: 20,
              itemStyle: {
                color: '#ff6b6b'
              }
            }],
            symbolSize: function (val) {
              return val[2] / 5
            },
            label: {
              show: true,
              formatter: '{b}',
              position: 'right',
              color: '#fff',
              fontSize: 12
            }
          },
          {
            name: '航班',
            type: 'scatter',
            coordinateSystem: 'geo',
            data: [],
            symbolSize: 8,
            label: {
              show: true,
              formatter: function(params) {
                return params.data.flightNumber
              },
              position: 'top',
              color: '#fff',
              fontSize: 10
            },
            itemStyle: {
              color: function(params) {
                const flight = params.data
                if (flight.isImportant === '1') {
                  return '#ff4757' // 红色 - 重点航班
                } else if (flight.flightType === '0') {
                  return '#2ed573' // 绿色 - 进港航班
                } else {
                  return '#ffa502' // 黄色 - 出港航班
                }
              }
            }
          },
          {
            name: '航线',
            type: 'lines',
            coordinateSystem: 'geo',
            data: [],
            lineStyle: {
              color: function(params) {
                const flight = params.data
                if (flight.isImportant === '1') {
                  return '#ff4757'
                } else if (flight.flightType === '0') {
                  return '#2ed573'
                } else {
                  return '#ffa502'
                }
              },
              width: 2,
              opacity: 0.6,
              curveness: 0.2
            },
            effect: {
              show: true,
              period: 6,
              trailLength: 0.7,
              color: '#fff',
              symbolSize: 3
            }
          }
        ],
        tooltip: {
          trigger: 'item',
          formatter: function(params) {
            if (params.seriesName === '航班') {
              const data = params.data
              return `
                <div style="padding: 10px;">
                  <div style="font-weight: bold; margin-bottom: 5px;">${data.flightNumber}</div>
                  <div>始发地: ${data.origin}</div>
                  <div>目的地: ${data.destination}</div>
                  <div>航空公司: ${data.airline}</div>
                  <div>机型: ${data.aircraftType}</div>
                  <div>高度: ${data.altitude}米</div>
                  <div>速度: ${data.speed}km/h</div>
                  <div>状态: ${data.status === '1' ? '飞行中' : '即将起飞'}</div>
                  ${data.isImportant === '1' ? '<div style="color: #ff4757;">重点航班</div>' : ''}
                </div>
              `
            }
            return params.name
          }
        }
      }
      
      this.chart.setOption(option)
    },
    
    async loadFlightData() {
      try {
        const response = await getVisualizationData()
        this.flightData = response.data
        this.updateMapData()
      } catch (error) {
        console.error('加载航班数据失败:', error)
      }
    },
    
    updateMapData() {
      const airportCoord = [118.862, 32.011]
      const flightPoints = []
      const flightLines = []
      
      this.flightData.forEach(flight => {
        const currentCoord = [flight.longitude, flight.latitude]
        
        // 添加航班点
        flightPoints.push({
          name: flight.flightNumber,
          value: currentCoord,
          flightNumber: flight.flightNumber,
          origin: flight.origin,
          destination: flight.destination,
          airline: flight.airline,
          aircraftType: flight.aircraftType,
          altitude: flight.altitude,
          speed: flight.speed,
          status: flight.status,
          flightType: flight.flightType,
          isImportant: flight.isImportant
        })
        
        // 添加航线
        let lineCoords = []
        if (flight.flightType === '0') {
          // 进港航班：从当前位置到机场
          lineCoords = [currentCoord, airportCoord]
        } else {
          // 出港航班：从机场到当前位置
          lineCoords = [airportCoord, currentCoord]
        }
        
        flightLines.push({
          coords: lineCoords,
          flightType: flight.flightType,
          isImportant: flight.isImportant
        })
      })
      
      // 更新图表数据
      this.chart.setOption({
        series: [
          {}, // 机场点保持不变
          {
            data: flightPoints
          },
          {
            data: flightLines
          }
        ]
      })
    }
  }
}
</script>

<style scoped>
.flight-map-container {
  position: relative;
  width: 100%;
  height: 600px;
}

.map-container {
  width: 100%;
  height: 100%;
}

.map-legend {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.7);
  padding: 15px;
  border-radius: 5px;
  color: white;
}

.legend-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.legend-item:last-child {
  margin-bottom: 0;
}

.legend-icon {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 8px;
}

.legend-icon.inbound {
  background-color: #2ed573;
}

.legend-icon.outbound {
  background-color: #ffa502;
}

.legend-icon.important {
  background-color: #ff4757;
}

.legend-text {
  font-size: 12px;
}
</style>
