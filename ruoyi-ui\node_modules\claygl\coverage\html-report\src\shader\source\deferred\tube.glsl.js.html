<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for src/shader/source/deferred/tube.glsl.js</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../../prettify.css" />
    <link rel="stylesheet" href="../../../../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../../../../index.html">all files</a> / <a href="index.html">src/shader/source/deferred/</a> tube.glsl.js
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">100% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>1/1</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">100% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>0/0</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">100% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>0/0</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">100% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>1/1</span>
      </div>
    </div>
  </div>
  <div class='status-line high'></div>
<pre><table class="coverage">
<tr><td class="line-count quiet">1
2</td><td class="line-coverage quiet"><span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">export default "@export clay.deferred.tube_light\n@import clay.deferred.chunk.light_head\n@import clay.util.calculate_attenuation\n@import clay.deferred.chunk.light_equation\nuniform vec3 lightPosition;\nuniform vec3 lightColor;\nuniform float lightRange;\nuniform vec3 lightExtend;\nuniform vec3 eyePosition;\nvarying vec3 v_Position;\nvoid main()\n{\n    @import clay.deferred.chunk.gbuffer_read\n    vec3 L = lightPosition - position;\n    vec3 V = normalize(eyePosition - position);\n    vec3 R = reflect(V, N);\n    vec3 L0 = lightPosition - lightExtend - position;\n    vec3 L1 = lightPosition + lightExtend - position;\n    vec3 LD = L1 - L0;\n    float len0 = length(L0);\n    float len1 = length(L1);\n    float irra = 2.0 * clamp(dot(N, L0) / (2.0 * len0) + dot(N, L1) / (2.0 * len1), 0.0, 1.0);\n    float LDDotR = dot(R, LD);\n    float t = (LDDotR * dot(R, L0) - dot(L0, LD)) / (dot(LD, LD) - LDDotR * LDDotR);\n    t = clamp(t, 0.0, 1.0);\n    L = L0 + t * LD;\n    float dist = length(L);\n    L /= dist;\n    vec3 H = normalize(L + V);\n    float ndh = clamp(dot(N, H), 0.0, 1.0);\n    float ndv = clamp(dot(N, V), 0.0, 1.0);\n    glossiness = clamp(glossiness - 0.0 / 2.0 / dist, 0.0, 1.0);\n    gl_FragColor.rgb = lightColor * irra * lightAttenuation(dist, lightRange)\n        * (diffuseColor + D_Phong(glossiness, ndh) * F_Schlick(ndv, specularColor));\n    gl_FragColor.a = 1.0;\n}\n@end";
&nbsp;</pre></td></tr>
</table></pre>
<div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Fri Feb 23 2018 01:09:23 GMT+0800 (CST)
</div>
</div>
<script src="../../../../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../../../../sorter.js"></script>
</body>
</html>
