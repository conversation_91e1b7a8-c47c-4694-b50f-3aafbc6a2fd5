<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for src/picking/</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../../index.html">all files</a> src/picking/
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">7.06% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>12/170</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>0/78</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">5% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>1/20</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">7.06% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>12/170</span>
      </div>
    </div>
  </div>
  <div class='status-line low'></div>
<div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="PixelPicking.js"><a href="PixelPicking.js.html">PixelPicking.js</a></td>
	<td data-value="2.86" class="pic low"><div class="chart"><div class="cover-fill" style="width: 2%;"></div><div class="cover-empty" style="width:98%;"></div></div></td>
	<td data-value="2.86" class="pct low">2.86%</td>
	<td data-value="70" class="abs low">2/70</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="19" class="abs low">0/19</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="12" class="abs low">0/12</td>
	<td data-value="2.86" class="pct low">2.86%</td>
	<td data-value="70" class="abs low">2/70</td>
	</tr>

<tr>
	<td class="file low" data-value="RayPicking.js"><a href="RayPicking.js.html">RayPicking.js</a></td>
	<td data-value="9.09" class="pic low"><div class="chart"><div class="cover-fill" style="width: 9%;"></div><div class="cover-empty" style="width:91%;"></div></div></td>
	<td data-value="9.09" class="pct low">9.09%</td>
	<td data-value="99" class="abs low">9/99</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="59" class="abs low">0/59</td>
	<td data-value="12.5" class="pct low">12.5%</td>
	<td data-value="8" class="abs low">1/8</td>
	<td data-value="9.09" class="pct low">9.09%</td>
	<td data-value="99" class="abs low">9/99</td>
	</tr>

<tr>
	<td class="file high" data-value="color.glsl.js"><a href="color.glsl.js.html">color.glsl.js</a></td>
	<td data-value="100" class="pic high"><div class="chart"><div class="cover-fill cover-full" style="width: 100%;"></div><div class="cover-empty" style="width:0%;"></div></div></td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="1" class="abs high">1/1</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="1" class="abs high">1/1</td>
	</tr>

</tbody>
</table>
</div><div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Fri Feb 23 2018 01:09:23 GMT+0800 (CST)
</div>
</div>
<script src="../../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../../sorter.js"></script>
</body>
</html>
