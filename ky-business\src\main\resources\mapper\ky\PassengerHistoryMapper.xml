<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.ky.mapper.PassengerHistoryMapper">
    
    <resultMap type="PassengerHistory" id="PassengerHistoryResult">
        <result property="historyId"    column="history_id"    />
        <result property="passengerId"    column="passenger_id"    />
        <result property="idCard"    column="id_card"    />
        <result property="passengerName"    column="passenger_name"    />
        <result property="flightNumber"    column="flight_number"    />
        <result property="origin"    column="origin"    />
        <result property="destination"    column="destination"    />
        <result property="flightDate"    column="flight_date"    />
        <result property="seatNumber"    column="seat_number"    />
        <result property="cabinClass"    column="cabin_class"    />
        <result property="recordType"    column="record_type"    />
        <result property="eventDescription"    column="event_description"    />
        <result property="handleResult"    column="handle_result"    />
        <result property="riskAssessment"    column="risk_assessment"    />
        <result property="caseNumber"    column="case_number"    />
        <result property="recordSource"    column="record_source"    />
        <result property="isVerified"    column="is_verified"    />
        <result property="verifyPerson"    column="verify_person"    />
        <result property="verifyTime"    column="verify_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectPassengerHistoryVo">
        select history_id, passenger_id, id_card, passenger_name, flight_number, origin, destination, flight_date, seat_number, cabin_class, record_type, event_description, handle_result, risk_assessment, case_number, record_source, is_verified, verify_person, verify_time, create_time, update_time, remark from ky_passenger_history
    </sql>

    <select id="selectPassengerHistoryList" parameterType="PassengerHistory" resultMap="PassengerHistoryResult">
        <include refid="selectPassengerHistoryVo"/>
        <where>  
            <if test="passengerId != null "> and passenger_id = #{passengerId}</if>
            <if test="idCard != null  and idCard != ''"> and id_card = #{idCard}</if>
            <if test="passengerName != null  and passengerName != ''"> and passenger_name like concat('%', #{passengerName}, '%')</if>
            <if test="flightNumber != null  and flightNumber != ''"> and flight_number like concat('%', #{flightNumber}, '%')</if>
            <if test="origin != null  and origin != ''"> and origin = #{origin}</if>
            <if test="destination != null  and destination != ''"> and destination = #{destination}</if>
            <if test="recordType != null  and recordType != ''"> and record_type = #{recordType}</if>
            <if test="isVerified != null  and isVerified != ''"> and is_verified = #{isVerified}</if>
        </where>
        order by flight_date desc, create_time desc
    </select>
    
    <select id="selectPassengerHistoryByHistoryId" parameterType="Long" resultMap="PassengerHistoryResult">
        <include refid="selectPassengerHistoryVo"/>
        where history_id = #{historyId}
    </select>

    <select id="selectHistoryByPassengerId" parameterType="Long" resultMap="PassengerHistoryResult">
        <include refid="selectPassengerHistoryVo"/>
        where passenger_id = #{passengerId}
        order by flight_date desc, create_time desc
    </select>

    <select id="selectHistoryByIdCard" parameterType="String" resultMap="PassengerHistoryResult">
        <include refid="selectPassengerHistoryVo"/>
        where id_card = #{idCard}
        order by flight_date desc, create_time desc
    </select>

    <select id="selectAbnormalRecords" parameterType="PassengerHistory" resultMap="PassengerHistoryResult">
        <include refid="selectPassengerHistoryVo"/>
        <where>
            and record_type in ('1', '2', '3')
            <if test="idCard != null  and idCard != ''"> and id_card = #{idCard}</if>
            <if test="passengerName != null  and passengerName != ''"> and passenger_name like concat('%', #{passengerName}, '%')</if>
            <if test="recordType != null  and recordType != ''"> and record_type = #{recordType}</if>
        </where>
        order by flight_date desc, create_time desc
    </select>

    <select id="selectSecurityEvents" parameterType="PassengerHistory" resultMap="PassengerHistoryResult">
        <include refid="selectPassengerHistoryVo"/>
        <where>
            and record_type = '2'
            <if test="idCard != null  and idCard != ''"> and id_card = #{idCard}</if>
            <if test="passengerName != null  and passengerName != ''"> and passenger_name like concat('%', #{passengerName}, '%')</if>
        </where>
        order by flight_date desc, create_time desc
    </select>

    <select id="countTravelTimes" parameterType="String" resultType="int">
        select count(*) from ky_passenger_history where id_card = #{idCard}
    </select>

    <select id="countAbnormalRecords" parameterType="String" resultType="int">
        select count(*) from ky_passenger_history where id_card = #{idCard} and record_type in ('1', '2', '3')
    </select>
        
    <insert id="insertPassengerHistory" parameterType="PassengerHistory" useGeneratedKeys="true" keyProperty="historyId">
        insert into ky_passenger_history
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="passengerId != null">passenger_id,</if>
            <if test="idCard != null and idCard != ''">id_card,</if>
            <if test="passengerName != null and passengerName != ''">passenger_name,</if>
            <if test="flightNumber != null and flightNumber != ''">flight_number,</if>
            <if test="origin != null">origin,</if>
            <if test="destination != null">destination,</if>
            <if test="flightDate != null">flight_date,</if>
            <if test="seatNumber != null">seat_number,</if>
            <if test="cabinClass != null">cabin_class,</if>
            <if test="recordType != null">record_type,</if>
            <if test="eventDescription != null">event_description,</if>
            <if test="handleResult != null">handle_result,</if>
            <if test="riskAssessment != null">risk_assessment,</if>
            <if test="caseNumber != null">case_number,</if>
            <if test="recordSource != null">record_source,</if>
            <if test="isVerified != null">is_verified,</if>
            <if test="verifyPerson != null">verify_person,</if>
            <if test="verifyTime != null">verify_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="passengerId != null">#{passengerId},</if>
            <if test="idCard != null and idCard != ''">#{idCard},</if>
            <if test="passengerName != null and passengerName != ''">#{passengerName},</if>
            <if test="flightNumber != null and flightNumber != ''">#{flightNumber},</if>
            <if test="origin != null">#{origin},</if>
            <if test="destination != null">#{destination},</if>
            <if test="flightDate != null">#{flightDate},</if>
            <if test="seatNumber != null">#{seatNumber},</if>
            <if test="cabinClass != null">#{cabinClass},</if>
            <if test="recordType != null">#{recordType},</if>
            <if test="eventDescription != null">#{eventDescription},</if>
            <if test="handleResult != null">#{handleResult},</if>
            <if test="riskAssessment != null">#{riskAssessment},</if>
            <if test="caseNumber != null">#{caseNumber},</if>
            <if test="recordSource != null">#{recordSource},</if>
            <if test="isVerified != null">#{isVerified},</if>
            <if test="verifyPerson != null">#{verifyPerson},</if>
            <if test="verifyTime != null">#{verifyTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updatePassengerHistory" parameterType="PassengerHistory">
        update ky_passenger_history
        <trim prefix="SET" suffixOverrides=",">
            <if test="passengerId != null">passenger_id = #{passengerId},</if>
            <if test="idCard != null and idCard != ''">id_card = #{idCard},</if>
            <if test="passengerName != null and passengerName != ''">passenger_name = #{passengerName},</if>
            <if test="flightNumber != null and flightNumber != ''">flight_number = #{flightNumber},</if>
            <if test="origin != null">origin = #{origin},</if>
            <if test="destination != null">destination = #{destination},</if>
            <if test="flightDate != null">flight_date = #{flightDate},</if>
            <if test="seatNumber != null">seat_number = #{seatNumber},</if>
            <if test="cabinClass != null">cabin_class = #{cabinClass},</if>
            <if test="recordType != null">record_type = #{recordType},</if>
            <if test="eventDescription != null">event_description = #{eventDescription},</if>
            <if test="handleResult != null">handle_result = #{handleResult},</if>
            <if test="riskAssessment != null">risk_assessment = #{riskAssessment},</if>
            <if test="caseNumber != null">case_number = #{caseNumber},</if>
            <if test="recordSource != null">record_source = #{recordSource},</if>
            <if test="isVerified != null">is_verified = #{isVerified},</if>
            <if test="verifyPerson != null">verify_person = #{verifyPerson},</if>
            <if test="verifyTime != null">verify_time = #{verifyTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where history_id = #{historyId}
    </update>

    <delete id="deletePassengerHistoryByHistoryId" parameterType="Long">
        delete from ky_passenger_history where history_id = #{historyId}
    </delete>

    <delete id="deletePassengerHistoryByHistoryIds" parameterType="String">
        delete from ky_passenger_history where history_id in 
        <foreach item="historyId" collection="array" open="(" separator="," close=")">
            #{historyId}
        </foreach>
    </delete>

</mapper>
