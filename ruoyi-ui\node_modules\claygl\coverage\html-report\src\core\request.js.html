<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for src/core/request.js</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../../index.html">all files</a> / <a href="index.html">src/core/</a> request.js
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">58.82% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>10/17</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">42.86% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>6/14</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">66.67% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>2/3</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">58.82% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>10/17</span>
      </div>
    </div>
  </div>
  <div class='status-line medium'></div>
<pre><table class="coverage">
<tr><td class="line-count quiet">1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41</td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">function get(options) {
&nbsp;
    var xhr = new XMLHttpRequest();
&nbsp;
    xhr.open('get', options.url);
    // With response type set browser can get and put binary data
    // https://developer.mozilla.org/en-US/docs/DOM/XMLHttpRequest/Sending_and_Receiving_Binary_Data
    // Default is text, and it can be set
    // arraybuffer, blob, document, json, text
    xhr.responseType = options.responseType || <span class="branch-1 cbranch-no" title="branch not covered" >'text';</span>
&nbsp;
    <span class="missing-if-branch" title="if path not taken" >I</span>if (options.onprogress) {
        //https://developer.mozilla.org/en-US/docs/DOM/XMLHttpRequest/Using_XMLHttpRequest
<span class="cstat-no" title="statement not covered" >        xhr.onprogress = function(e) <span class="fstat-no" title="function not covered" >{</span></span>
<span class="cstat-no" title="statement not covered" >            if (e.lengthComputable) {</span>
                var percent = <span class="cstat-no" title="statement not covered" >e.loaded / e.total;</span>
<span class="cstat-no" title="statement not covered" >                options.onprogress(percent, e.loaded, e.total);</span>
            }
            else {
<span class="cstat-no" title="statement not covered" >                options.onprogress(null);</span>
            }
        };
    }
    xhr.onload = function(e) {
        <span class="missing-if-branch" title="if path not taken" >I</span>if (xhr.status &gt;= 400) {
<span class="cstat-no" title="statement not covered" >            options.onerror &amp;&amp; options.onerror();</span>
        }
        else {
            options.onload &amp;&amp; options.onload(xhr.response);
        }
    };
    <span class="missing-if-branch" title="if path not taken" >I</span>if (options.onerror) {
<span class="cstat-no" title="statement not covered" >        xhr.onerror = options.onerror;</span>
    }
    xhr.send(null);
}
&nbsp;
export default {
    get : get
};
&nbsp;</pre></td></tr>
</table></pre>
<div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Fri Feb 23 2018 01:09:23 GMT+0800 (CST)
</div>
</div>
<script src="../../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../../sorter.js"></script>
</body>
</html>
