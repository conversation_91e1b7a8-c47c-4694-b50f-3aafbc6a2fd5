<template>
  <div class="cabin-visualization">
    <!-- 航班信息头部 -->
    <div class="flight-header">
      <el-card>
        <div class="flight-info">
          <h3>{{ flightInfo.flightNumber }} 航班舱位分布</h3>
          <div class="info-row">
            <span>起飞时间: {{ flightInfo.departureTime }}</span>
            <span>航线: {{ flightInfo.origin }} → {{ flightInfo.destination }}</span>
            <span>机型: {{ flightInfo.aircraftType }}</span>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 统计信息 -->
    <div class="statistics-panel">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ statistics.totalPassengers }}</div>
              <div class="stat-label">总乘客数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card key-person">
            <div class="stat-item">
              <div class="stat-value">{{ keyPersonStatistics.totalKeyPersons }}</div>
              <div class="stat-label">重点人员</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ statistics.checkedInCount }}</div>
              <div class="stat-label">已值机</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ statistics.boardedCount }}</div>
              <div class="stat-label">已登机</div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 舱位布局 -->
    <div class="cabin-layout">
      <el-card>
        <div slot="header" class="cabin-header">
          <span>舱位布局图</span>
          <div class="legend">
            <span class="legend-item">
              <span class="seat-icon normal"></span>普通乘客
            </span>
            <span class="legend-item">
              <span class="seat-icon key-person"></span>重点人员
            </span>
            <span class="legend-item">
              <span class="seat-icon empty"></span>空座位
            </span>
          </div>
        </div>
        
        <!-- 机舱布局 -->
        <div class="aircraft-cabin">
          <!-- 头等舱 -->
          <div class="cabin-section first-class" v-if="layoutData.config">
            <div class="section-title">头等舱 ({{ statistics.firstCount }}人)</div>
            <div class="seat-rows">
              <div 
                v-for="row in getRowsForSection('first')" 
                :key="'first-' + row" 
                class="seat-row"
              >
                <div class="row-number">{{ row }}</div>
                <div class="seats">
                  <div 
                    v-for="(seat, index) in layoutData.config.seatLabels" 
                    :key="seat"
                    class="seat-container"
                  >
                    <div 
                      :class="getSeatClass(row + seat)"
                      @click="onSeatClick(row + seat)"
                    >
                      {{ row }}{{ seat }}
                    </div>
                    <div v-if="isAisle(index)" class="aisle"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 商务舱 -->
          <div class="cabin-section business-class" v-if="layoutData.config">
            <div class="section-title">商务舱 ({{ statistics.businessCount }}人)</div>
            <div class="seat-rows">
              <div 
                v-for="row in getRowsForSection('business')" 
                :key="'business-' + row" 
                class="seat-row"
              >
                <div class="row-number">{{ row }}</div>
                <div class="seats">
                  <div 
                    v-for="(seat, index) in layoutData.config.seatLabels" 
                    :key="seat"
                    class="seat-container"
                  >
                    <div 
                      :class="getSeatClass(row + seat)"
                      @click="onSeatClick(row + seat)"
                    >
                      {{ row }}{{ seat }}
                    </div>
                    <div v-if="isAisle(index)" class="aisle"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 经济舱 -->
          <div class="cabin-section economy-class" v-if="layoutData.config">
            <div class="section-title">经济舱 ({{ statistics.economyCount }}人)</div>
            <div class="seat-rows">
              <div 
                v-for="row in getRowsForSection('economy')" 
                :key="'economy-' + row" 
                class="seat-row"
              >
                <div class="row-number">{{ row }}</div>
                <div class="seats">
                  <div 
                    v-for="(seat, index) in layoutData.config.seatLabels" 
                    :key="seat"
                    class="seat-container"
                  >
                    <div 
                      :class="getSeatClass(row + seat)"
                      @click="onSeatClick(row + seat)"
                    >
                      {{ row }}{{ seat }}
                    </div>
                    <div v-if="isAisle(index)" class="aisle"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 重点人员列表 -->
    <div class="key-person-list" v-if="keyPersonList.length > 0">
      <el-card>
        <div slot="header">
          <span>重点人员列表</span>
        </div>
        <el-table :data="keyPersonList" style="width: 100%">
          <el-table-column prop="seatNumber" label="座位号" width="80"></el-table-column>
          <el-table-column prop="passengerName" label="姓名" width="120"></el-table-column>
          <el-table-column prop="keyPersonType" label="人员类型" width="120">
            <template slot-scope="scope">
              <el-tag :type="getKeyPersonTypeTag(scope.row.keyPersonType)">
                {{ getKeyPersonTypeName(scope.row.keyPersonType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="riskLevel" label="风险等级" width="100">
            <template slot-scope="scope">
              <el-tag :type="getRiskLevelTag(scope.row.riskLevel)">
                {{ getRiskLevelName(scope.row.riskLevel) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="ticketStatus" label="状态" width="100">
            <template slot-scope="scope">
              <el-tag :type="getTicketStatusTag(scope.row.ticketStatus)">
                {{ getTicketStatusName(scope.row.ticketStatus) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="remark" label="备注"></el-table-column>
          <el-table-column label="操作" width="120">
            <template slot-scope="scope">
              <el-button size="mini" @click="viewPassengerDetail(scope.row)">详情</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>

    <!-- 乘客详情对话框 -->
    <passenger-detail-dialog 
      :visible.sync="detailDialogVisible"
      :passenger-data="selectedPassenger"
      @close="detailDialogVisible = false"
    />
  </div>
</template>

<script>
import { getCabinLayoutData, getCabinStatistics, getKeyPersonStatistics } from "@/api/ky/passenger"
import { getFlightInfo } from "@/api/ky/flight"
import PassengerDetailDialog from "./components/PassengerDetailDialog"

export default {
  name: "CabinVisualization",
  components: {
    PassengerDetailDialog
  },
  data() {
    return {
      flightId: null,
      flightInfo: {},
      layoutData: {},
      statistics: {},
      keyPersonStatistics: {},
      keyPersonList: [],
      selectedPassenger: null,
      detailDialogVisible: false
    }
  },
  created() {
    this.flightId = this.$route.params.flightId || this.$route.query.flightId
    if (this.flightId) {
      this.loadData()
    }
  },
  methods: {
    async loadData() {
      try {
        // 加载航班信息
        await this.loadFlightInfo()
        // 加载舱位布局数据
        await this.loadCabinLayoutData()
        // 加载统计信息
        await this.loadStatistics()
        // 加载重点人员统计
        await this.loadKeyPersonStatistics()
      } catch (error) {
        console.error('加载数据失败:', error)
        this.$message.error('加载数据失败')
      }
    },
    async loadFlightInfo() {
      const response = await getFlightInfo(this.flightId)
      this.flightInfo = response.data
    },
    async loadCabinLayoutData() {
      const response = await getCabinLayoutData(this.flightId)
      this.layoutData = response.data
    },
    async loadStatistics() {
      const response = await getCabinStatistics(this.flightId)
      this.statistics = response.data
    },
    async loadKeyPersonStatistics() {
      const response = await getKeyPersonStatistics(this.flightId)
      this.keyPersonStatistics = response.data
      // 获取重点人员列表
      if (this.layoutData.passengers) {
        this.keyPersonList = this.layoutData.passengers.filter(p => p.isKeyPerson === '1')
      }
    },
    getRowsForSection(section) {
      if (!this.layoutData.config || !this.layoutData.config.sections) return []
      const sectionConfig = this.layoutData.config.sections[section]
      if (!sectionConfig) return []
      
      const rows = []
      for (let i = sectionConfig[0]; i <= sectionConfig[1]; i++) {
        rows.push(i)
      }
      return rows
    },
    getSeatClass(seatNumber) {
      const passenger = this.layoutData.seatMap && this.layoutData.seatMap[seatNumber]
      if (!passenger) {
        return 'seat empty'
      }
      
      const classes = ['seat', 'occupied']
      if (passenger.isKeyPerson === '1') {
        classes.push('key-person')
        if (passenger.riskLevel === '3') {
          classes.push('high-risk')
        }
      }
      return classes.join(' ')
    },
    isAisle(index) {
      return this.layoutData.config && this.layoutData.config.aisles.includes(index)
    },
    onSeatClick(seatNumber) {
      const passenger = this.layoutData.seatMap && this.layoutData.seatMap[seatNumber]
      if (passenger) {
        this.viewPassengerDetail(passenger)
      }
    },
    viewPassengerDetail(passenger) {
      this.selectedPassenger = passenger
      this.detailDialogVisible = true
    },
    getKeyPersonTypeName(type) {
      const types = {
        '1': '涉恐',
        '2': '涉毒', 
        '3': '涉黑',
        '4': '逃犯',
        '5': '精神病',
        '6': '其他危险',
        '7': 'VIP'
      }
      return types[type] || '未知'
    },
    getKeyPersonTypeTag(type) {
      const tags = {
        '1': 'danger',
        '2': 'danger',
        '3': 'danger', 
        '4': 'danger',
        '5': 'warning',
        '6': 'warning',
        '7': 'success'
      }
      return tags[type] || 'info'
    },
    getRiskLevelName(level) {
      const levels = {
        '1': '低风险',
        '2': '中风险',
        '3': '高风险'
      }
      return levels[level] || '未知'
    },
    getRiskLevelTag(level) {
      const tags = {
        '1': 'success',
        '2': 'warning', 
        '3': 'danger'
      }
      return tags[level] || 'info'
    },
    getTicketStatusName(status) {
      const statuses = {
        '0': '已订票',
        '1': '已值机',
        '2': '已登机',
        '3': '已起飞'
      }
      return statuses[status] || '未知'
    },
    getTicketStatusTag(status) {
      const tags = {
        '0': 'info',
        '1': 'warning',
        '2': 'success', 
        '3': 'primary'
      }
      return tags[status] || 'info'
    }
  }
}
</script>

<style scoped>
.cabin-visualization {
  padding: 20px;
}

.flight-header {
  margin-bottom: 20px;
}

.flight-info h3 {
  margin: 0 0 10px 0;
  color: #303133;
}

.info-row {
  display: flex;
  gap: 30px;
  color: #606266;
}

.statistics-panel {
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
}

.stat-card.key-person {
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
  color: white;
}

.stat-item {
  padding: 10px;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  opacity: 0.8;
}

.cabin-layout {
  margin-bottom: 20px;
}

.cabin-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.legend {
  display: flex;
  gap: 20px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 12px;
}

.seat-icon {
  width: 20px;
  height: 20px;
  border-radius: 4px;
  display: inline-block;
}

.seat-icon.normal {
  background-color: #67c23a;
}

.seat-icon.key-person {
  background-color: #f56c6c;
}

.seat-icon.empty {
  background-color: #e4e7ed;
  border: 1px dashed #c0c4cc;
}

.aircraft-cabin {
  max-width: 800px;
  margin: 0 auto;
  background: linear-gradient(to bottom, #f8f9fa, #e9ecef);
  border-radius: 20px;
  padding: 20px;
  position: relative;
}

.aircraft-cabin::before {
  content: '';
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 20px;
  background: #6c757d;
  border-radius: 30px 30px 0 0;
}

.cabin-section {
  margin-bottom: 30px;
  padding: 15px;
  border-radius: 10px;
}

.first-class {
  background: linear-gradient(135deg, #ffd700, #ffed4e);
}

.business-class {
  background: linear-gradient(135deg, #87ceeb, #4fc3f7);
}

.economy-class {
  background: linear-gradient(135deg, #98fb98, #66bb6a);
}

.section-title {
  text-align: center;
  font-weight: bold;
  margin-bottom: 15px;
  color: #2c3e50;
}

.seat-rows {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.seat-row {
  display: flex;
  align-items: center;
  justify-content: center;
}

.row-number {
  width: 30px;
  text-align: center;
  font-weight: bold;
  color: #495057;
}

.seats {
  display: flex;
  gap: 5px;
  align-items: center;
}

.seat-container {
  display: flex;
  align-items: center;
  gap: 5px;
}

.seat {
  width: 35px;
  height: 35px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.seat.empty {
  background-color: #f8f9fa;
  border: 1px dashed #dee2e6;
  color: #6c757d;
}

.seat.occupied {
  background-color: #28a745;
  color: white;
}

.seat.key-person {
  background-color: #dc3545;
  color: white;
  animation: pulse 2s infinite;
}

.seat.high-risk {
  background-color: #8b0000;
  box-shadow: 0 0 10px rgba(220, 53, 69, 0.5);
}

.seat:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.aisle {
  width: 20px;
  height: 35px;
  background: linear-gradient(to right, transparent, #dee2e6, transparent);
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(220, 53, 69, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(220, 53, 69, 0);
  }
}

.key-person-list {
  margin-top: 20px;
}
</style>
