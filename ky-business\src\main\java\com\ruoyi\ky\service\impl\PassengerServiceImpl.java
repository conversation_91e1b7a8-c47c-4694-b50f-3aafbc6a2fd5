package com.ruoyi.ky.service.impl;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.ky.mapper.PassengerMapper;
import com.ruoyi.ky.domain.Passenger;
import com.ruoyi.ky.service.IPassengerService;

/**
 * 乘客信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class PassengerServiceImpl implements IPassengerService 
{
    @Autowired
    private PassengerMapper passengerMapper;

    /**
     * 查询乘客信息
     * 
     * @param passengerId 乘客信息主键
     * @return 乘客信息
     */
    @Override
    public Passenger selectPassengerByPassengerId(Long passengerId)
    {
        return passengerMapper.selectPassengerByPassengerId(passengerId);
    }

    /**
     * 查询乘客信息列表
     * 
     * @param passenger 乘客信息
     * @return 乘客信息
     */
    @Override
    public List<Passenger> selectPassengerList(Passenger passenger)
    {
        return passengerMapper.selectPassengerList(passenger);
    }

    /**
     * 根据航班ID查询乘客列表
     * 
     * @param flightId 航班ID
     * @return 乘客信息集合
     */
    @Override
    public List<Passenger> selectPassengerListByFlightId(Long flightId)
    {
        return passengerMapper.selectPassengerListByFlightId(flightId);
    }

    /**
     * 根据航班号查询乘客列表
     * 
     * @param flightNumber 航班号
     * @return 乘客信息集合
     */
    @Override
    public List<Passenger> selectPassengerListByFlightNumber(String flightNumber)
    {
        return passengerMapper.selectPassengerListByFlightNumber(flightNumber);
    }

    /**
     * 查询重点人员乘客列表
     * 
     * @param passenger 乘客信息
     * @return 重点人员乘客集合
     */
    @Override
    public List<Passenger> selectKeyPersonList(Passenger passenger)
    {
        return passengerMapper.selectKeyPersonList(passenger);
    }

    /**
     * 根据身份证号查询乘客信息
     * 
     * @param idCard 身份证号
     * @return 乘客信息
     */
    @Override
    public Passenger selectPassengerByIdCard(String idCard)
    {
        return passengerMapper.selectPassengerByIdCard(idCard);
    }

    /**
     * 根据座位号查询乘客信息
     * 
     * @param flightId 航班ID
     * @param seatNumber 座位号
     * @return 乘客信息
     */
    @Override
    public Passenger selectPassengerBySeat(Long flightId, String seatNumber)
    {
        return passengerMapper.selectPassengerBySeat(flightId, seatNumber);
    }

    /**
     * 获取航班舱位分布数据
     * 
     * @param flightId 航班ID
     * @return 舱位分布数据
     */
    @Override
    public Map<String, Object> getCabinLayoutData(Long flightId)
    {
        List<Passenger> passengers = passengerMapper.selectCabinDistribution(flightId);
        Map<String, Object> result = new HashMap<>();
        
        // 按舱位等级分组
        Map<String, List<Passenger>> cabinGroups = passengers.stream()
            .collect(Collectors.groupingBy(p -> p.getCabinClass() != null ? p.getCabinClass() : "0"));
        
        result.put("passengers", passengers);
        result.put("cabinGroups", cabinGroups);
        
        // 生成座位布局数据
        Map<String, Object> seatLayout = generateSeatLayout(passengers);
        result.put("seatLayout", seatLayout);
        
        return result;
    }

    /**
     * 获取航班舱位统计信息
     * 
     * @param flightId 航班ID
     * @return 舱位统计信息
     */
    @Override
    public Map<String, Object> getCabinStatistics(Long flightId)
    {
        List<Passenger> passengers = passengerMapper.selectPassengerListByFlightId(flightId);
        Map<String, Object> statistics = new HashMap<>();
        
        // 总乘客数
        statistics.put("totalPassengers", passengers.size());
        
        // 按舱位等级统计
        long economyCount = passengers.stream().filter(p -> "0".equals(p.getCabinClass())).count();
        long businessCount = passengers.stream().filter(p -> "1".equals(p.getCabinClass())).count();
        long firstCount = passengers.stream().filter(p -> "2".equals(p.getCabinClass())).count();
        
        statistics.put("economyCount", economyCount);
        statistics.put("businessCount", businessCount);
        statistics.put("firstCount", firstCount);
        
        // 按票务状态统计
        long bookedCount = passengers.stream().filter(p -> "0".equals(p.getTicketStatus())).count();
        long checkedInCount = passengers.stream().filter(p -> "1".equals(p.getTicketStatus())).count();
        long boardedCount = passengers.stream().filter(p -> "2".equals(p.getTicketStatus())).count();
        long departedCount = passengers.stream().filter(p -> "3".equals(p.getTicketStatus())).count();
        
        statistics.put("bookedCount", bookedCount);
        statistics.put("checkedInCount", checkedInCount);
        statistics.put("boardedCount", boardedCount);
        statistics.put("departedCount", departedCount);
        
        return statistics;
    }

    /**
     * 获取重点人员统计信息
     * 
     * @param flightId 航班ID
     * @return 重点人员统计信息
     */
    @Override
    public Map<String, Object> getKeyPersonStatistics(Long flightId)
    {
        Passenger query = new Passenger();
        query.setFlightId(flightId);
        List<Passenger> keyPersons = passengerMapper.selectKeyPersonList(query);
        
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("totalKeyPersons", keyPersons.size());
        
        // 按重点人员类型统计
        Map<String, Long> typeCount = keyPersons.stream()
            .collect(Collectors.groupingBy(
                p -> p.getKeyPersonType() != null ? p.getKeyPersonType() : "0",
                Collectors.counting()
            ));
        
        statistics.put("typeCount", typeCount);
        
        // 按风险等级统计
        Map<String, Long> riskCount = keyPersons.stream()
            .collect(Collectors.groupingBy(
                p -> p.getRiskLevel() != null ? p.getRiskLevel() : "1",
                Collectors.counting()
            ));
        
        statistics.put("riskCount", riskCount);
        
        return statistics;
    }

    /**
     * 生成座位布局数据
     * 
     * @param passengers 乘客列表
     * @return 座位布局数据
     */
    private Map<String, Object> generateSeatLayout(List<Passenger> passengers)
    {
        Map<String, Object> layout = new HashMap<>();
        
        // 创建座位映射
        Map<String, Passenger> seatMap = passengers.stream()
            .filter(p -> p.getSeatNumber() != null)
            .collect(Collectors.toMap(Passenger::getSeatNumber, p -> p));
        
        layout.put("seatMap", seatMap);
        
        // 生成座位配置（假设为A320机型）
        Map<String, Object> config = new HashMap<>();
        config.put("rows", 30); // 30排座位
        config.put("seatsPerRow", 6); // 每排6个座位
        config.put("aisles", new int[]{2}); // 过道位置
        config.put("seatLabels", new String[]{"A", "B", "C", "D", "E", "F"});
        
        // 舱位分区
        Map<String, Object> sections = new HashMap<>();
        sections.put("first", new int[]{1, 3}); // 头等舱：1-3排
        sections.put("business", new int[]{4, 9}); // 商务舱：4-9排
        sections.put("economy", new int[]{10, 30}); // 经济舱：10-30排
        
        config.put("sections", sections);
        layout.put("config", config);
        
        return layout;
    }

    /**
     * 新增乘客信息
     * 
     * @param passenger 乘客信息
     * @return 结果
     */
    @Override
    public int insertPassenger(Passenger passenger)
    {
        return passengerMapper.insertPassenger(passenger);
    }

    /**
     * 修改乘客信息
     * 
     * @param passenger 乘客信息
     * @return 结果
     */
    @Override
    public int updatePassenger(Passenger passenger)
    {
        return passengerMapper.updatePassenger(passenger);
    }

    /**
     * 批量删除乘客信息
     * 
     * @param passengerIds 需要删除的乘客信息主键
     * @return 结果
     */
    @Override
    public int deletePassengerByPassengerIds(Long[] passengerIds)
    {
        return passengerMapper.deletePassengerByPassengerIds(passengerIds);
    }

    /**
     * 删除乘客信息信息
     * 
     * @param passengerId 乘客信息主键
     * @return 结果
     */
    @Override
    public int deletePassengerByPassengerId(Long passengerId)
    {
        return passengerMapper.deletePassengerByPassengerId(passengerId);
    }

    /**
     * 更新乘客票务状态
     * 
     * @param passengerId 乘客ID
     * @param ticketStatus 票务状态
     * @return 结果
     */
    @Override
    public int updatePassengerTicketStatus(Long passengerId, String ticketStatus)
    {
        return passengerMapper.updatePassengerTicketStatus(passengerId, ticketStatus);
    }

    /**
     * 批量更新乘客登机状态
     * 
     * @param flightId 航班ID
     * @param ticketStatus 票务状态
     * @return 结果
     */
    @Override
    public int batchUpdateTicketStatus(Long flightId, String ticketStatus)
    {
        return passengerMapper.batchUpdateTicketStatus(flightId, ticketStatus);
    }
}
