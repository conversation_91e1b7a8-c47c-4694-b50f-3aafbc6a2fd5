<template>
  <div class="flight-visualization">
    <!-- 顶部统计信息 -->
    <div class="statistics-panel">
      <div class="stat-card">
        <div class="stat-icon inbound">
          <i class="el-icon-bottom"></i>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ statistics.inboundCount || 0 }}</div>
          <div class="stat-label">进港航班</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon outbound">
          <i class="el-icon-top"></i>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ statistics.outboundCount || 0 }}</div>
          <div class="stat-label">出港航班</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon important">
          <i class="el-icon-warning"></i>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ statistics.importantCount || 0 }}</div>
          <div class="stat-label">重点航班</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon flying">
          <i class="el-icon-position"></i>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ statistics.flyingCount || 0 }}</div>
          <div class="stat-label">飞行中</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon departing">
          <i class="el-icon-time"></i>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ statistics.departingSoonCount || 0 }}</div>
          <div class="stat-label">即将起飞</div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧地图 -->
      <div class="map-section">
        <div class="section-header">
          <h3>空域航班实时位置</h3>
          <div class="header-actions">
            <el-button 
              size="mini" 
              type="primary" 
              icon="el-icon-refresh" 
              @click="refreshData"
              :loading="loading"
            >
              刷新
            </el-button>
            <el-switch
              v-model="autoRefresh"
              active-text="自动刷新"
              @change="toggleAutoRefresh"
            ></el-switch>
          </div>
        </div>
        <flight-map ref="flightMap" />
      </div>

      <!-- 右侧航班列表 -->
      <div class="list-section">
        <div class="section-header">
          <h3>航班信息列表</h3>
          <div class="time-display">
            {{ currentTime }}
          </div>
        </div>
        <flight-list ref="flightList" />
      </div>
    </div>
  </div>
</template>

<script>
import FlightMap from '@/components/FlightMap'
import FlightList from '@/components/FlightList'
import { getFlightStatistics } from '@/api/ky/flight'

export default {
  name: 'FlightVisualization',
  components: {
    FlightMap,
    FlightList
  },
  data() {
    return {
      statistics: {},
      loading: false,
      autoRefresh: true,
      currentTime: '',
      timeTimer: null,
      refreshTimer: null
    }
  },
  mounted() {
    this.loadStatistics()
    this.updateCurrentTime()
    this.startTimeUpdate()
    
    if (this.autoRefresh) {
      this.startAutoRefresh()
    }
  },
  beforeDestroy() {
    this.stopTimeUpdate()
    this.stopAutoRefresh()
  },
  methods: {
    async loadStatistics() {
      try {
        const response = await getFlightStatistics()
        this.statistics = response.data || {}
      } catch (error) {
        console.error('加载统计数据失败:', error)
        this.$message.error('加载统计数据失败')
      }
    },
    
    async refreshData() {
      this.loading = true
      try {
        // 刷新统计数据
        await this.loadStatistics()
        
        // 刷新地图数据
        if (this.$refs.flightMap) {
          await this.$refs.flightMap.loadFlightData()
        }
        
        // 刷新列表数据
        if (this.$refs.flightList) {
          await this.$refs.flightList.loadFlightData()
        }
        
        this.$message.success('数据刷新成功')
      } catch (error) {
        console.error('刷新数据失败:', error)
        this.$message.error('刷新数据失败')
      } finally {
        this.loading = false
      }
    },
    
    toggleAutoRefresh(value) {
      if (value) {
        this.startAutoRefresh()
      } else {
        this.stopAutoRefresh()
      }
    },
    
    startAutoRefresh() {
      this.stopAutoRefresh()
      this.refreshTimer = setInterval(() => {
        this.refreshData()
      }, 30000) // 30秒刷新一次
    },
    
    stopAutoRefresh() {
      if (this.refreshTimer) {
        clearInterval(this.refreshTimer)
        this.refreshTimer = null
      }
    },
    
    updateCurrentTime() {
      const now = new Date()
      this.currentTime = now.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    },
    
    startTimeUpdate() {
      this.timeTimer = setInterval(() => {
        this.updateCurrentTime()
      }, 1000)
    },
    
    stopTimeUpdate() {
      if (this.timeTimer) {
        clearInterval(this.timeTimer)
        this.timeTimer = null
      }
    }
  }
}
</script>

<style scoped>
.flight-visualization {
  padding: 20px;
  background: #f0f2f5;
  min-height: calc(100vh - 84px);
}

.statistics-panel {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.stat-card {
  flex: 1;
  background: white;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 24px;
  color: white;
}

.stat-icon.inbound {
  background: linear-gradient(135deg, #2ed573, #1e90ff);
}

.stat-icon.outbound {
  background: linear-gradient(135deg, #ffa502, #ff6348);
}

.stat-icon.important {
  background: linear-gradient(135deg, #ff4757, #ff3742);
}

.stat-icon.flying {
  background: linear-gradient(135deg, #5352ed, #3742fa);
}

.stat-icon.departing {
  background: linear-gradient(135deg, #f39c12, #e67e22);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: 600;
  color: #2c3e50;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #7f8c8d;
  margin-top: 5px;
}

.main-content {
  display: flex;
  gap: 20px;
  height: calc(100vh - 200px);
}

.map-section {
  flex: 2;
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.list-section {
  flex: 1;
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid #e4e7ed;
}

.section-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 18px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 15px;
}

.time-display {
  font-size: 14px;
  color: #606266;
  font-family: 'Courier New', monospace;
  background: #f5f7fa;
  padding: 5px 10px;
  border-radius: 4px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-content {
    flex-direction: column;
    height: auto;
  }
  
  .map-section, .list-section {
    flex: none;
  }
  
  .statistics-panel {
    flex-wrap: wrap;
  }
  
  .stat-card {
    min-width: calc(50% - 10px);
  }
}

@media (max-width: 768px) {
  .flight-visualization {
    padding: 10px;
  }
  
  .statistics-panel {
    flex-direction: column;
    gap: 10px;
  }
  
  .stat-card {
    min-width: 100%;
  }
  
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .header-actions {
    width: 100%;
    justify-content: space-between;
  }
}
</style>
