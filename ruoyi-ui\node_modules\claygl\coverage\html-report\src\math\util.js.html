<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for src/math/util.js</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../../index.html">all files</a> / <a href="index.html">src/math/</a> util.js
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">35.71% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>5/14</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">100% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>0/0</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">33.33% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>1/3</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">35.71% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>5/14</span>
      </div>
    </div>
  </div>
  <div class='status-line low'></div>
<pre><table class="coverage">
<tr><td class="line-count quiet">1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24</td><td class="line-coverage quiet"><span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">44×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">var mathUtil = {};
&nbsp;
mathUtil.isPowerOfTwo = function (value) {
    return (value &amp; (value - 1)) === 0;
};
&nbsp;
mathUtil.nextPowerOfTwo = function (value) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    value --;</span>
<span class="cstat-no" title="statement not covered" >    value |= value &gt;&gt; 1;</span>
<span class="cstat-no" title="statement not covered" >    value |= value &gt;&gt; 2;</span>
<span class="cstat-no" title="statement not covered" >    value |= value &gt;&gt; 4;</span>
<span class="cstat-no" title="statement not covered" >    value |= value &gt;&gt; 8;</span>
<span class="cstat-no" title="statement not covered" >    value |= value &gt;&gt; 16;</span>
<span class="cstat-no" title="statement not covered" >    value ++;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return value;</span>
};
&nbsp;
mathUtil.nearestPowerOfTwo = function (value) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    return Math.pow( 2, Math.round( Math.log( value ) / Math.LN2 ) );</span>
};
&nbsp;
export default mathUtil;
&nbsp;</pre></td></tr>
</table></pre>
<div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Fri Feb 23 2018 01:09:23 GMT+0800 (CST)
</div>
</div>
<script src="../../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../../sorter.js"></script>
</body>
</html>
