package com.ruoyi.system.mapper;

import com.ruoyi.system.domain.TUserMoney;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

public interface TUserMoneyMapper {
    int deleteByPrimaryKey(Long id);

    int insert(TUserMoney record);

    int insertSelective(TUserMoney record);

    TUserMoney selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(TUserMoney record);

    int updateByPrimaryKey(TUserMoney record);

    void updateAmount(@Param("userName") String userName, @Param("amount") BigDecimal amount);

    void updateBatch(List<TUserMoney> asList);
}
