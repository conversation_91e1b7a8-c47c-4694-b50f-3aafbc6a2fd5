package com.ruoyi.ky.mapper;

import java.util.List;
import com.ruoyi.ky.domain.Passenger;

/**
 * 乘客信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface PassengerMapper 
{
    /**
     * 查询乘客信息
     * 
     * @param passengerId 乘客信息主键
     * @return 乘客信息
     */
    public Passenger selectPassengerByPassengerId(Long passengerId);

    /**
     * 查询乘客信息列表
     * 
     * @param passenger 乘客信息
     * @return 乘客信息集合
     */
    public List<Passenger> selectPassengerList(Passenger passenger);

    /**
     * 根据航班ID查询乘客列表
     * 
     * @param flightId 航班ID
     * @return 乘客信息集合
     */
    public List<Passenger> selectPassengerListByFlightId(Long flightId);

    /**
     * 根据航班号查询乘客列表
     * 
     * @param flightNumber 航班号
     * @return 乘客信息集合
     */
    public List<Passenger> selectPassengerListByFlightNumber(String flightNumber);

    /**
     * 查询重点人员乘客列表
     * 
     * @param passenger 乘客信息
     * @return 重点人员乘客集合
     */
    public List<Passenger> selectKeyPersonList(Passenger passenger);

    /**
     * 根据身份证号查询乘客信息
     * 
     * @param idCard 身份证号
     * @return 乘客信息
     */
    public Passenger selectPassengerByIdCard(String idCard);

    /**
     * 根据座位号查询乘客信息
     * 
     * @param flightId 航班ID
     * @param seatNumber 座位号
     * @return 乘客信息
     */
    public Passenger selectPassengerBySeat(Long flightId, String seatNumber);

    /**
     * 查询航班舱位分布统计
     * 
     * @param flightId 航班ID
     * @return 舱位统计信息
     */
    public List<Passenger> selectCabinDistribution(Long flightId);

    /**
     * 新增乘客信息
     * 
     * @param passenger 乘客信息
     * @return 结果
     */
    public int insertPassenger(Passenger passenger);

    /**
     * 修改乘客信息
     * 
     * @param passenger 乘客信息
     * @return 结果
     */
    public int updatePassenger(Passenger passenger);

    /**
     * 删除乘客信息
     * 
     * @param passengerId 乘客信息主键
     * @return 结果
     */
    public int deletePassengerByPassengerId(Long passengerId);

    /**
     * 批量删除乘客信息
     * 
     * @param passengerIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePassengerByPassengerIds(Long[] passengerIds);

    /**
     * 更新乘客票务状态
     * 
     * @param passengerId 乘客ID
     * @param ticketStatus 票务状态
     * @return 结果
     */
    public int updatePassengerTicketStatus(Long passengerId, String ticketStatus);

    /**
     * 批量更新乘客登机状态
     * 
     * @param flightId 航班ID
     * @param ticketStatus 票务状态
     * @return 结果
     */
    public int batchUpdateTicketStatus(Long flightId, String ticketStatus);
}
