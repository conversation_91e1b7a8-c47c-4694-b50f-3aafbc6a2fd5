package com.ruoyi.ky.service;

import java.util.List;
import java.util.Map;
import com.ruoyi.ky.domain.PassengerHistory;

/**
 * 乘客历史记录Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface IPassengerHistoryService 
{
    /**
     * 查询乘客历史记录
     * 
     * @param historyId 乘客历史记录主键
     * @return 乘客历史记录
     */
    public PassengerHistory selectPassengerHistoryByHistoryId(Long historyId);

    /**
     * 查询乘客历史记录列表
     * 
     * @param passengerHistory 乘客历史记录
     * @return 乘客历史记录集合
     */
    public List<PassengerHistory> selectPassengerHistoryList(PassengerHistory passengerHistory);

    /**
     * 根据乘客ID查询历史记录
     * 
     * @param passengerId 乘客ID
     * @return 乘客历史记录集合
     */
    public List<PassengerHistory> selectHistoryByPassengerId(Long passengerId);

    /**
     * 根据身份证号查询历史记录
     * 
     * @param idCard 身份证号
     * @return 乘客历史记录集合
     */
    public List<PassengerHistory> selectHistoryByIdCard(String idCard);

    /**
     * 查询异常记录
     * 
     * @param passengerHistory 查询条件
     * @return 异常记录集合
     */
    public List<PassengerHistory> selectAbnormalRecords(PassengerHistory passengerHistory);

    /**
     * 查询安全事件记录
     * 
     * @param passengerHistory 查询条件
     * @return 安全事件记录集合
     */
    public List<PassengerHistory> selectSecurityEvents(PassengerHistory passengerHistory);

    /**
     * 获取乘客详细信息（包含历史记录统计）
     * 
     * @param idCard 身份证号
     * @return 乘客详细信息
     */
    public Map<String, Object> getPassengerDetailInfo(String idCard);

    /**
     * 获取乘客风险评估信息
     * 
     * @param idCard 身份证号
     * @return 风险评估信息
     */
    public Map<String, Object> getPassengerRiskAssessment(String idCard);

    /**
     * 统计乘客出行次数
     * 
     * @param idCard 身份证号
     * @return 出行次数
     */
    public int countTravelTimes(String idCard);

    /**
     * 统计乘客异常记录次数
     * 
     * @param idCard 身份证号
     * @return 异常记录次数
     */
    public int countAbnormalRecords(String idCard);

    /**
     * 新增乘客历史记录
     * 
     * @param passengerHistory 乘客历史记录
     * @return 结果
     */
    public int insertPassengerHistory(PassengerHistory passengerHistory);

    /**
     * 修改乘客历史记录
     * 
     * @param passengerHistory 乘客历史记录
     * @return 结果
     */
    public int updatePassengerHistory(PassengerHistory passengerHistory);

    /**
     * 批量删除乘客历史记录
     * 
     * @param historyIds 需要删除的乘客历史记录主键集合
     * @return 结果
     */
    public int deletePassengerHistoryByHistoryIds(Long[] historyIds);

    /**
     * 删除乘客历史记录信息
     * 
     * @param historyId 乘客历史记录主键
     * @return 结果
     */
    public int deletePassengerHistoryByHistoryId(Long historyId);
}
