<template>
  <el-dialog
    title="乘客历史记录"
    :visible.sync="dialogVisible"
    width="80%"
    :before-close="handleClose"
  >
    <div class="passenger-history-content" v-if="passengerData">
      <!-- 乘客基本信息 -->
      <div class="passenger-summary">
        <el-card>
          <div class="summary-header">
            <div class="passenger-info">
              <h3>{{ passengerData.passengerName }}</h3>
              <p>身份证号: {{ passengerData.idCard }}</p>
            </div>
            <div class="summary-stats">
              <el-row :gutter="20">
                <el-col :span="8">
                  <div class="stat-item">
                    <div class="stat-value">{{ totalRecords }}</div>
                    <div class="stat-label">总记录数</div>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="stat-item">
                    <div class="stat-value">{{ abnormalRecords }}</div>
                    <div class="stat-label">异常记录</div>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="stat-item">
                    <div class="stat-value">{{ recentDays }}</div>
                    <div class="stat-label">最近出行(天)</div>
                  </div>
                </el-col>
              </el-row>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 筛选和搜索 -->
      <div class="filter-section">
        <el-card>
          <el-row :gutter="20">
            <el-col :span="6">
              <el-select v-model="filterType" placeholder="记录类型" @change="onFilterChange">
                <el-option label="全部记录" value=""></el-option>
                <el-option label="正常记录" value="0"></el-option>
                <el-option label="异常行为" value="1"></el-option>
                <el-option label="安全事件" value="2"></el-option>
                <el-option label="违规记录" value="3"></el-option>
              </el-select>
            </el-col>
            <el-col :span="6">
              <el-date-picker
                v-model="dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                @change="onFilterChange"
              >
              </el-date-picker>
            </el-col>
            <el-col :span="8">
              <el-input
                v-model="searchKeyword"
                placeholder="搜索航班号、起点、终点或事件描述"
                @input="onFilterChange"
              >
                <i slot="prefix" class="el-input__icon el-icon-search"></i>
              </el-input>
            </el-col>
            <el-col :span="4">
              <el-button type="primary" @click="refreshData">
                <i class="el-icon-refresh"></i> 刷新
              </el-button>
            </el-col>
          </el-row>
        </el-card>
      </div>

      <!-- 历史记录时间线 -->
      <div class="timeline-section">
        <el-card>
          <div slot="header">
            <span>历史记录时间线</span>
            <el-radio-group v-model="viewMode" style="float: right;">
              <el-radio-button label="timeline">时间线</el-radio-button>
              <el-radio-button label="table">表格</el-radio-button>
            </el-radio-group>
          </div>

          <div v-if="loading" class="loading-container">
            <el-loading-spinner></el-loading-spinner>
            <span>加载历史记录中...</span>
          </div>

          <!-- 时间线视图 -->
          <div v-else-if="viewMode === 'timeline'" class="timeline-view">
            <el-timeline>
              <el-timeline-item
                v-for="record in filteredRecords"
                :key="record.historyId"
                :timestamp="formatDate(record.flightDate)"
                :type="getTimelineType(record.recordType)"
                :color="getTimelineColor(record.recordType)"
                placement="top"
              >
                <el-card class="timeline-card" :class="getRecordCardClass(record.recordType)">
                  <div class="timeline-header">
                    <span class="flight-info">
                      {{ record.flightNumber }} | {{ record.origin }} → {{ record.destination }}
                    </span>
                    <el-tag :type="getRecordTypeTagType(record.recordType)" size="mini">
                      {{ getRecordTypeText(record.recordType) }}
                    </el-tag>
                  </div>
                  <div class="timeline-content">
                    <p><strong>座位:</strong> {{ record.seatNumber }}</p>
                    <p><strong>事件:</strong> {{ record.eventDescription || '无特殊事件' }}</p>
                    <p v-if="record.handleResult"><strong>处理结果:</strong> {{ record.handleResult }}</p>
                    <p v-if="record.riskAssessment"><strong>风险评估:</strong> {{ record.riskAssessment }}</p>
                  </div>
                  <div class="timeline-actions">
                    <el-button size="mini" @click="viewRecordDetail(record)">详情</el-button>
                    <el-button v-if="record.recordType !== '0'" size="mini" type="warning" @click="viewSecurityDetail(record)">
                      安全详情
                    </el-button>
                  </div>
                </el-card>
              </el-timeline-item>
            </el-timeline>
          </div>

          <!-- 表格视图 -->
          <div v-else class="table-view">
            <el-table :data="filteredRecords" style="width: 100%" max-height="500">
              <el-table-column prop="flightDate" label="日期" width="100" sortable>
                <template slot-scope="scope">
                  {{ formatDate(scope.row.flightDate) }}
                </template>
              </el-table-column>
              <el-table-column prop="flightNumber" label="航班号" width="100"></el-table-column>
              <el-table-column prop="origin" label="起点" width="80"></el-table-column>
              <el-table-column prop="destination" label="终点" width="80"></el-table-column>
              <el-table-column prop="seatNumber" label="座位" width="80"></el-table-column>
              <el-table-column prop="cabinClass" label="舱位" width="80">
                <template slot-scope="scope">
                  {{ getCabinClassText(scope.row.cabinClass) }}
                </template>
              </el-table-column>
              <el-table-column prop="recordType" label="记录类型" width="100">
                <template slot-scope="scope">
                  <el-tag :type="getRecordTypeTagType(scope.row.recordType)" size="mini">
                    {{ getRecordTypeText(scope.row.recordType) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="eventDescription" label="事件描述" show-overflow-tooltip></el-table-column>
              <el-table-column prop="handleResult" label="处理结果" width="120" show-overflow-tooltip></el-table-column>
              <el-table-column prop="isVerified" label="核实状态" width="80">
                <template slot-scope="scope">
                  <el-tag :type="scope.row.isVerified === '1' ? 'success' : 'warning'" size="mini">
                    {{ scope.row.isVerified === '1' ? '已核实' : '待核实' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="150">
                <template slot-scope="scope">
                  <el-button size="mini" @click="viewRecordDetail(scope.row)">详情</el-button>
                  <el-button v-if="scope.row.recordType !== '0'" size="mini" type="warning" @click="viewSecurityDetail(scope.row)">
                    安全
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <!-- 分页 -->
          <div class="pagination-container" v-if="filteredRecords.length > 0">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage"
              :page-sizes="[10, 20, 50, 100]"
              :page-size="pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="totalRecords"
            >
            </el-pagination>
          </div>
        </el-card>
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关闭</el-button>
      <el-button type="primary" @click="exportHistoryData">
        <i class="el-icon-download"></i> 导出历史记录
      </el-button>
    </div>

    <!-- 记录详情对话框 -->
    <record-detail-dialog
      :visible.sync="detailDialogVisible"
      :record-data="selectedRecord"
    />

    <!-- 安全详情对话框 -->
    <security-detail-dialog
      :visible.sync="securityDialogVisible"
      :record-data="selectedRecord"
    />
  </el-dialog>
</template>

<script>
import { getHistoryByIdCard } from "@/api/ky/passengerHistory"
import RecordDetailDialog from "./RecordDetailDialog"
import SecurityDetailDialog from "./SecurityDetailDialog"

export default {
  name: "PassengerHistoryModal",
  components: {
    RecordDetailDialog,
    SecurityDetailDialog
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    passengerData: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      dialogVisible: false,
      historyRecords: [],
      filteredRecords: [],
      loading: false,
      viewMode: 'timeline',
      filterType: '',
      dateRange: null,
      searchKeyword: '',
      currentPage: 1,
      pageSize: 20,
      detailDialogVisible: false,
      securityDialogVisible: false,
      selectedRecord: null
    }
  },
  computed: {
    totalRecords() {
      return this.historyRecords.length
    },
    abnormalRecords() {
      return this.historyRecords.filter(record => record.recordType !== '0').length
    },
    recentDays() {
      if (this.historyRecords.length === 0) return 0
      const lastDate = new Date(this.historyRecords[0].flightDate)
      const now = new Date()
      const diffTime = Math.abs(now - lastDate)
      return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
      if (val && this.passengerData) {
        this.loadHistoryData()
      }
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    }
  },
  methods: {
    async loadHistoryData() {
      if (!this.passengerData.idCard) return
      
      this.loading = true
      try {
        const response = await getHistoryByIdCard(this.passengerData.idCard)
        this.historyRecords = response.data || []
        this.applyFilters()
      } catch (error) {
        console.error('加载历史记录失败:', error)
        this.$message.error('加载历史记录失败')
      } finally {
        this.loading = false
      }
    },
    refreshData() {
      this.loadHistoryData()
    },
    onFilterChange() {
      this.applyFilters()
    },
    applyFilters() {
      let filtered = [...this.historyRecords]
      
      // 按记录类型筛选
      if (this.filterType) {
        filtered = filtered.filter(record => record.recordType === this.filterType)
      }
      
      // 按日期范围筛选
      if (this.dateRange && this.dateRange.length === 2) {
        const startDate = this.dateRange[0]
        const endDate = this.dateRange[1]
        filtered = filtered.filter(record => {
          const recordDate = new Date(record.flightDate)
          return recordDate >= startDate && recordDate <= endDate
        })
      }
      
      // 按关键词搜索
      if (this.searchKeyword.trim()) {
        const keyword = this.searchKeyword.toLowerCase()
        filtered = filtered.filter(record =>
          (record.flightNumber && record.flightNumber.toLowerCase().includes(keyword)) ||
          (record.origin && record.origin.toLowerCase().includes(keyword)) ||
          (record.destination && record.destination.toLowerCase().includes(keyword)) ||
          (record.eventDescription && record.eventDescription.toLowerCase().includes(keyword))
        )
      }
      
      this.filteredRecords = filtered
      this.currentPage = 1
    },
    viewRecordDetail(record) {
      this.selectedRecord = record
      this.detailDialogVisible = true
    },
    viewSecurityDetail(record) {
      this.selectedRecord = record
      this.securityDialogVisible = true
    },
    handleSizeChange(val) {
      this.pageSize = val
    },
    handleCurrentChange(val) {
      this.currentPage = val
    },
    handleClose() {
      this.dialogVisible = false
      this.resetData()
    },
    resetData() {
      this.historyRecords = []
      this.filteredRecords = []
      this.filterType = ''
      this.dateRange = null
      this.searchKeyword = ''
      this.currentPage = 1
    },
    exportHistoryData() {
      this.$message.success('导出功能开发中...')
    },
    getTimelineType(recordType) {
      const types = {
        '0': 'primary',
        '1': 'warning',
        '2': 'danger',
        '3': 'danger'
      }
      return types[recordType] || 'primary'
    },
    getTimelineColor(recordType) {
      const colors = {
        '0': '#409eff',
        '1': '#e6a23c',
        '2': '#f56c6c',
        '3': '#f56c6c'
      }
      return colors[recordType] || '#409eff'
    },
    getRecordCardClass(recordType) {
      const classes = {
        '0': 'normal-record',
        '1': 'warning-record',
        '2': 'danger-record',
        '3': 'danger-record'
      }
      return classes[recordType] || 'normal-record'
    },
    getRecordTypeText(type) {
      const types = {
        '0': '正常记录',
        '1': '异常行为',
        '2': '安全事件',
        '3': '违规记录'
      }
      return types[type] || '未知'
    },
    getRecordTypeTagType(type) {
      const types = {
        '0': 'success',
        '1': 'warning',
        '2': 'danger',
        '3': 'danger'
      }
      return types[type] || 'info'
    },
    getCabinClassText(cabinClass) {
      const classes = {
        '0': '经济舱',
        '1': '商务舱',
        '2': '头等舱'
      }
      return classes[cabinClass] || '未知'
    },
    formatDate(date) {
      if (!date) return ''
      return new Date(date).toLocaleDateString()
    }
  }
}
</script>

<style scoped>
.passenger-history-content {
  max-height: 80vh;
  overflow-y: auto;
}

.passenger-summary {
  margin-bottom: 20px;
}

.summary-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.passenger-info h3 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 20px;
}

.passenger-info p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.summary-stats {
  min-width: 300px;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 12px;
  color: #909399;
}

.filter-section {
  margin-bottom: 20px;
}

.timeline-section {
  margin-bottom: 20px;
}

.loading-container {
  text-align: center;
  padding: 40px;
  color: #909399;
}

.timeline-view {
  padding: 20px 0;
}

.timeline-card {
  margin-bottom: 10px;
  border-left: 4px solid #409eff;
}

.timeline-card.warning-record {
  border-left-color: #e6a23c;
}

.timeline-card.danger-record {
  border-left-color: #f56c6c;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.flight-info {
  font-weight: bold;
  color: #303133;
}

.timeline-content {
  margin-bottom: 15px;
}

.timeline-content p {
  margin: 5px 0;
  color: #606266;
  font-size: 14px;
}

.timeline-actions {
  text-align: right;
}

.table-view {
  margin-top: 20px;
}

.pagination-container {
  margin-top: 20px;
  text-align: center;
}

.dialog-footer {
  text-align: right;
}
</style>
