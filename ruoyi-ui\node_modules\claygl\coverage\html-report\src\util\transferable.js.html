<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for src/util/transferable.js</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../../index.html">all files</a> / <a href="index.html">src/util/</a> transferable.js
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">92.68% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>38/41</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">74.19% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>23/31</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">100% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>4/4</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">92.68% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>38/41</span>
      </div>
    </div>
  </div>
  <div class='status-line high'></div>
<pre><table class="coverage">
<tr><td class="line-count quiet">1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
116
117
118
119
120
121
122
123
124
125
126
127
128
129
130
131</td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-yes">27×</span>
<span class="cline-any cline-yes">27×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">27×</span>
<span class="cline-any cline-yes">9×</span>
<span class="cline-any cline-yes">9×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">9×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">12×</span>
<span class="cline-any cline-yes">4×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">8×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">import util from '../core/util';
import Geometry from '../Geometry';
import BoundingBox from '../math/BoundingBox';
import Vector3 from '../math/Vector3';
&nbsp;
var META = {
    version: 1.0,
    type: 'Geometry',
    generator: 'util.transferable.toObject'
};
&nbsp;
/**
 * @alias clay.util.transferable
 */
var transferableUtil = {
    /**
     * Convert geometry to a object containing transferable data
     * @param {Geometry} geometry geometry
     * @param {Boolean} shallow whether shallow copy
     * @returns {Object} { data : data, buffers : buffers }, buffers is the transferable list
     */
    toObject : function (geometry, shallow) {
        <span class="missing-if-branch" title="if path not taken" >I</span>if (!geometry) {
<span class="cstat-no" title="statement not covered" >            return null;</span>
        }
        var data = {
            metadata : util.extend({}, META)
        };
        //transferable buffers
        var buffers = [];
&nbsp;
        //dynamic
        data.dynamic = geometry.dynamic;
&nbsp;
        //bounding box
        <span class="missing-if-branch" title="else path not taken" >E</span>if (geometry.boundingBox) {
            data.boundingBox = {
                min : geometry.boundingBox.min.toArray(),
                max : geometry.boundingBox.max.toArray()
            };
        }
&nbsp;
        //indices
        <span class="missing-if-branch" title="else path not taken" >E</span>if (geometry.indices &amp;&amp; geometry.indices.length &gt; 0) {
            data.indices = copyIfNecessary(geometry.indices, shallow);
            buffers.push(data.indices.buffer);
        }
&nbsp;
        //attributes
        data.attributes = {};
        for (var p in geometry.attributes) {
            <span class="missing-if-branch" title="else path not taken" >E</span>if (geometry.attributes.hasOwnProperty(p)) {
                var attr = geometry.attributes[p];
                //ignore empty attributes
                if (attr &amp;&amp; attr.value &amp;&amp; attr.value.length &gt; 0) {
                    attr = data.attributes[p] = copyAttribute(attr, shallow);
                    buffers.push(attr.value.buffer);
                }
            }
        }
&nbsp;
        return {
            data : data,
            buffers : buffers
        };
    },
&nbsp;
    /**
     * Reproduce a geometry from object generated by toObject
     * @param {Object} object object generated by toObject
     * @returns {Geometry} geometry
     */
    toGeometry : function (object) {
        <span class="missing-if-branch" title="if path not taken" >I</span>if (!object) {
<span class="cstat-no" title="statement not covered" >            return null;</span>
        }
        if (object.data &amp;&amp; object.buffers) {
            return transferableUtil.toGeometry(object.data);
        }
        <span class="missing-if-branch" title="if path not taken" >I</span>if (!object.metadata || object.metadata.generator !== META.generator) {
<span class="cstat-no" title="statement not covered" >            throw new Error('[util.transferable.toGeometry] the object is not generated by util.transferable.');</span>
        }
&nbsp;
        //basic options
        var options = {
            dynamic : object.dynamic,
            indices : object.indices
        };
&nbsp;
        <span class="missing-if-branch" title="else path not taken" >E</span>if (object.boundingBox) {
            var min = new Vector3().setArray(object.boundingBox.min);
            var max = new Vector3().setArray(object.boundingBox.max);
            options.boundingBox = new BoundingBox(min, max);
        }
&nbsp;
        var geometry = new Geometry(options);
&nbsp;
        //attributes
        for (var p in object.attributes) {
            <span class="missing-if-branch" title="else path not taken" >E</span>if (object.attributes.hasOwnProperty(p)) {
                var attr = object.attributes[p];
                geometry.attributes[p] = new Geometry.Attribute(attr.name, attr.type, attr.size, attr.semantic);
                geometry.attributes[p].value = attr.value;
            }
        }
&nbsp;
        return geometry;
    }
&nbsp;
}
&nbsp;
function copyAttribute(attr, shallow) {
    return {
        name : attr.name,
        type : attr.type,
        size : attr.size,
        semantic : attr.semantic,
        value : copyIfNecessary(attr.value, shallow)
    };
}
&nbsp;
function copyIfNecessary(arr, shallow) {
    if (!shallow) {
        return new arr.constructor(arr);
    } else {
        return arr;
    }
}
&nbsp;
export default transferableUtil;
&nbsp;</pre></td></tr>
</table></pre>
<div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Fri Feb 23 2018 01:09:23 GMT+0800 (CST)
</div>
</div>
<script src="../../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../../sorter.js"></script>
</body>
</html>
