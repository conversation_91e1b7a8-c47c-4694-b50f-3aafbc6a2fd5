<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for src/geometry/Cube.js</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../../index.html">all files</a> / <a href="index.html">src/geometry/</a> Cube.js
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">98.25% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>56/57</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">75% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>9/12</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">100% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>3/3</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">98.25% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>56/57</span>
      </div>
    </div>
  </div>
  <div class='status-line high'></div>
<pre><table class="coverage">
<tr><td class="line-count quiet">1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
116
117
118
119
120
121
122
123
124
125
126
127
128
129
130
131
132
133
134
135
136
137
138
139</td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">35×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">35×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">35×</span>
<span class="cline-any cline-yes">35×</span>
<span class="cline-any cline-yes">35×</span>
<span class="cline-any cline-yes">35×</span>
<span class="cline-any cline-yes">210×</span>
<span class="cline-any cline-yes">210×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">35×</span>
<span class="cline-any cline-yes">105×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">35×</span>
<span class="cline-any cline-yes">35×</span>
<span class="cline-any cline-yes">35×</span>
<span class="cline-any cline-yes">35×</span>
<span class="cline-any cline-yes">210×</span>
<span class="cline-any cline-yes">210×</span>
<span class="cline-any cline-yes">630×</span>
<span class="cline-any cline-yes">630×</span>
<span class="cline-any cline-yes">630×</span>
<span class="cline-any cline-yes">630×</span>
<span class="cline-any cline-yes">630×</span>
<span class="cline-any cline-yes">7616×</span>
<span class="cline-any cline-yes">7616×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">7616×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">210×</span>
<span class="cline-any cline-yes">210×</span>
<span class="cline-any cline-yes">1644×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">210×</span>
<span class="cline-any cline-yes">210×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">35×</span>
<span class="cline-any cline-yes">35×</span>
<span class="cline-any cline-yes">35×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">210×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">210×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">210×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">35×</span>
<span class="cline-any cline-yes">35×</span>
<span class="cline-any cline-yes">35×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">35×</span>
<span class="cline-any cline-yes">35×</span>
<span class="cline-any cline-yes">35×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">35×</span>
<span class="cline-any cline-yes">35×</span>
<span class="cline-any cline-yes">35×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">35×</span>
<span class="cline-any cline-yes">35×</span>
<span class="cline-any cline-yes">35×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">35×</span>
<span class="cline-any cline-yes">35×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">35×</span>
<span class="cline-any cline-yes">35×</span>
<span class="cline-any cline-yes">35×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">210×</span>
<span class="cline-any cline-yes">210×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">import Geometry from '../Geometry';
import Plane from './Plane';
import Matrix4 from '../math/Matrix4';
import Vector3 from '../math/Vector3';
import BoundingBox from '../math/BoundingBox';
import vendor from '../core/vendor';
&nbsp;
var planeMatrix = new Matrix4();
&nbsp;
/**
 * @constructor clay.geometry.Cube
 * @extends clay.Geometry
 * @param {Object} [opt]
 * @param {number} [opt.widthSegments]
 * @param {number} [opt.heightSegments]
 * @param {number} [opt.depthSegments]
 * @param {boolean} [opt.inside]
 */
var Cube = Geometry.extend(
/**@lends clay.geometry.Cube# */
{
    dynamic: false,
    /**
     * @type {number}
     */
    widthSegments: 1,
    /**
     * @type {number}
     */
    heightSegments: 1,
    /**
     * @type {number}
     */
    depthSegments: 1,
    /**
     * @type {boolean}
     */
    inside: false
}, function() {
    this.build();
},
/** @lends clay.geometry.Cube.prototype */
{
    /**
     * Build cube geometry
     */
    build: function() {
&nbsp;
        var planes = {
            'px': createPlane('px', this.depthSegments, this.heightSegments),
            'nx': createPlane('nx', this.depthSegments, this.heightSegments),
            'py': createPlane('py', this.widthSegments, this.depthSegments),
            'ny': createPlane('ny', this.widthSegments, this.depthSegments),
            'pz': createPlane('pz', this.widthSegments, this.heightSegments),
            'nz': createPlane('nz', this.widthSegments, this.heightSegments),
        };
&nbsp;
        var attrList = ['position', 'texcoord0', 'normal'];
        var vertexNumber = 0;
        var faceNumber = 0;
        for (var pos in planes) {
            vertexNumber += planes[pos].vertexCount;
            faceNumber += planes[pos].indices.length;
        }
        for (var k = 0; k &lt; attrList.length; k++) {
            this.attributes[attrList[k]].init(vertexNumber);
        }
        this.indices = new vendor.Uint16Array(faceNumber);
        var faceOffset = 0;
        var vertexOffset = 0;
        for (var pos in planes) {
            var plane = planes[pos];
            for (var k = 0; k &lt; attrList.length; k++) {
                var attrName = attrList[k];
                var attrArray = plane.attributes[attrName].value;
                var attrSize = plane.attributes[attrName].size;
                var isNormal = attrName === 'normal';
                for (var i = 0; i &lt; attrArray.length; i++) {
                    var value = attrArray[i];
                    <span class="missing-if-branch" title="if path not taken" >I</span>if (this.inside &amp;&amp; <span class="branch-1 cbranch-no" title="branch not covered" >isNormal)</span> {
<span class="cstat-no" title="statement not covered" >                        value = -value;</span>
                    }
                    this.attributes[attrName].value[i + attrSize * vertexOffset] = value;
                }
            }
            var len = plane.indices.length;
            for (var i = 0; i &lt; plane.indices.length; i++) {
                this.indices[i + faceOffset] = vertexOffset + plane.indices[this.inside ? (<span class="branch-0 cbranch-no" title="branch not covered" >len - i - 1)</span> : i];
            }
            faceOffset += plane.indices.length;
            vertexOffset += plane.vertexCount;
        }
&nbsp;
        this.boundingBox = new BoundingBox();
        this.boundingBox.max.set(1, 1, 1);
        this.boundingBox.min.set(-1, -1, -1);
    }
});
&nbsp;
function createPlane(pos, widthSegments, heightSegments) {
&nbsp;
    planeMatrix.identity();
&nbsp;
    var plane = new Plane({
        widthSegments: widthSegments,
        heightSegments: heightSegments
    });
&nbsp;
    switch(pos) {
        case 'px':
            Matrix4.translate(planeMatrix, planeMatrix, Vector3.POSITIVE_X);
            Matrix4.rotateY(planeMatrix, planeMatrix, Math.PI / 2);
            break;
        case 'nx':
            Matrix4.translate(planeMatrix, planeMatrix, Vector3.NEGATIVE_X);
            Matrix4.rotateY(planeMatrix, planeMatrix, -Math.PI / 2);
            break;
        case 'py':
            Matrix4.translate(planeMatrix, planeMatrix, Vector3.POSITIVE_Y);
            Matrix4.rotateX(planeMatrix, planeMatrix, -Math.PI / 2);
            break;
        case 'ny':
            Matrix4.translate(planeMatrix, planeMatrix, Vector3.NEGATIVE_Y);
            Matrix4.rotateX(planeMatrix, planeMatrix, Math.PI / 2);
            break;
        case 'pz':
            Matrix4.translate(planeMatrix, planeMatrix, Vector3.POSITIVE_Z);
            break;
        case 'nz':
            Matrix4.translate(planeMatrix, planeMatrix, Vector3.NEGATIVE_Z);
            Matrix4.rotateY(planeMatrix, planeMatrix, Math.PI);
            break;
    }
    plane.applyTransform(planeMatrix);
    return plane;
}
&nbsp;
export default Cube;
&nbsp;</pre></td></tr>
</table></pre>
<div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Fri Feb 23 2018 01:09:23 GMT+0800 (CST)
</div>
</div>
<script src="../../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../../sorter.js"></script>
</body>
</html>
