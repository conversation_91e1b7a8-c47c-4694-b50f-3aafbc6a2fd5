<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for src/canvas/</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../../index.html">all files</a> src/canvas/
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">7.6% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>19/250</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">1.47% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>1/68</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">9.09% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>2/22</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">7.6% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>19/250</span>
      </div>
    </div>
  </div>
  <div class='status-line low'></div>
<div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file high" data-value="Material.js"><a href="Material.js.html">Material.js</a></td>
	<td data-value="100" class="pic high"><div class="chart"><div class="cover-fill cover-full" style="width: 100%;"></div><div class="cover-empty" style="width:0%;"></div></div></td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="1" class="abs high">1/1</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="1" class="abs high">1/1</td>
	</tr>

<tr>
	<td class="file low" data-value="Renderer.js"><a href="Renderer.js.html">Renderer.js</a></td>
	<td data-value="7.23" class="pic low"><div class="chart"><div class="cover-fill" style="width: 7%;"></div><div class="cover-empty" style="width:93%;"></div></div></td>
	<td data-value="7.23" class="pct low">7.23%</td>
	<td data-value="249" class="abs low">18/249</td>
	<td data-value="1.47" class="pct low">1.47%</td>
	<td data-value="68" class="abs low">1/68</td>
	<td data-value="9.09" class="pct low">9.09%</td>
	<td data-value="22" class="abs low">2/22</td>
	<td data-value="7.23" class="pct low">7.23%</td>
	<td data-value="249" class="abs low">18/249</td>
	</tr>

</tbody>
</table>
</div><div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Sun Jan 07 2018 14:10:25 GMT+0800 (CST)
</div>
</div>
<script src="../../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../../sorter.js"></script>
</body>
</html>
