<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for src/light/</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../../index.html">all files</a> src/light/
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">44.58% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>37/83</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">50% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>4/8</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">34.38% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>11/32</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">44.58% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>37/83</span>
      </div>
    </div>
  </div>
  <div class='status-line low'></div>
<div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file high" data-value="Ambient.js"><a href="Ambient.js.html">Ambient.js</a></td>
	<td data-value="100" class="pic high"><div class="chart"><div class="cover-fill cover-full" style="width: 100%;"></div><div class="cover-empty" style="width:0%;"></div></div></td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="4" class="abs high">4/4</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="1" class="abs high">1/1</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="4" class="abs high">4/4</td>
	</tr>

<tr>
	<td class="file high" data-value="AmbientCubemap.js"><a href="AmbientCubemap.js.html">AmbientCubemap.js</a></td>
	<td data-value="93.75" class="pic high"><div class="chart"><div class="cover-fill" style="width: 93%;"></div><div class="cover-empty" style="width:7%;"></div></div></td>
	<td data-value="93.75" class="pct high">93.75%</td>
	<td data-value="16" class="abs high">15/16</td>
	<td data-value="50" class="pct medium">50%</td>
	<td data-value="4" class="abs medium">2/4</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="4" class="abs high">4/4</td>
	<td data-value="93.75" class="pct high">93.75%</td>
	<td data-value="16" class="abs high">15/16</td>
	</tr>

<tr>
	<td class="file low" data-value="AmbientSH.js"><a href="AmbientSH.js.html">AmbientSH.js</a></td>
	<td data-value="11.11" class="pic low"><div class="chart"><div class="cover-fill" style="width: 11%;"></div><div class="cover-empty" style="width:89%;"></div></div></td>
	<td data-value="11.11" class="pct low">11.11%</td>
	<td data-value="9" class="abs low">1/9</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="3" class="abs low">0/3</td>
	<td data-value="11.11" class="pct low">11.11%</td>
	<td data-value="9" class="abs low">1/9</td>
	</tr>

<tr>
	<td class="file medium" data-value="Directional.js"><a href="Directional.js.html">Directional.js</a></td>
	<td data-value="60" class="pic medium"><div class="chart"><div class="cover-fill" style="width: 60%;"></div><div class="cover-empty" style="width:40%;"></div></div></td>
	<td data-value="60" class="pct medium">60%</td>
	<td data-value="10" class="abs medium">6/10</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="2" class="abs high">2/2</td>
	<td data-value="66.67" class="pct medium">66.67%</td>
	<td data-value="3" class="abs medium">2/3</td>
	<td data-value="60" class="pct medium">60%</td>
	<td data-value="10" class="abs medium">6/10</td>
	</tr>

<tr>
	<td class="file medium" data-value="Point.js"><a href="Point.js.html">Point.js</a></td>
	<td data-value="66.67" class="pic medium"><div class="chart"><div class="cover-fill" style="width: 66%;"></div><div class="cover-empty" style="width:34%;"></div></div></td>
	<td data-value="66.67" class="pct medium">66.67%</td>
	<td data-value="9" class="abs medium">6/9</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="75" class="pct medium">75%</td>
	<td data-value="4" class="abs medium">3/4</td>
	<td data-value="66.67" class="pct medium">66.67%</td>
	<td data-value="9" class="abs medium">6/9</td>
	</tr>

<tr>
	<td class="file low" data-value="Sphere.js"><a href="Sphere.js.html">Sphere.js</a></td>
	<td data-value="14.29" class="pic low"><div class="chart"><div class="cover-fill" style="width: 14%;"></div><div class="cover-empty" style="width:86%;"></div></div></td>
	<td data-value="14.29" class="pct low">14.29%</td>
	<td data-value="7" class="abs low">1/7</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="4" class="abs low">0/4</td>
	<td data-value="14.29" class="pct low">14.29%</td>
	<td data-value="7" class="abs low">1/7</td>
	</tr>

<tr>
	<td class="file low" data-value="Spot.js"><a href="Spot.js.html">Spot.js</a></td>
	<td data-value="5.26" class="pic low"><div class="chart"><div class="cover-fill" style="width: 5%;"></div><div class="cover-empty" style="width:95%;"></div></div></td>
	<td data-value="5.26" class="pct low">5.26%</td>
	<td data-value="19" class="abs low">1/19</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="2" class="abs low">0/2</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="8" class="abs low">0/8</td>
	<td data-value="5.26" class="pct low">5.26%</td>
	<td data-value="19" class="abs low">1/19</td>
	</tr>

<tr>
	<td class="file low" data-value="Tube.js"><a href="Tube.js.html">Tube.js</a></td>
	<td data-value="33.33" class="pic low"><div class="chart"><div class="cover-fill" style="width: 33%;"></div><div class="cover-empty" style="width:67%;"></div></div></td>
	<td data-value="33.33" class="pct low">33.33%</td>
	<td data-value="9" class="abs low">3/9</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="20" class="pct low">20%</td>
	<td data-value="5" class="abs low">1/5</td>
	<td data-value="33.33" class="pct low">33.33%</td>
	<td data-value="9" class="abs low">3/9</td>
	</tr>

</tbody>
</table>
</div><div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Fri Feb 23 2018 01:09:23 GMT+0800 (CST)
</div>
</div>
<script src="../../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../../sorter.js"></script>
</body>
</html>
