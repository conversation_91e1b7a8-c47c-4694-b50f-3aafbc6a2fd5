<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for src/animation/</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../../index.html">all files</a> src/animation/
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">25.31% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>207/818</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">20.25% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>81/400</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">18.42% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>21/114</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">25.68% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>207/806</span>
      </div>
    </div>
  </div>
  <div class='status-line low'></div>
<div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file medium" data-value="Animator.js"><a href="Animator.js.html">Animator.js</a></td>
	<td data-value="52.4" class="pic medium"><div class="chart"><div class="cover-fill" style="width: 52%;"></div><div class="cover-empty" style="width:48%;"></div></div></td>
	<td data-value="52.4" class="pct medium">52.4%</td>
	<td data-value="208" class="abs medium">109/208</td>
	<td data-value="39.47" class="pct low">39.47%</td>
	<td data-value="114" class="abs low">45/114</td>
	<td data-value="57.69" class="pct medium">57.69%</td>
	<td data-value="26" class="abs medium">15/26</td>
	<td data-value="52.4" class="pct medium">52.4%</td>
	<td data-value="208" class="abs medium">109/208</td>
	</tr>

<tr>
	<td class="file low" data-value="Blend1DClip.js"><a href="Blend1DClip.js.html">Blend1DClip.js</a></td>
	<td data-value="10.98" class="pic low"><div class="chart"><div class="cover-fill" style="width: 10%;"></div><div class="cover-empty" style="width:90%;"></div></div></td>
	<td data-value="10.98" class="pct low">10.98%</td>
	<td data-value="82" class="abs low">9/82</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="46" class="abs low">0/46</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="7" class="abs low">0/7</td>
	<td data-value="10.98" class="pct low">10.98%</td>
	<td data-value="82" class="abs low">9/82</td>
	</tr>

<tr>
	<td class="file low" data-value="Blend2DClip.js"><a href="Blend2DClip.js.html">Blend2DClip.js</a></td>
	<td data-value="13.64" class="pic low"><div class="chart"><div class="cover-fill" style="width: 13%;"></div><div class="cover-empty" style="width:87%;"></div></div></td>
	<td data-value="13.64" class="pct low">13.64%</td>
	<td data-value="66" class="abs low">9/66</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="30" class="abs low">0/30</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="8" class="abs low">0/8</td>
	<td data-value="13.64" class="pct low">13.64%</td>
	<td data-value="66" class="abs low">9/66</td>
	</tr>

<tr>
	<td class="file medium" data-value="Clip.js"><a href="Clip.js.html">Clip.js</a></td>
	<td data-value="54.88" class="pic medium"><div class="chart"><div class="cover-fill" style="width: 54%;"></div><div class="cover-empty" style="width:46%;"></div></div></td>
	<td data-value="54.88" class="pct medium">54.88%</td>
	<td data-value="82" class="abs medium">45/82</td>
	<td data-value="66.67" class="pct medium">66.67%</td>
	<td data-value="54" class="abs medium">36/54</td>
	<td data-value="35.71" class="pct low">35.71%</td>
	<td data-value="14" class="abs low">5/14</td>
	<td data-value="54.88" class="pct medium">54.88%</td>
	<td data-value="82" class="abs medium">45/82</td>
	</tr>

<tr>
	<td class="file low" data-value="SamplerTrack.js"><a href="SamplerTrack.js.html">SamplerTrack.js</a></td>
	<td data-value="8" class="pic low"><div class="chart"><div class="cover-fill" style="width: 8%;"></div><div class="cover-empty" style="width:92%;"></div></div></td>
	<td data-value="8" class="pct low">8%</td>
	<td data-value="200" class="abs low">16/200</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="82" class="abs low">0/82</td>
	<td data-value="7.14" class="pct low">7.14%</td>
	<td data-value="14" class="abs low">1/14</td>
	<td data-value="8.25" class="pct low">8.25%</td>
	<td data-value="194" class="abs low">16/194</td>
	</tr>

<tr>
	<td class="file high" data-value="Timeline.js"><a href="Timeline.js.html">Timeline.js</a></td>
	<td data-value="100" class="pic high"><div class="chart"><div class="cover-fill cover-full" style="width: 100%;"></div><div class="cover-empty" style="width:0%;"></div></div></td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	</tr>

<tr>
	<td class="file low" data-value="TrackClip.js"><a href="TrackClip.js.html">TrackClip.js</a></td>
	<td data-value="20.69" class="pic low"><div class="chart"><div class="cover-fill" style="width: 20%;"></div><div class="cover-empty" style="width:80%;"></div></div></td>
	<td data-value="20.69" class="pct low">20.69%</td>
	<td data-value="87" class="abs low">18/87</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="18" class="abs low">0/18</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="14" class="abs low">0/14</td>
	<td data-value="20.69" class="pct low">20.69%</td>
	<td data-value="87" class="abs low">18/87</td>
	</tr>

<tr>
	<td class="file low" data-value="easing.js"><a href="easing.js.html">easing.js</a></td>
	<td data-value="1.08" class="pic low"><div class="chart"><div class="cover-fill" style="width: 1%;"></div><div class="cover-empty" style="width:99%;"></div></div></td>
	<td data-value="1.08" class="pct low">1.08%</td>
	<td data-value="93" class="abs low">1/93</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="56" class="abs low">0/56</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="31" class="abs low">0/31</td>
	<td data-value="1.15" class="pct low">1.15%</td>
	<td data-value="87" class="abs low">1/87</td>
	</tr>

</tbody>
</table>
</div><div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Fri Feb 23 2018 01:09:23 GMT+0800 (CST)
</div>
</div>
<script src="../../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../../sorter.js"></script>
</body>
</html>
