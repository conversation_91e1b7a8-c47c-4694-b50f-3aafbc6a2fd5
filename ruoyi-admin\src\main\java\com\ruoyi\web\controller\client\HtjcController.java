//package com.ruoyi.web.controller.client;
//
//import com.alibaba.fastjson2.JSON;
//import com.aliyun.cloudauth20190307.models.DescribeFaceVerifyRequest;
//import com.aliyun.cloudauth20190307.models.DescribeFaceVerifyResponse;
//import com.aliyun.cloudauth20190307.models.InitFaceVerifyRequest;
//import com.aliyun.cloudauth20190307.models.InitFaceVerifyResponse;
//import com.github.pagehelper.Page;
//import com.github.pagehelper.PageInfo;
//import com.ruoyi.common.annotation.Log;
//import com.ruoyi.common.core.domain.AjaxResult;
//import com.ruoyi.common.core.domain.entity.SysUser;
//import com.ruoyi.common.core.domain.model.LoginUser;
//import com.ruoyi.common.enums.BusinessType;
//import com.ruoyi.common.utils.ServletUtils;
//import com.ruoyi.common.utils.StringUtils;
//import com.ruoyi.common.utils.bean.BeanUtils;
//import com.ruoyi.common.utils.poi.ExcelUtil;
//import com.ruoyi.common.utils.uuid.IdUtils;
//import com.ruoyi.system.domain.DataExcel;
//import org.apache.commons.compress.utils.Lists;
//import org.springframework.web.bind.annotation.*;
//import org.springframework.web.context.request.RequestContextHolder;
//import org.springframework.web.context.request.ServletRequestAttributes;
//import org.springframework.web.multipart.MultipartFile;
//
//import java.util.List;
//import java.util.Objects;
//import java.util.function.Supplier;
//
//@RestController
//@RequestMapping("/system/htjc")
//public class HtjcController {
//
//    @PostMapping("/initFaceVerify")
//    public AjaxResult initFaceVerify(@RequestBody InitFaceVerifyRequest request)
//    {
////        InitFaceVerifyRequest request = new InitFaceVerifyRequest();
//        // 请输入场景ID+L。
//        request.setSceneId(1000008633L);
//        // 设置商户请求的唯一标识。
//        request.setOuterOrderNo(IdUtils.fastSimpleUUID());
//        // 认证方案。
//        request.setProductCode("ID_PRO");
//
//        // 模式。
//        request.setModel("LIVENESS");
//        request.setCertType("IDENTITY_CARD");
//        request.setCertName("盛铭");
//        request.setCertNo("330781199912306314");
//        // MetaInfo环境参数。
////        request.setMetaInfo("{\"zimVer\":\"3.0.0\",\"appVersion\": \"1\",\"bioMetaInfo\": \"4.1.0:1150****,0\",\"appName\": \"com.aliyun.antcloudauth\",\"deviceType\": \"ios\",\"osVersion\": \"iOS 10.3.2\",\"apdidToken\": \"\",\"deviceModel\": \"iPhone9,1\"}");
//        //request.setMobile("130xxxxxxxx");
//        //request.setIp("114.xxx.xxx.xxx");
//        //request.setUserId("12345xxxx");
//        //request.setCallbackUrl("https://www.aliyundoc.com");
//        //request.setCallbackToken("xxxxx");
//        // 如需开启个人信息加密传输。
//        //request.setEncryptType("SM2");
//        //request.setCertName("BCRD/7ZkNy7Q*****M1BMBezZe8GaYHrLwyJv558w==");
//        //request.setCertNo("BMjsstxK3S4b1YH*****Pet8ECObfxmLN92SLsNg==");
//
//        // 推荐，支持服务路由。
//        InitFaceVerifyResponse response = InitFaceVerifyUtil.initFaceVerifyAutoRoute(request);
//
//        // 不支持服务自动路由。
//        //InitFaceVerifyResponse response = initFaceVerify("cloudauth.cn-shanghai.aliyuncs.com", request);
//        if (Objects.nonNull(response.getBody().getResultObject())) {
//            return AjaxResult.success(response.getBody().getResultObject().getCertifyId());
//        }
//
//        return AjaxResult.success();
//    }
//
//    @PostMapping("/describeFa3ceVerify")
//    public AjaxResult describeFaceVerify(@RequestBody DescribeFaceVerifyRequest request)
//    {
//        // 请输入场景ID+L。
//        request.setSceneId(1000008633L);
//
//        // 推荐，支持服务路由。
//        DescribeFaceVerifyResponse response = InitFaceVerifyUtil.describeFaceVerifyAutoRoute(request);
//
//        // 不支持服务自动路由。
//        //InitFaceVerifyResponse response = initFaceVerify("cloudauth.cn-shanghai.aliyuncs.com", request);
//        if (Objects.nonNull(response.getBody().getResultObject())) {
//            if ("T".equals(response.getBody().getResultObject().getPassed())) {
//                return AjaxResult.success("验证通过");
//            }
//        }
//
//        return AjaxResult.success("验证失败");
//    }
//
//    @PostMapping("/importData")
//    public AjaxResult importData(MultipartFile file) throws Exception
//    {
//        ExcelUtil<DataExcel> util = new ExcelUtil<DataExcel>(DataExcel.class);
//        List<DataExcel> userList = util.importExcel(file.getInputStream());
//
//        for (DataExcel dataExcel : userList) {
//            String col6 = dataExcel.getCol6();
//            List<List> lists = JSON.parseArray(col6, List.class);
//
//            List<String> strList = Lists.newArrayList();
//
//            for (List list : lists) {
//                String str = (String) list.get(2);
//
//                if (StringUtils.isNotBlank(str)) {
//                    strList.add(str);
//                }
//            }
//
//            dataExcel.setCol9(StringUtils.join(strList, ","));
//        }
//
//        return util.exportExcel(userList, "表数据");
//    }
//}
