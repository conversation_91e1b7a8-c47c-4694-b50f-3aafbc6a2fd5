<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="航班号" prop="flightNumber">
        <el-input
          v-model="queryParams.flightNumber"
          placeholder="请输入航班号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="始发地" prop="origin">
        <el-input
          v-model="queryParams.origin"
          placeholder="请输入始发地"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="目的地" prop="destination">
        <el-input
          v-model="queryParams.destination"
          placeholder="请输入目的地"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="航班状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择航班状态" clearable>
          <el-option label="即将起飞" value="0" />
          <el-option label="飞行中" value="1" />
          <el-option label="已抵达" value="2" />
          <el-option label="延误" value="3" />
          <el-option label="取消" value="4" />
        </el-select>
      </el-form-item>
      <el-form-item label="航班类型" prop="flightType">
        <el-select v-model="queryParams.flightType" placeholder="请选择航班类型" clearable>
          <el-option label="进港" value="0" />
          <el-option label="出港" value="1" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['ky:flight:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['ky:flight:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['ky:flight:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['ky:flight:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="flightList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="航班号" align="center" prop="flightNumber" />
      <el-table-column label="始发地" align="center" prop="origin" />
      <el-table-column label="目的地" align="center" prop="destination" />
      <el-table-column label="起飞时间" align="center" prop="departureTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.departureTime, '{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="预计抵达时间" align="center" prop="arrivalTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.arrivalTime, '{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="航班状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.ky_flight_status" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="航班类型" align="center" prop="flightType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.ky_flight_type" :value="scope.row.flightType"/>
        </template>
      </el-table-column>
      <el-table-column label="重点航班" align="center" prop="isImportant">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_yes_no" :value="scope.row.isImportant"/>
        </template>
      </el-table-column>
      <el-table-column label="航空公司" align="center" prop="airline" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['ky:flight:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['ky:flight:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改航班信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="航班号" prop="flightNumber">
          <el-input v-model="form.flightNumber" placeholder="请输入航班号" />
        </el-form-item>
        <el-form-item label="始发地" prop="origin">
          <el-input v-model="form.origin" placeholder="请输入始发地" />
        </el-form-item>
        <el-form-item label="目的地" prop="destination">
          <el-input v-model="form.destination" placeholder="请输入目的地" />
        </el-form-item>
        <el-form-item label="起飞时间" prop="departureTime">
          <el-date-picker clearable
            v-model="form.departureTime"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="请选择起飞时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="预计抵达时间" prop="arrivalTime">
          <el-date-picker clearable
            v-model="form.arrivalTime"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="请选择预计抵达时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="航班状态" prop="status">
          <el-select v-model="form.status" placeholder="请选择航班状态">
            <el-option label="即将起飞" value="0" />
            <el-option label="飞行中" value="1" />
            <el-option label="已抵达" value="2" />
            <el-option label="延误" value="3" />
            <el-option label="取消" value="4" />
          </el-select>
        </el-form-item>
        <el-form-item label="航班类型" prop="flightType">
          <el-select v-model="form.flightType" placeholder="请选择航班类型">
            <el-option label="进港" value="0" />
            <el-option label="出港" value="1" />
          </el-select>
        </el-form-item>
        <el-form-item label="重点航班" prop="isImportant">
          <el-radio-group v-model="form.isImportant">
            <el-radio label="0">否</el-radio>
            <el-radio label="1">是</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="航空公司" prop="airline">
          <el-input v-model="form.airline" placeholder="请输入航空公司" />
        </el-form-item>
        <el-form-item label="航班机型" prop="aircraftType">
          <el-input v-model="form.aircraftType" placeholder="请输入航班机型" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listFlight, getFlight, delFlight, addFlight, updateFlight } from "@/api/ky/flight";

export default {
  name: "Flight",
  dicts: ['ky_flight_status', 'ky_flight_type', 'sys_yes_no'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 航班信息表格数据
      flightList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        flightNumber: null,
        origin: null,
        destination: null,
        status: null,
        flightType: null,
        isImportant: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        flightNumber: [
          { required: true, message: "航班号不能为空", trigger: "blur" }
        ],
        origin: [
          { required: true, message: "始发地不能为空", trigger: "blur" }
        ],
        destination: [
          { required: true, message: "目的地不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询航班信息列表 */
    getList() {
      this.loading = true;
      listFlight(this.queryParams).then(response => {
        this.flightList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        flightId: null,
        flightNumber: null,
        origin: null,
        destination: null,
        departureTime: null,
        arrivalTime: null,
        status: null,
        flightType: null,
        isImportant: "0",
        longitude: null,
        latitude: null,
        altitude: null,
        speed: null,
        aircraftType: null,
        airline: null,
        gate: null,
        terminal: null,
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.flightId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加航班信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const flightId = row.flightId || this.ids
      getFlight(flightId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改航班信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.flightId != null) {
            updateFlight(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addFlight(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const flightIds = row.flightId || this.ids;
      this.$modal.confirm('是否确认删除航班信息编号为"' + flightIds + '"的数据项？').then(function() {
        return delFlight(flightIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('ky/flight/export', {
        ...this.queryParams
      }, `flight_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
