<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for src/util/sh.js</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../../index.html">all files</a> / <a href="index.html">src/util/</a> sh.js
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">5.49% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>5/91</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>0/28</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>0/3</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">5.49% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>5/91</span>
      </div>
    </div>
  </div>
  <div class='status-line low'></div>
<pre><table class="coverage">
<tr><td class="line-count quiet">1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
116
117
118
119
120
121
122
123
124
125
126
127
128
129
130
131
132
133
134
135
136
137
138
139
140
141
142
143
144
145
146
147
148
149
150
151
152
153
154
155
156
157
158
159
160
161
162
163
164
165
166
167
168
169
170
171
172
173
174
175
176
177
178
179
180
181
182
183
184
185
186
187
188
189
190
191
192
193
194
195
196
197
198
199
200
201
202
203
204
205
206
207
208
209
210
211
212
213
214
215
216
217
218</td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">// Spherical Harmonic Helpers
import Texture from '../Texture';
import FrameBuffer from '../FrameBuffer';
import Texture2D from '../Texture2D';
import Pass from '../compositor/Pass';
import vendor from '../core/vendor';
import Skybox from '../plugin/Skybox';
import Skydome from '../plugin/Skydome';
import EnvironmentMapPass from '../prePass/EnvironmentMap';
import Scene from '../Scene';
import glmatrix from '../dep/glmatrix';
var vec3 = glmatrix.vec3;
var sh = {};
&nbsp;
import projectEnvMapShaderCode from './shader/projectEnvMap.glsl.js';
&nbsp;
var targets = ['px', 'nx', 'py', 'ny', 'pz', 'nz'];
&nbsp;
// Project on gpu, but needs browser to support readPixels as Float32Array.
function projectEnvironmentMapGPU(renderer, envMap) {
    var shTexture = new Texture2D({
        width: 9,
        height: 1,
        type: Texture.FLOAT
    });
    var pass = new Pass({
        fragment: projectEnvMapShaderCode
    });
    pass.material.define('fragment', 'TEXTURE_SIZE', envMap.width);
    pass.setUniform('environmentMap', envMap);
&nbsp;
    var framebuffer = new FrameBuffer();
    framebuffer.attach(shTexture);
    pass.render(renderer, framebuffer);
&nbsp;
    framebuffer.bind(renderer);
    // TODO Only chrome and firefox support Float32Array
    var pixels = new vendor.Float32Array(9 * 4);
    renderer.gl.readPixels(0, 0, 9, 1, Texture.RGBA, Texture.FLOAT, pixels);
&nbsp;
    var coeff = new vendor.Float32Array(9 * 3);
    for (var i = 0; i &lt; 9; i++) {
        coeff[i * 3] = pixels[i * 4];
        coeff[i * 3 + 1] = pixels[i * 4 + 1];
        coeff[i * 3 + 2] = pixels[i * 4 + 2];
    }
    framebuffer.unbind(renderer);
&nbsp;
    framebuffer.dispose(renderer);
    pass.dispose(renderer);
    return coeff;
}
&nbsp;
function harmonics(normal, index)<span class="fstat-no" title="function not covered" >{</span>
    var x = <span class="cstat-no" title="statement not covered" >normal[0];</span>
    var y = <span class="cstat-no" title="statement not covered" >normal[1];</span>
    var z = <span class="cstat-no" title="statement not covered" >normal[2];</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (index === 0) {</span>
<span class="cstat-no" title="statement not covered" >        return 1.0;</span>
    }
    else <span class="cstat-no" title="statement not covered" >if (index === 1) {</span>
<span class="cstat-no" title="statement not covered" >        return x;</span>
    }
    else <span class="cstat-no" title="statement not covered" >if (index === 2) {</span>
<span class="cstat-no" title="statement not covered" >        return y;</span>
    }
    else <span class="cstat-no" title="statement not covered" >if (index === 3) {</span>
<span class="cstat-no" title="statement not covered" >        return z;</span>
    }
    else <span class="cstat-no" title="statement not covered" >if (index === 4) {</span>
<span class="cstat-no" title="statement not covered" >        return x * z;</span>
    }
    else <span class="cstat-no" title="statement not covered" >if (index === 5) {</span>
<span class="cstat-no" title="statement not covered" >        return y * z;</span>
    }
    else <span class="cstat-no" title="statement not covered" >if (index === 6) {</span>
<span class="cstat-no" title="statement not covered" >        return x * y;</span>
    }
    else <span class="cstat-no" title="statement not covered" >if (index === 7) {</span>
<span class="cstat-no" title="statement not covered" >        return 3.0 * z * z - 1.0;</span>
    }
    else {
<span class="cstat-no" title="statement not covered" >        return x * x - y * y;</span>
    }
}
&nbsp;
var normalTransform = {
    px: [2, 1, 0, -1, -1, 1],
    nx: [2, 1, 0, 1, -1, -1],
    py: [0, 2, 1, 1, -1, -1],
    ny: [0, 2, 1, 1, 1, 1],
    pz: [0, 1, 2, -1, -1, -1],
    nz: [0, 1, 2, 1, -1, 1]
};
&nbsp;
// Project on cpu.
function projectEnvironmentMapCPU(renderer, cubePixels, width, height) <span class="fstat-no" title="function not covered" >{</span>
    var coeff = <span class="cstat-no" title="statement not covered" >new vendor.Float32Array(9 * 3);</span>
    var normal = <span class="cstat-no" title="statement not covered" >vec3.create();</span>
    var texel = <span class="cstat-no" title="statement not covered" >vec3.create();</span>
    var fetchNormal = <span class="cstat-no" title="statement not covered" >vec3.create();</span>
<span class="cstat-no" title="statement not covered" >    for (var m = 0; m &lt; 9; m++) {</span>
        var result = <span class="cstat-no" title="statement not covered" >vec3.create();</span>
<span class="cstat-no" title="statement not covered" >        for (var k = 0; k &lt; targets.length; k++) {</span>
            var pixels = <span class="cstat-no" title="statement not covered" >cubePixels[targets[k]];</span>
&nbsp;
            var sideResult = <span class="cstat-no" title="statement not covered" >vec3.create();</span>
            var divider = <span class="cstat-no" title="statement not covered" >0;</span>
            var i = <span class="cstat-no" title="statement not covered" >0;</span>
            var transform = <span class="cstat-no" title="statement not covered" >normalTransform[targets[k]];</span>
<span class="cstat-no" title="statement not covered" >            for (var y = 0; y &lt; height; y++) {</span>
<span class="cstat-no" title="statement not covered" >                for (var x = 0; x &lt; width; x++) {</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >                    normal[0] = x / (width - 1.0) * 2.0 - 1.0;</span>
                    // TODO Flip y?
<span class="cstat-no" title="statement not covered" >                    normal[1] = y / (height - 1.0) * 2.0 - 1.0;</span>
<span class="cstat-no" title="statement not covered" >                    normal[2] = -1.0;</span>
<span class="cstat-no" title="statement not covered" >                    vec3.normalize(normal, normal);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >                    fetchNormal[0] = normal[transform[0]] * transform[3];</span>
<span class="cstat-no" title="statement not covered" >                    fetchNormal[1] = normal[transform[1]] * transform[4];</span>
<span class="cstat-no" title="statement not covered" >                    fetchNormal[2] = normal[transform[2]] * transform[5];</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >                    texel[0] = pixels[i++] / 255;</span>
<span class="cstat-no" title="statement not covered" >                    texel[1] = pixels[i++] / 255;</span>
<span class="cstat-no" title="statement not covered" >                    texel[2] = pixels[i++] / 255;</span>
                    // RGBM Decode
                    var scale = <span class="cstat-no" title="statement not covered" >pixels[i++] / 255 * 51.5;</span>
<span class="cstat-no" title="statement not covered" >                    texel[0] *= scale;</span>
<span class="cstat-no" title="statement not covered" >                    texel[1] *= scale;</span>
<span class="cstat-no" title="statement not covered" >                    texel[2] *= scale;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >                    vec3.scaleAndAdd(sideResult, sideResult, texel, harmonics(fetchNormal, m) * -normal[2]);</span>
                    // -normal.z equals cos(theta) of Lambertian
<span class="cstat-no" title="statement not covered" >                    divider += -normal[2];</span>
                }
            }
<span class="cstat-no" title="statement not covered" >            vec3.scaleAndAdd(result, result, sideResult, 1 / divider);</span>
        }
&nbsp;
<span class="cstat-no" title="statement not covered" >        coeff[m * 3] = result[0] / 6.0;</span>
<span class="cstat-no" title="statement not covered" >        coeff[m * 3 + 1] = result[1] / 6.0;</span>
<span class="cstat-no" title="statement not covered" >        coeff[m * 3 + 2] = result[2] / 6.0;</span>
    }
<span class="cstat-no" title="statement not covered" >    return coeff;</span>
}
&nbsp;
/**
 * @param  {clay.Renderer} renderer
 * @param  {clay.Texture} envMap
 * @param  {Object} [textureOpts]
 * @param  {Object} [textureOpts.lod]
 * @param  {boolean} [textureOpts.decodeRGBM]
 */
sh.projectEnvironmentMap = function (renderer, envMap, opts) <span class="fstat-no" title="function not covered" >{</span>
&nbsp;
    // TODO sRGB
&nbsp;
<span class="cstat-no" title="statement not covered" >    opts = opts || {};</span>
<span class="cstat-no" title="statement not covered" >    opts.lod = opts.lod || 0;</span>
&nbsp;
    var skybox;
    var dummyScene = <span class="cstat-no" title="statement not covered" >new Scene();</span>
    var size = <span class="cstat-no" title="statement not covered" >64;</span>
<span class="cstat-no" title="statement not covered" >    if (envMap.textureType === 'texture2D') {</span>
<span class="cstat-no" title="statement not covered" >        skybox = new Skydome({</span>
            scene: dummyScene,
            environmentMap: envMap
        });
    }
    else {
<span class="cstat-no" title="statement not covered" >        size = (envMap.image &amp;&amp; envMap.image.px) ? envMap.image.px.width : envMap.width;</span>
<span class="cstat-no" title="statement not covered" >        skybox = new Skybox({</span>
            scene: dummyScene,
            environmentMap: envMap
        });
    }
    // Convert to rgbm
    var width = <span class="cstat-no" title="statement not covered" >Math.ceil(size / Math.pow(2, opts.lod));</span>
    var height = <span class="cstat-no" title="statement not covered" >Math.ceil(size / Math.pow(2, opts.lod));</span>
    var rgbmTexture = <span class="cstat-no" title="statement not covered" >new Texture2D({</span>
        width: width,
        height: height
    });
    var framebuffer = <span class="cstat-no" title="statement not covered" >new FrameBuffer();</span>
<span class="cstat-no" title="statement not covered" >    skybox.material.define('fragment', 'RGBM_ENCODE');</span>
<span class="cstat-no" title="statement not covered" >    if (opts.decodeRGBM) {</span>
<span class="cstat-no" title="statement not covered" >        skybox.material.define('fragment', 'RGBM_DECODE');</span>
    }
<span class="cstat-no" title="statement not covered" >    skybox.material.set('lod', opts.lod);</span>
    var envMapPass = <span class="cstat-no" title="statement not covered" >new EnvironmentMapPass({</span>
        texture: rgbmTexture
    });
    var cubePixels = <span class="cstat-no" title="statement not covered" >{};</span>
<span class="cstat-no" title="statement not covered" >    for (var i = 0; i &lt; targets.length; i++) {</span>
<span class="cstat-no" title="statement not covered" >        cubePixels[targets[i]] = new Uint8Array(width * height * 4);</span>
        var camera = <span class="cstat-no" title="statement not covered" >envMapPass.getCamera(targets[i]);</span>
<span class="cstat-no" title="statement not covered" >        camera.fov = 90;</span>
<span class="cstat-no" title="statement not covered" >        framebuffer.attach(rgbmTexture);</span>
<span class="cstat-no" title="statement not covered" >        framebuffer.bind(renderer);</span>
<span class="cstat-no" title="statement not covered" >        renderer.render(dummyScene, camera);</span>
<span class="cstat-no" title="statement not covered" >        renderer.gl.readPixels(</span>
            0, 0, width, height,
            Texture.RGBA, Texture.UNSIGNED_BYTE, cubePixels[targets[i]]
        );
<span class="cstat-no" title="statement not covered" >        framebuffer.unbind(renderer);</span>
    }
&nbsp;
<span class="cstat-no" title="statement not covered" >    skybox.dispose(renderer);</span>
<span class="cstat-no" title="statement not covered" >    framebuffer.dispose(renderer);</span>
<span class="cstat-no" title="statement not covered" >    rgbmTexture.dispose(renderer);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return projectEnvironmentMapCPU(renderer, cubePixels, width, height);</span>
};
&nbsp;
export default sh;
&nbsp;</pre></td></tr>
</table></pre>
<div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Fri Feb 23 2018 01:09:23 GMT+0800 (CST)
</div>
</div>
<script src="../../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../../sorter.js"></script>
</body>
</html>
