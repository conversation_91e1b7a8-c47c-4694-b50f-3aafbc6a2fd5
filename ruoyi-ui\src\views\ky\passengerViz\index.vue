<template>
  <div class="passenger-visualization">
    <!-- 航班选择器 -->
    <div class="flight-selector">
      <el-card>
        <div class="selector-content">
          <el-select 
            v-model="selectedFlightId" 
            placeholder="请选择航班"
            @change="onFlightChange"
            style="width: 300px;"
          >
            <el-option
              v-for="flight in flightList"
              :key="flight.flightId"
              :label="`${flight.flightNumber} (${flight.origin} → ${flight.destination})`"
              :value="flight.flightId"
            >
            </el-option>
          </el-select>
          <el-button type="primary" @click="loadPassengerData" :loading="loading">
            <i class="el-icon-search"></i> 加载乘客数据
          </el-button>
        </div>
      </el-card>
    </div>

    <!-- 乘客概览统计 -->
    <div class="passenger-overview" v-if="passengerList.length > 0">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="overview-item">
              <div class="overview-icon total">
                <i class="el-icon-user"></i>
              </div>
              <div class="overview-content">
                <div class="overview-value">{{ passengerList.length }}</div>
                <div class="overview-label">总乘客数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="overview-item">
              <div class="overview-icon key-person">
                <i class="el-icon-warning"></i>
              </div>
              <div class="overview-content">
                <div class="overview-value">{{ keyPersonCount }}</div>
                <div class="overview-label">重点人员</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="overview-item">
              <div class="overview-icon boarded">
                <i class="el-icon-check"></i>
              </div>
              <div class="overview-content">
                <div class="overview-value">{{ boardedCount }}</div>
                <div class="overview-label">已登机</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="overview-item">
              <div class="overview-icon pending">
                <i class="el-icon-time"></i>
              </div>
              <div class="overview-content">
                <div class="overview-value">{{ pendingCount }}</div>
                <div class="overview-label">待登机</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 乘客可视化区域 -->
    <div class="passenger-visual-area" v-if="passengerList.length > 0">
      <el-card>
        <div slot="header" class="visual-header">
          <span>乘客分布可视化</span>
          <div class="header-controls">
            <el-radio-group v-model="viewMode" @change="onViewModeChange">
              <el-radio-button label="grid">网格视图</el-radio-button>
              <el-radio-button label="list">列表视图</el-radio-button>
              <el-radio-button label="chart">图表视图</el-radio-button>
            </el-radio-group>
            <el-input
              v-model="searchKeyword"
              placeholder="搜索乘客姓名或身份证"
              style="width: 200px; margin-left: 10px;"
              @input="onSearch"
            >
              <i slot="prefix" class="el-input__icon el-icon-search"></i>
            </el-input>
          </div>
        </div>

        <!-- 网格视图 -->
        <div v-if="viewMode === 'grid'" class="grid-view">
          <div class="passenger-grid">
            <div 
              v-for="passenger in filteredPassengers" 
              :key="passenger.passengerId"
              :class="getPassengerCardClass(passenger)"
              @click="showPassengerDetail(passenger)"
            >
              <div class="passenger-avatar">
                <i :class="getPassengerIcon(passenger)"></i>
                <div v-if="passenger.isKeyPerson === '1'" class="key-person-badge">
                  <i class="el-icon-warning"></i>
                </div>
              </div>
              <div class="passenger-info">
                <div class="passenger-name">{{ passenger.passengerName }}</div>
                <div class="passenger-seat">{{ passenger.seatNumber }}</div>
                <div class="passenger-status">
                  <el-tag :type="getStatusTagType(passenger.ticketStatus)" size="mini">
                    {{ getStatusText(passenger.ticketStatus) }}
                  </el-tag>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 列表视图 -->
        <div v-if="viewMode === 'list'" class="list-view">
          <el-table :data="filteredPassengers" style="width: 100%">
            <el-table-column width="60">
              <template slot-scope="scope">
                <div :class="getPassengerIconClass(scope.row)" @click="showPassengerDetail(scope.row)">
                  <i :class="getPassengerIcon(scope.row)"></i>
                  <div v-if="scope.row.isKeyPerson === '1'" class="key-person-indicator"></div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="passengerName" label="姓名" width="120">
              <template slot-scope="scope">
                <span @click="showPassengerDetail(scope.row)" class="clickable-name">
                  {{ scope.row.passengerName }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="seatNumber" label="座位号" width="80"></el-table-column>
            <el-table-column prop="gender" label="性别" width="60">
              <template slot-scope="scope">
                {{ getGenderText(scope.row.gender) }}
              </template>
            </el-table-column>
            <el-table-column prop="age" label="年龄" width="60"></el-table-column>
            <el-table-column prop="cabinClass" label="舱位" width="80">
              <template slot-scope="scope">
                {{ getCabinClassText(scope.row.cabinClass) }}
              </template>
            </el-table-column>
            <el-table-column prop="ticketStatus" label="状态" width="100">
              <template slot-scope="scope">
                <el-tag :type="getStatusTagType(scope.row.ticketStatus)" size="mini">
                  {{ getStatusText(scope.row.ticketStatus) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="isKeyPerson" label="重点人员" width="100">
              <template slot-scope="scope">
                <el-tag v-if="scope.row.isKeyPerson === '1'" type="danger" size="mini">
                  {{ getKeyPersonTypeText(scope.row.keyPersonType) }}
                </el-tag>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column prop="phone" label="联系电话" show-overflow-tooltip></el-table-column>
            <el-table-column label="操作" width="120">
              <template slot-scope="scope">
                <el-button size="mini" @click="showPassengerDetail(scope.row)">详情</el-button>
                <el-button size="mini" @click="showHistoryRecords(scope.row)">历史</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 图表视图 -->
        <div v-if="viewMode === 'chart'" class="chart-view">
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="chart-container">
                <div class="chart-title">舱位分布</div>
                <div id="cabinChart" style="height: 300px;"></div>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="chart-container">
                <div class="chart-title">重点人员分布</div>
                <div id="keyPersonChart" style="height: 300px;"></div>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20" style="margin-top: 20px;">
            <el-col :span="12">
              <div class="chart-container">
                <div class="chart-title">年龄分布</div>
                <div id="ageChart" style="height: 300px;"></div>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="chart-container">
                <div class="chart-title">登机状态</div>
                <div id="statusChart" style="height: 300px;"></div>
              </div>
            </el-col>
          </el-row>
        </div>
      </el-card>
    </div>

    <!-- 乘客详情对话框 -->
    <passenger-detail-modal
      :visible.sync="detailModalVisible"
      :passenger-data="selectedPassenger"
      @close="detailModalVisible = false"
    />

    <!-- 历史记录对话框 -->
    <passenger-history-modal
      :visible.sync="historyModalVisible"
      :passenger-data="selectedPassenger"
      @close="historyModalVisible = false"
    />
  </div>
</template>

<script>
import { getPassengersByFlightId } from "@/api/ky/passenger"
import { listFlight } from "@/api/ky/flight"
import PassengerDetailModal from "./components/PassengerDetailModal"
import PassengerHistoryModal from "./components/PassengerHistoryModal"
import * as echarts from 'echarts'

export default {
  name: "PassengerVisualization",
  components: {
    PassengerDetailModal,
    PassengerHistoryModal
  },
  data() {
    return {
      selectedFlightId: null,
      flightList: [],
      passengerList: [],
      filteredPassengers: [],
      selectedPassenger: null,
      detailModalVisible: false,
      historyModalVisible: false,
      loading: false,
      viewMode: 'grid',
      searchKeyword: '',
      charts: {}
    }
  },
  computed: {
    keyPersonCount() {
      return this.passengerList.filter(p => p.isKeyPerson === '1').length
    },
    boardedCount() {
      return this.passengerList.filter(p => p.ticketStatus === '2' || p.ticketStatus === '3').length
    },
    pendingCount() {
      return this.passengerList.filter(p => p.ticketStatus === '0' || p.ticketStatus === '1').length
    }
  },
  created() {
    this.loadFlightList()
    // 如果有传入的航班ID，直接加载
    if (this.$route.query.flightId) {
      this.selectedFlightId = parseInt(this.$route.query.flightId)
      this.loadPassengerData()
    }
  },
  methods: {
    async loadFlightList() {
      try {
        const response = await listFlight({})
        this.flightList = response.rows || []
      } catch (error) {
        console.error('加载航班列表失败:', error)
        this.$message.error('加载航班列表失败')
      }
    },
    async loadPassengerData() {
      if (!this.selectedFlightId) {
        this.$message.warning('请先选择航班')
        return
      }
      
      this.loading = true
      try {
        const response = await getPassengersByFlightId(this.selectedFlightId)
        this.passengerList = response.data || []
        this.filteredPassengers = [...this.passengerList]
        
        // 如果是图表视图，需要重新渲染图表
        if (this.viewMode === 'chart') {
          this.$nextTick(() => {
            this.renderCharts()
          })
        }
      } catch (error) {
        console.error('加载乘客数据失败:', error)
        this.$message.error('加载乘客数据失败')
      } finally {
        this.loading = false
      }
    },
    onFlightChange() {
      this.passengerList = []
      this.filteredPassengers = []
    },
    onViewModeChange() {
      if (this.viewMode === 'chart' && this.passengerList.length > 0) {
        this.$nextTick(() => {
          this.renderCharts()
        })
      }
    },
    onSearch() {
      if (!this.searchKeyword.trim()) {
        this.filteredPassengers = [...this.passengerList]
        return
      }
      
      const keyword = this.searchKeyword.toLowerCase()
      this.filteredPassengers = this.passengerList.filter(passenger => 
        passenger.passengerName.toLowerCase().includes(keyword) ||
        (passenger.idCard && passenger.idCard.includes(keyword)) ||
        (passenger.seatNumber && passenger.seatNumber.toLowerCase().includes(keyword))
      )
    },
    showPassengerDetail(passenger) {
      this.selectedPassenger = passenger
      this.detailModalVisible = true
    },
    showHistoryRecords(passenger) {
      this.selectedPassenger = passenger
      this.historyModalVisible = true
    },
    getPassengerCardClass(passenger) {
      const classes = ['passenger-card']
      if (passenger.isKeyPerson === '1') {
        classes.push('key-person')
        if (passenger.riskLevel === '3') {
          classes.push('high-risk')
        }
      }
      return classes.join(' ')
    },
    getPassengerIconClass(passenger) {
      const classes = ['passenger-icon-wrapper']
      if (passenger.isKeyPerson === '1') {
        classes.push('key-person-icon')
      }
      return classes.join(' ')
    },
    getPassengerIcon(passenger) {
      if (passenger.gender === '0') {
        return 'el-icon-female'
      } else if (passenger.gender === '1') {
        return 'el-icon-male'
      }
      return 'el-icon-user'
    },
    getGenderText(gender) {
      return gender === '1' ? '男' : gender === '0' ? '女' : '未知'
    },
    getCabinClassText(cabinClass) {
      const classes = {
        '0': '经济舱',
        '1': '商务舱',
        '2': '头等舱'
      }
      return classes[cabinClass] || '未知'
    },
    getStatusText(status) {
      const statuses = {
        '0': '已订票',
        '1': '已值机',
        '2': '已登机',
        '3': '已起飞'
      }
      return statuses[status] || '未知'
    },
    getStatusTagType(status) {
      const types = {
        '0': 'info',
        '1': 'warning',
        '2': 'success',
        '3': 'primary'
      }
      return types[status] || 'info'
    },
    getKeyPersonTypeText(type) {
      const types = {
        '1': '涉恐',
        '2': '涉毒',
        '3': '涉黑',
        '4': '逃犯',
        '5': '精神病',
        '6': '其他危险',
        '7': 'VIP'
      }
      return types[type] || '重点'
    },
    renderCharts() {
      this.renderCabinChart()
      this.renderKeyPersonChart()
      this.renderAgeChart()
      this.renderStatusChart()
    },
    renderCabinChart() {
      const chart = echarts.init(document.getElementById('cabinChart'))
      const cabinData = [
        { name: '经济舱', value: this.passengerList.filter(p => p.cabinClass === '0').length },
        { name: '商务舱', value: this.passengerList.filter(p => p.cabinClass === '1').length },
        { name: '头等舱', value: this.passengerList.filter(p => p.cabinClass === '2').length }
      ]

      const option = {
        tooltip: { trigger: 'item' },
        legend: { orient: 'vertical', left: 'left' },
        series: [{
          name: '舱位分布',
          type: 'pie',
          radius: '50%',
          data: cabinData,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }]
      }
      chart.setOption(option)
      this.charts.cabinChart = chart
    },
    renderKeyPersonChart() {
      const chart = echarts.init(document.getElementById('keyPersonChart'))
      const keyPersonData = [
        { name: '普通乘客', value: this.passengerList.filter(p => p.isKeyPerson === '0').length },
        { name: '重点人员', value: this.passengerList.filter(p => p.isKeyPerson === '1').length }
      ]

      const option = {
        tooltip: { trigger: 'item' },
        legend: { orient: 'vertical', left: 'left' },
        series: [{
          name: '重点人员分布',
          type: 'pie',
          radius: '50%',
          data: keyPersonData,
          itemStyle: {
            color: function(params) {
              return params.name === '重点人员' ? '#f56c6c' : '#67c23a'
            }
          }
        }]
      }
      chart.setOption(option)
      this.charts.keyPersonChart = chart
    },
    renderAgeChart() {
      const chart = echarts.init(document.getElementById('ageChart'))
      const ageGroups = {
        '18-30': 0,
        '31-45': 0,
        '46-60': 0,
        '60+': 0
      }

      this.passengerList.forEach(p => {
        const age = p.age
        if (age <= 30) ageGroups['18-30']++
        else if (age <= 45) ageGroups['31-45']++
        else if (age <= 60) ageGroups['46-60']++
        else ageGroups['60+']++
      })

      const option = {
        tooltip: { trigger: 'axis' },
        xAxis: {
          type: 'category',
          data: Object.keys(ageGroups)
        },
        yAxis: { type: 'value' },
        series: [{
          name: '人数',
          type: 'bar',
          data: Object.values(ageGroups),
          itemStyle: { color: '#409eff' }
        }]
      }
      chart.setOption(option)
      this.charts.ageChart = chart
    },
    renderStatusChart() {
      const chart = echarts.init(document.getElementById('statusChart'))
      const statusData = [
        { name: '已订票', value: this.passengerList.filter(p => p.ticketStatus === '0').length },
        { name: '已值机', value: this.passengerList.filter(p => p.ticketStatus === '1').length },
        { name: '已登机', value: this.passengerList.filter(p => p.ticketStatus === '2').length },
        { name: '已起飞', value: this.passengerList.filter(p => p.ticketStatus === '3').length }
      ]

      const option = {
        tooltip: { trigger: 'item' },
        legend: { orient: 'vertical', left: 'left' },
        series: [{
          name: '登机状态',
          type: 'pie',
          radius: ['40%', '70%'],
          data: statusData,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }]
      }
      chart.setOption(option)
      this.charts.statusChart = chart
    }
  },
  beforeDestroy() {
    // 销毁图表实例
    Object.values(this.charts).forEach(chart => {
      if (chart) {
        chart.dispose()
      }
    })
  }
}
</script>

<style scoped>
.passenger-visualization {
  padding: 20px;
}

.flight-selector {
  margin-bottom: 20px;
}

.selector-content {
  display: flex;
  align-items: center;
  gap: 15px;
}

.passenger-overview {
  margin-bottom: 20px;
}

.overview-card {
  height: 100px;
}

.overview-item {
  display: flex;
  align-items: center;
  height: 100%;
}

.overview-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
  margin-right: 15px;
}

.overview-icon.total {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.overview-icon.key-person {
  background: linear-gradient(135deg, #f093fb, #f5576c);
}

.overview-icon.boarded {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.overview-icon.pending {
  background: linear-gradient(135deg, #43e97b, #38f9d7);
}

.overview-content {
  flex: 1;
}

.overview-value {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.overview-label {
  font-size: 14px;
  color: #909399;
}

.visual-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-controls {
  display: flex;
  align-items: center;
}

.grid-view {
  padding: 20px 0;
}

.passenger-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
}

.passenger-card {
  background: white;
  border: 2px solid #e4e7ed;
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.passenger-card:hover {
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
  transform: translateY(-2px);
}

.passenger-card.key-person {
  border-color: #f56c6c;
  background: linear-gradient(135deg, #fff5f5, #fef0f0);
}

.passenger-card.high-risk {
  border-color: #8b0000;
  background: linear-gradient(135deg, #ffe6e6, #ffcccc);
  animation: pulse-border 2s infinite;
}

@keyframes pulse-border {
  0% {
    border-color: #8b0000;
    box-shadow: 0 0 0 0 rgba(139, 0, 0, 0.4);
  }
  70% {
    border-color: #f56c6c;
    box-shadow: 0 0 0 10px rgba(139, 0, 0, 0);
  }
  100% {
    border-color: #8b0000;
    box-shadow: 0 0 0 0 rgba(139, 0, 0, 0);
  }
}

.passenger-avatar {
  position: relative;
  margin-bottom: 15px;
}

.passenger-avatar i {
  font-size: 48px;
  color: #409eff;
}

.key-person-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  width: 20px;
  height: 20px;
  background: #f56c6c;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
}

.passenger-info {
  text-align: center;
}

.passenger-name {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 8px;
}

.passenger-seat {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.passenger-status {
  margin-top: 8px;
}

.list-view {
  margin-top: 20px;
}

.passenger-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #f5f7fa;
  cursor: pointer;
  position: relative;
  transition: all 0.3s ease;
}

.passenger-icon-wrapper:hover {
  background: #409eff;
  color: white;
}

.passenger-icon-wrapper.key-person-icon {
  background: #f56c6c;
  color: white;
}

.key-person-indicator {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 12px;
  height: 12px;
  background: #f56c6c;
  border-radius: 50%;
  border: 2px solid white;
}

.clickable-name {
  cursor: pointer;
  color: #409eff;
  text-decoration: underline;
}

.clickable-name:hover {
  color: #66b1ff;
}

.chart-view {
  padding: 20px 0;
}

.chart-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chart-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 15px;
  text-align: center;
}
</style>
