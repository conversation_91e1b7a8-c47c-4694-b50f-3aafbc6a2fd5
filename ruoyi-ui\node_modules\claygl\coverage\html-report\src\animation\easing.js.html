<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for src/animation/easing.js</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../../index.html">all files</a> / <a href="index.html">src/animation/</a> easing.js
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">1.08% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>1/93</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>0/56</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>0/31</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">1.15% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>1/87</span>
      </div>
    </div>
  </div>
  <div class='status-line low'></div>
<pre><table class="coverage">
<tr><td class="line-count quiet">1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
116
117
118
119
120
121
122
123
124
125
126
127
128
129
130
131
132
133
134
135
136
137
138
139
140
141
142
143
144
145
146
147
148
149
150
151
152
153
154
155
156
157
158
159
160
161
162
163
164
165
166
167
168
169
170
171
172
173
174
175
176
177
178
179
180
181
182
183
184
185
186
187
188
189
190
191
192
193
194
195
196
197
198
199
200
201
202
203
204
205
206
207
208
209
210
211
212
213
214
215
216
217
218
219
220
221
222
223
224
225
226
227
228
229
230
231
232
233
234
235
236
237
238
239
240
241
242
243
244
245
246
247
248
249
250
251
252
253
254
255
256
257
258
259
260
261
262
263
264
265
266
267
268
269
270
271
272
273
274
275
276
277
278
279
280
281
282
283
284
285
286
287
288
289
290
291
292
293
294
295
296
297
298
299
300
301
302
303
304
305
306
307
308
309
310
311
312
313
314
315
316
317
318
319
320
321
322
323
324
325
326
327
328
329
330
331
332
333
334
335
336
337
338
339
340
341
342
343
344
345
346</td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">// 缓动函数来自 https://github.com/sole/tween.js/blob/master/src/Tween.js
&nbsp;
/**
 * @namespace clay.animation.easing
 */
var easing = {
    /**
     * @alias clay.animation.easing.linear
     * @param {number} k
     * @return {number}
     */
    linear: function(k) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        return k;</span>
    },
    /**
     * @alias clay.animation.easing.quadraticIn
     * @param {number} k
     * @return {number}
     */
    quadraticIn: function(k) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        return k * k;</span>
    },
    /**
     * @alias clay.animation.easing.quadraticOut
     * @param {number} k
     * @return {number}
     */
    quadraticOut: function(k) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        return k * (2 - k);</span>
    },
    /**
     * @alias clay.animation.easing.quadraticInOut
     * @param {number} k
     * @return {number}
     */
    quadraticInOut: function(k) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        if ((k *= 2) &lt; 1) {</span>
<span class="cstat-no" title="statement not covered" >            return 0.5 * k * k;</span>
        }
<span class="cstat-no" title="statement not covered" >        return - 0.5 * (--k * (k - 2) - 1);</span>
    },
    /**
     * @alias clay.animation.easing.cubicIn
     * @param {number} k
     * @return {number}
     */
    cubicIn: function(k) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        return k * k * k;</span>
    },
    /**
     * @alias clay.animation.easing.cubicOut
     * @param {number} k
     * @return {number}
     */
    cubicOut: function(k) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        return --k * k * k + 1;</span>
    },
    /**
     * @alias clay.animation.easing.cubicInOut
     * @param {number} k
     * @return {number}
     */
    cubicInOut: function(k) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        if ((k *= 2) &lt; 1) {</span>
<span class="cstat-no" title="statement not covered" >            return 0.5 * k * k * k;</span>
        }
<span class="cstat-no" title="statement not covered" >        return 0.5 * ((k -= 2) * k * k + 2);</span>
    },
    /**
     * @alias clay.animation.easing.quarticIn
     * @param {number} k
     * @return {number}
     */
    quarticIn: function(k) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        return k * k * k * k;</span>
    },
    /**
     * @alias clay.animation.easing.quarticOut
     * @param {number} k
     * @return {number}
     */
    quarticOut: function(k) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        return 1 - (--k * k * k * k);</span>
    },
    /**
     * @alias clay.animation.easing.quarticInOut
     * @param {number} k
     * @return {number}
     */
    quarticInOut: function(k) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        if ((k *= 2) &lt; 1) {</span>
<span class="cstat-no" title="statement not covered" >            return 0.5 * k * k * k * k;</span>
        }
<span class="cstat-no" title="statement not covered" >        return - 0.5 * ((k -= 2) * k * k * k - 2);</span>
    },
    /**
     * @alias clay.animation.easing.quinticIn
     * @param {number} k
     * @return {number}
     */
    quinticIn: function(k) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        return k * k * k * k * k;</span>
    },
    /**
     * @alias clay.animation.easing.quinticOut
     * @param {number} k
     * @return {number}
     */
    quinticOut: function(k) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        return --k * k * k * k * k + 1;</span>
    },
    /**
     * @alias clay.animation.easing.quinticInOut
     * @param {number} k
     * @return {number}
     */
    quinticInOut: function(k) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        if ((k *= 2) &lt; 1) {</span>
<span class="cstat-no" title="statement not covered" >            return 0.5 * k * k * k * k * k;</span>
        }
<span class="cstat-no" title="statement not covered" >        return 0.5 * ((k -= 2) * k * k * k * k + 2);</span>
    },
    /**
     * @alias clay.animation.easing.sinusoidalIn
     * @param {number} k
     * @return {number}
     */
    sinusoidalIn: function(k) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        return 1 - Math.cos(k * Math.PI / 2);</span>
    },
    /**
     * @alias clay.animation.easing.sinusoidalOut
     * @param {number} k
     * @return {number}
     */
    sinusoidalOut: function(k) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        return Math.sin(k * Math.PI / 2);</span>
    },
    /**
     * @alias clay.animation.easing.sinusoidalInOut
     * @param {number} k
     * @return {number}
     */
    sinusoidalInOut: function(k) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        return 0.5 * (1 - Math.cos(Math.PI * k));</span>
    },
    /**
     * @alias clay.animation.easing.exponentialIn
     * @param {number} k
     * @return {number}
     */
    exponentialIn: function(k) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        return k === 0 ? 0 : Math.pow(1024, k - 1);</span>
    },
    /**
     * @alias clay.animation.easing.exponentialOut
     * @param {number} k
     * @return {number}
     */
    exponentialOut: function(k) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        return k === 1 ? 1 : 1 - Math.pow(2, - 10 * k);</span>
    },
    /**
     * @alias clay.animation.easing.exponentialInOut
     * @param {number} k
     * @return {number}
     */
    exponentialInOut: function(k) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        if (k === 0) {</span>
<span class="cstat-no" title="statement not covered" >            return 0;</span>
        }
<span class="cstat-no" title="statement not covered" >        if (k === 1) {</span>
<span class="cstat-no" title="statement not covered" >            return 1;</span>
        }
<span class="cstat-no" title="statement not covered" >        if ((k *= 2) &lt; 1) {</span>
<span class="cstat-no" title="statement not covered" >            return 0.5 * Math.pow(1024, k - 1);</span>
        }
<span class="cstat-no" title="statement not covered" >        return 0.5 * (- Math.pow(2, - 10 * (k - 1)) + 2);</span>
    },
    /**
     * @alias clay.animation.easing.circularIn
     * @param {number} k
     * @return {number}
     */
    circularIn: function(k) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        return 1 - Math.sqrt(1 - k * k);</span>
    },
    /**
     * @alias clay.animation.easing.circularOut
     * @param {number} k
     * @return {number}
     */
    circularOut: function(k) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        return Math.sqrt(1 - (--k * k));</span>
    },
    /**
     * @alias clay.animation.easing.circularInOut
     * @param {number} k
     * @return {number}
     */
    circularInOut: function(k) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        if ((k *= 2) &lt; 1) {</span>
<span class="cstat-no" title="statement not covered" >            return - 0.5 * (Math.sqrt(1 - k * k) - 1);</span>
        }
<span class="cstat-no" title="statement not covered" >        return 0.5 * (Math.sqrt(1 - (k -= 2) * k) + 1);</span>
    },
    /**
     * @alias clay.animation.easing.elasticIn
     * @param {number} k
     * @return {number}
     */
    elasticIn: function(k) <span class="fstat-no" title="function not covered" >{</span>
        var s, a = <span class="cstat-no" title="statement not covered" >0.1,</span> p = <span class="cstat-no" title="statement not covered" >0.4;</span>
<span class="cstat-no" title="statement not covered" >        if (k === 0) {</span>
<span class="cstat-no" title="statement not covered" >            return 0;</span>
        }
<span class="cstat-no" title="statement not covered" >        if (k === 1) {</span>
<span class="cstat-no" title="statement not covered" >            return 1;</span>
        }
<span class="cstat-no" title="statement not covered" >        if (!a || a &lt; 1) {</span>
<span class="cstat-no" title="statement not covered" >            a = 1; <span class="cstat-no" title="statement not covered" ></span>s = p / 4;</span>
        }else{
<span class="cstat-no" title="statement not covered" >            s = p * Math.asin(1 / a) / (2 * Math.PI);</span>
        }
<span class="cstat-no" title="statement not covered" >        return - (a * Math.pow(2, 10 * (k -= 1)) *</span>
                    Math.sin((k - s) * (2 * Math.PI) / p));
    },
    /**
     * @alias clay.animation.easing.elasticOut
     * @param {number} k
     * @return {number}
     */
    elasticOut: function(k) <span class="fstat-no" title="function not covered" >{</span>
        var s, a = <span class="cstat-no" title="statement not covered" >0.1,</span> p = <span class="cstat-no" title="statement not covered" >0.4;</span>
<span class="cstat-no" title="statement not covered" >        if (k === 0) {</span>
<span class="cstat-no" title="statement not covered" >            return 0;</span>
        }
<span class="cstat-no" title="statement not covered" >        if (k === 1) {</span>
<span class="cstat-no" title="statement not covered" >            return 1;</span>
        }
<span class="cstat-no" title="statement not covered" >        if (!a || a &lt; 1) {</span>
<span class="cstat-no" title="statement not covered" >            a = 1; <span class="cstat-no" title="statement not covered" ></span>s = p / 4;</span>
        }
        else{
<span class="cstat-no" title="statement not covered" >            s = p * Math.asin(1 / a) / (2 * Math.PI);</span>
        }
<span class="cstat-no" title="statement not covered" >        return (a * Math.pow(2, - 10 * k) *</span>
                Math.sin((k - s) * (2 * Math.PI) / p) + 1);
    },
    /**
     * @alias clay.animation.easing.elasticInOut
     * @param {number} k
     * @return {number}
     */
    elasticInOut: function(k) <span class="fstat-no" title="function not covered" >{</span>
        var s, a = <span class="cstat-no" title="statement not covered" >0.1,</span> p = <span class="cstat-no" title="statement not covered" >0.4;</span>
<span class="cstat-no" title="statement not covered" >        if (k === 0) {</span>
<span class="cstat-no" title="statement not covered" >            return 0;</span>
        }
<span class="cstat-no" title="statement not covered" >        if (k === 1) {</span>
<span class="cstat-no" title="statement not covered" >            return 1;</span>
        }
<span class="cstat-no" title="statement not covered" >        if (!a || a &lt; 1) {</span>
<span class="cstat-no" title="statement not covered" >            a = 1; <span class="cstat-no" title="statement not covered" ></span>s = p / 4;</span>
        }
        else{
<span class="cstat-no" title="statement not covered" >            s = p * Math.asin(1 / a) / (2 * Math.PI);</span>
        }
<span class="cstat-no" title="statement not covered" >        if ((k *= 2) &lt; 1) {</span>
<span class="cstat-no" title="statement not covered" >            return - 0.5 * (a * Math.pow(2, 10 * (k -= 1))</span>
                * Math.sin((k - s) * (2 * Math.PI) / p));
        }
<span class="cstat-no" title="statement not covered" >        return a * Math.pow(2, -10 * (k -= 1))</span>
                * Math.sin((k - s) * (2 * Math.PI) / p) * 0.5 + 1;
&nbsp;
    },
    /**
     * @alias clay.animation.easing.backIn
     * @param {number} k
     * @return {number}
     */
    backIn: function(k) <span class="fstat-no" title="function not covered" >{</span>
        var s = <span class="cstat-no" title="statement not covered" >1.70158;</span>
<span class="cstat-no" title="statement not covered" >        return k * k * ((s + 1) * k - s);</span>
    },
    /**
     * @alias clay.animation.easing.backOut
     * @param {number} k
     * @return {number}
     */
    backOut: function(k) <span class="fstat-no" title="function not covered" >{</span>
        var s = <span class="cstat-no" title="statement not covered" >1.70158;</span>
<span class="cstat-no" title="statement not covered" >        return --k * k * ((s + 1) * k + s) + 1;</span>
    },
    /**
     * @alias clay.animation.easing.backInOut
     * @param {number} k
     * @return {number}
     */
    backInOut: function(k) <span class="fstat-no" title="function not covered" >{</span>
        var s = <span class="cstat-no" title="statement not covered" >1.70158 * 1.525;</span>
<span class="cstat-no" title="statement not covered" >        if ((k *= 2) &lt; 1) {</span>
<span class="cstat-no" title="statement not covered" >            return 0.5 * (k * k * ((s + 1) * k - s));</span>
        }
<span class="cstat-no" title="statement not covered" >        return 0.5 * ((k -= 2) * k * ((s + 1) * k + s) + 2);</span>
    },
    /**
     * @alias clay.animation.easing.bounceIn
     * @param {number} k
     * @return {number}
     */
    bounceIn: function(k) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        return 1 - easing.bounceOut(1 - k);</span>
    },
    /**
     * @alias clay.animation.easing.bounceOut
     * @param {number} k
     * @return {number}
     */
    bounceOut: function(k) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        if (k &lt; (1 / 2.75)) {</span>
<span class="cstat-no" title="statement not covered" >            return 7.5625 * k * k;</span>
        }
        else <span class="cstat-no" title="statement not covered" >if (k &lt; (2 / 2.75)) {</span>
<span class="cstat-no" title="statement not covered" >            return 7.5625 * (k -= (1.5 / 2.75)) * k + 0.75;</span>
        } else <span class="cstat-no" title="statement not covered" >if (k &lt; (2.5 / 2.75)) {</span>
<span class="cstat-no" title="statement not covered" >            return 7.5625 * (k -= (2.25 / 2.75)) * k + 0.9375;</span>
        } else {
<span class="cstat-no" title="statement not covered" >            return 7.5625 * (k -= (2.625 / 2.75)) * k + 0.984375;</span>
        }
    },
    /**
     * @alias clay.animation.easing.bounceInOut
     * @param {number} k
     * @return {number}
     */
    bounceInOut: function(k) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        if (k &lt; 0.5) {</span>
<span class="cstat-no" title="statement not covered" >            return easing.bounceIn(k * 2) * 0.5;</span>
        }
<span class="cstat-no" title="statement not covered" >        return easing.bounceOut(k * 2 - 1) * 0.5 + 0.5;</span>
    }
};
&nbsp;
export default easing;
&nbsp;</pre></td></tr>
</table></pre>
<div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Fri Feb 23 2018 01:09:23 GMT+0800 (CST)
</div>
</div>
<script src="../../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../../sorter.js"></script>
</body>
</html>
