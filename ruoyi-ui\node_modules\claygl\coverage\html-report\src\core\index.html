<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for src/core/</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../../index.html">all files</a> src/core/
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">35.17% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>185/526</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">18.79% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>56/298</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">44.59% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>33/74</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">35.65% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>185/519</span>
      </div>
    </div>
  </div>
  <div class='status-line low'></div>
<div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file high" data-value="Base.js"><a href="Base.js.html">Base.js</a></td>
	<td data-value="100" class="pic high"><div class="chart"><div class="cover-fill cover-full" style="width: 100%;"></div><div class="cover-empty" style="width:0%;"></div></div></td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="6" class="abs high">6/6</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="2" class="abs high">2/2</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="6" class="abs high">6/6</td>
	</tr>

<tr>
	<td class="file medium" data-value="Cache.js"><a href="Cache.js.html">Cache.js</a></td>
	<td data-value="72.34" class="pic medium"><div class="chart"><div class="cover-fill" style="width: 72%;"></div><div class="cover-empty" style="width:28%;"></div></div></td>
	<td data-value="72.34" class="pct medium">72.34%</td>
	<td data-value="47" class="abs medium">34/47</td>
	<td data-value="63.64" class="pct medium">63.64%</td>
	<td data-value="22" class="abs medium">14/22</td>
	<td data-value="62.5" class="pct medium">62.5%</td>
	<td data-value="16" class="abs medium">10/16</td>
	<td data-value="72.34" class="pct medium">72.34%</td>
	<td data-value="47" class="abs medium">34/47</td>
	</tr>

<tr>
	<td class="file high" data-value="GLInfo.js"><a href="GLInfo.js.html">GLInfo.js</a></td>
	<td data-value="95.45" class="pic high"><div class="chart"><div class="cover-fill" style="width: 95%;"></div><div class="cover-empty" style="width:5%;"></div></div></td>
	<td data-value="95.45" class="pct high">95.45%</td>
	<td data-value="22" class="abs high">21/22</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="6" class="abs high">6/6</td>
	<td data-value="75" class="pct medium">75%</td>
	<td data-value="4" class="abs medium">3/4</td>
	<td data-value="95.45" class="pct high">95.45%</td>
	<td data-value="22" class="abs high">21/22</td>
	</tr>

<tr>
	<td class="file medium" data-value="LRU.js"><a href="LRU.js.html">LRU.js</a></td>
	<td data-value="67.74" class="pic medium"><div class="chart"><div class="cover-fill" style="width: 67%;"></div><div class="cover-empty" style="width:33%;"></div></div></td>
	<td data-value="67.74" class="pct medium">67.74%</td>
	<td data-value="31" class="abs medium">21/31</td>
	<td data-value="50" class="pct medium">50%</td>
	<td data-value="14" class="abs medium">7/14</td>
	<td data-value="50" class="pct medium">50%</td>
	<td data-value="6" class="abs medium">3/6</td>
	<td data-value="67.74" class="pct medium">67.74%</td>
	<td data-value="31" class="abs medium">21/31</td>
	</tr>

<tr>
	<td class="file low" data-value="LinkedList.js"><a href="LinkedList.js.html">LinkedList.js</a></td>
	<td data-value="36.7" class="pic low"><div class="chart"><div class="cover-fill" style="width: 36%;"></div><div class="cover-empty" style="width:64%;"></div></div></td>
	<td data-value="36.7" class="pct low">36.7%</td>
	<td data-value="109" class="abs low">40/109</td>
	<td data-value="11.76" class="pct low">11.76%</td>
	<td data-value="34" class="abs low">4/34</td>
	<td data-value="37.5" class="pct low">37.5%</td>
	<td data-value="16" class="abs low">6/16</td>
	<td data-value="36.7" class="pct low">36.7%</td>
	<td data-value="109" class="abs low">40/109</td>
	</tr>

<tr>
	<td class="file low" data-value="color.js"><a href="color.js.html">color.js</a></td>
	<td data-value="7.01" class="pic low"><div class="chart"><div class="cover-fill" style="width: 7%;"></div><div class="cover-empty" style="width:93%;"></div></div></td>
	<td data-value="7.01" class="pct low">7.01%</td>
	<td data-value="214" class="abs low">15/214</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="148" class="abs low">0/148</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="15" class="abs low">0/15</td>
	<td data-value="7.25" class="pct low">7.25%</td>
	<td data-value="207" class="abs low">15/207</td>
	</tr>

<tr>
	<td class="file high" data-value="glenum.js"><a href="glenum.js.html">glenum.js</a></td>
	<td data-value="100" class="pic high"><div class="chart"><div class="cover-fill cover-full" style="width: 100%;"></div><div class="cover-empty" style="width:0%;"></div></div></td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="1" class="abs high">1/1</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="1" class="abs high">1/1</td>
	</tr>

<tr>
	<td class="file medium" data-value="request.js"><a href="request.js.html">request.js</a></td>
	<td data-value="58.82" class="pic medium"><div class="chart"><div class="cover-fill" style="width: 58%;"></div><div class="cover-empty" style="width:42%;"></div></div></td>
	<td data-value="58.82" class="pct medium">58.82%</td>
	<td data-value="17" class="abs medium">10/17</td>
	<td data-value="42.86" class="pct low">42.86%</td>
	<td data-value="14" class="abs low">6/14</td>
	<td data-value="66.67" class="pct medium">66.67%</td>
	<td data-value="3" class="abs medium">2/3</td>
	<td data-value="58.82" class="pct medium">58.82%</td>
	<td data-value="17" class="abs medium">10/17</td>
	</tr>

<tr>
	<td class="file low" data-value="util.js"><a href="util.js.html">util.js</a></td>
	<td data-value="37.1" class="pic low"><div class="chart"><div class="cover-fill" style="width: 37%;"></div><div class="cover-empty" style="width:63%;"></div></div></td>
	<td data-value="37.1" class="pct low">37.1%</td>
	<td data-value="62" class="abs low">23/62</td>
	<td data-value="23.81" class="pct low">23.81%</td>
	<td data-value="42" class="abs low">10/42</td>
	<td data-value="63.64" class="pct medium">63.64%</td>
	<td data-value="11" class="abs medium">7/11</td>
	<td data-value="37.1" class="pct low">37.1%</td>
	<td data-value="62" class="abs low">23/62</td>
	</tr>

<tr>
	<td class="file high" data-value="vendor.js"><a href="vendor.js.html">vendor.js</a></td>
	<td data-value="82.35" class="pic high"><div class="chart"><div class="cover-fill" style="width: 82%;"></div><div class="cover-empty" style="width:18%;"></div></div></td>
	<td data-value="82.35" class="pct high">82.35%</td>
	<td data-value="17" class="abs high">14/17</td>
	<td data-value="50" class="pct medium">50%</td>
	<td data-value="18" class="abs medium">9/18</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="1" class="abs low">0/1</td>
	<td data-value="82.35" class="pct high">82.35%</td>
	<td data-value="17" class="abs high">14/17</td>
	</tr>

</tbody>
</table>
</div><div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Fri Feb 23 2018 01:09:23 GMT+0800 (CST)
</div>
</div>
<script src="../../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../../sorter.js"></script>
</body>
</html>
