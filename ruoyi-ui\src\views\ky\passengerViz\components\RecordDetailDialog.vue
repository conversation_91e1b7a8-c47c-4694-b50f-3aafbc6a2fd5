<template>
  <el-dialog
    title="记录详情"
    :visible.sync="dialogVisible"
    width="60%"
    :before-close="handleClose"
  >
    <div class="record-detail-content" v-if="recordData">
      <!-- 记录基本信息 -->
      <el-card class="record-info-card">
        <div slot="header">
          <span>记录基本信息</span>
          <el-tag :type="getRecordTypeTagType(recordData.recordType)" style="float: right;">
            {{ getRecordTypeText(recordData.recordType) }}
          </el-tag>
        </div>
        
        <el-descriptions :column="2" border>
          <el-descriptions-item label="记录ID">
            {{ recordData.historyId }}
          </el-descriptions-item>
          <el-descriptions-item label="航班号">
            {{ recordData.flightNumber }}
          </el-descriptions-item>
          <el-descriptions-item label="航班日期">
            {{ formatDate(recordData.flightDate) }}
          </el-descriptions-item>
          <el-descriptions-item label="座位号">
            {{ recordData.seatNumber }}
          </el-descriptions-item>
          <el-descriptions-item label="起点">
            {{ recordData.origin }}
          </el-descriptions-item>
          <el-descriptions-item label="终点">
            {{ recordData.destination }}
          </el-descriptions-item>
          <el-descriptions-item label="舱位等级">
            {{ getCabinClassText(recordData.cabinClass) }}
          </el-descriptions-item>
          <el-descriptions-item label="记录来源">
            {{ recordData.recordSource || '系统记录' }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 事件详情 -->
      <el-card class="event-detail-card">
        <div slot="header">
          <span>事件详情</span>
        </div>
        
        <div class="event-content">
          <div class="event-description">
            <h4>事件描述</h4>
            <div class="description-text">
              {{ recordData.eventDescription || '无特殊事件' }}
            </div>
          </div>
          
          <div class="handle-result" v-if="recordData.handleResult">
            <h4>处理结果</h4>
            <div class="result-text">
              {{ recordData.handleResult }}
            </div>
          </div>
          
          <div class="risk-assessment" v-if="recordData.riskAssessment">
            <h4>风险评估</h4>
            <div class="assessment-text">
              <el-tag :type="getRiskAssessmentTagType(recordData.riskAssessment)">
                {{ recordData.riskAssessment }}
              </el-tag>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 案件信息 -->
      <el-card class="case-info-card" v-if="recordData.caseNumber || recordData.recordType !== '0'">
        <div slot="header">
          <span>案件信息</span>
        </div>
        
        <el-descriptions :column="2" border>
          <el-descriptions-item label="案件编号" v-if="recordData.caseNumber">
            {{ recordData.caseNumber }}
          </el-descriptions-item>
          <el-descriptions-item label="记录类型">
            <el-tag :type="getRecordTypeTagType(recordData.recordType)">
              {{ getRecordTypeText(recordData.recordType) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="核实状态">
            <el-tag :type="recordData.isVerified === '1' ? 'success' : 'warning'">
              {{ recordData.isVerified === '1' ? '已核实' : '待核实' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="核实人员" v-if="recordData.verifyPerson">
            {{ recordData.verifyPerson }}
          </el-descriptions-item>
          <el-descriptions-item label="核实时间" v-if="recordData.verifyTime">
            {{ formatDateTime(recordData.verifyTime) }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatDateTime(recordData.createTime) }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 相关记录 -->
      <el-card class="related-records-card" v-if="relatedRecords.length > 0">
        <div slot="header">
          <span>相关记录</span>
          <el-button size="mini" style="float: right;" @click="loadRelatedRecords">
            <i class="el-icon-refresh"></i> 刷新
          </el-button>
        </div>
        
        <el-table :data="relatedRecords" style="width: 100%" max-height="300">
          <el-table-column prop="flightDate" label="日期" width="100">
            <template slot-scope="scope">
              {{ formatDate(scope.row.flightDate) }}
            </template>
          </el-table-column>
          <el-table-column prop="flightNumber" label="航班号" width="100"></el-table-column>
          <el-table-column prop="recordType" label="类型" width="100">
            <template slot-scope="scope">
              <el-tag :type="getRecordTypeTagType(scope.row.recordType)" size="mini">
                {{ getRecordTypeText(scope.row.recordType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="eventDescription" label="事件描述" show-overflow-tooltip></el-table-column>
          <el-table-column label="操作" width="80">
            <template slot-scope="scope">
              <el-button size="mini" @click="viewRelatedRecord(scope.row)">查看</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <!-- 操作日志 -->
      <el-card class="operation-log-card">
        <div slot="header">
          <span>操作日志</span>
        </div>
        
        <el-timeline>
          <el-timeline-item
            v-for="log in operationLogs"
            :key="log.id"
            :timestamp="formatDateTime(log.operationTime)"
            placement="top"
          >
            <div class="log-content">
              <div class="log-operator">{{ log.operator }}</div>
              <div class="log-action">{{ log.action }}</div>
              <div class="log-remark" v-if="log.remark">{{ log.remark }}</div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </el-card>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关闭</el-button>
      <el-button type="primary" @click="exportRecord">
        <i class="el-icon-download"></i> 导出记录
      </el-button>
      <el-button 
        v-if="recordData.isVerified === '0'" 
        type="success" 
        @click="verifyRecord"
      >
        <i class="el-icon-check"></i> 核实记录
      </el-button>
    </div>

    <!-- 核实记录对话框 -->
    <el-dialog
      title="核实记录"
      :visible.sync="verifyDialogVisible"
      width="50%"
      append-to-body
    >
      <el-form :model="verifyForm" label-width="100px">
        <el-form-item label="核实结果">
          <el-radio-group v-model="verifyForm.result">
            <el-radio label="confirmed">确认属实</el-radio>
            <el-radio label="false">信息有误</el-radio>
            <el-radio label="pending">需进一步核实</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="核实说明">
          <el-input
            v-model="verifyForm.remark"
            type="textarea"
            :rows="4"
            placeholder="请输入核实说明"
          ></el-input>
        </el-form-item>
      </el-form>
      
      <div slot="footer">
        <el-button @click="verifyDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitVerify">确认核实</el-button>
      </div>
    </el-dialog>
  </el-dialog>
</template>

<script>
import { getHistoryByIdCard, updatePassengerHistory } from "@/api/ky/passengerHistory"

export default {
  name: "RecordDetailDialog",
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    recordData: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      dialogVisible: false,
      relatedRecords: [],
      operationLogs: [],
      verifyDialogVisible: false,
      verifyForm: {
        result: 'confirmed',
        remark: ''
      }
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
      if (val && this.recordData) {
        this.loadRelatedRecords()
        this.loadOperationLogs()
      }
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    }
  },
  methods: {
    async loadRelatedRecords() {
      if (!this.recordData.idCard) return
      
      try {
        const response = await getHistoryByIdCard(this.recordData.idCard)
        const allRecords = response.data || []
        // 排除当前记录，获取相关记录
        this.relatedRecords = allRecords
          .filter(record => record.historyId !== this.recordData.historyId)
          .slice(0, 5) // 只显示最近5条
      } catch (error) {
        console.error('加载相关记录失败:', error)
      }
    },
    loadOperationLogs() {
      // 模拟操作日志数据
      this.operationLogs = [
        {
          id: 1,
          operator: '系统',
          action: '创建记录',
          operationTime: this.recordData.createTime,
          remark: '自动生成历史记录'
        }
      ]
      
      if (this.recordData.verifyTime) {
        this.operationLogs.push({
          id: 2,
          operator: this.recordData.verifyPerson || '未知',
          action: '核实记录',
          operationTime: this.recordData.verifyTime,
          remark: '记录已核实'
        })
      }
    },
    viewRelatedRecord(record) {
      this.$emit('view-record', record)
    },
    verifyRecord() {
      this.verifyDialogVisible = true
    },
    async submitVerify() {
      try {
        const updateData = {
          historyId: this.recordData.historyId,
          isVerified: '1',
          verifyPerson: '当前用户', // 实际应该从用户信息获取
          verifyTime: new Date(),
          remark: this.verifyForm.remark
        }
        
        await updatePassengerHistory(updateData)
        this.$message.success('记录核实成功')
        this.verifyDialogVisible = false
        this.recordData.isVerified = '1'
        this.recordData.verifyPerson = '当前用户'
        this.recordData.verifyTime = new Date()
        this.loadOperationLogs()
      } catch (error) {
        console.error('核实记录失败:', error)
        this.$message.error('核实记录失败')
      }
    },
    exportRecord() {
      this.$message.success('导出功能开发中...')
    },
    handleClose() {
      this.dialogVisible = false
      this.resetData()
    },
    resetData() {
      this.relatedRecords = []
      this.operationLogs = []
      this.verifyForm = {
        result: 'confirmed',
        remark: ''
      }
    },
    getRecordTypeText(type) {
      const types = {
        '0': '正常记录',
        '1': '异常行为',
        '2': '安全事件',
        '3': '违规记录'
      }
      return types[type] || '未知'
    },
    getRecordTypeTagType(type) {
      const types = {
        '0': 'success',
        '1': 'warning',
        '2': 'danger',
        '3': 'danger'
      }
      return types[type] || 'info'
    },
    getCabinClassText(cabinClass) {
      const classes = {
        '0': '经济舱',
        '1': '商务舱',
        '2': '头等舱'
      }
      return classes[cabinClass] || '未知'
    },
    getRiskAssessmentTagType(assessment) {
      if (!assessment) return 'info'
      if (assessment.includes('高')) return 'danger'
      if (assessment.includes('中')) return 'warning'
      return 'success'
    },
    formatDate(date) {
      if (!date) return ''
      return new Date(date).toLocaleDateString()
    },
    formatDateTime(datetime) {
      if (!datetime) return ''
      return new Date(datetime).toLocaleString()
    }
  }
}
</script>

<style scoped>
.record-detail-content {
  max-height: 70vh;
  overflow-y: auto;
}

.record-info-card,
.event-detail-card,
.case-info-card,
.related-records-card,
.operation-log-card {
  margin-bottom: 20px;
}

.event-content {
  padding: 10px 0;
}

.event-description,
.handle-result,
.risk-assessment {
  margin-bottom: 20px;
}

.event-description h4,
.handle-result h4,
.risk-assessment h4 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 16px;
  border-bottom: 1px solid #e4e7ed;
  padding-bottom: 8px;
}

.description-text,
.result-text {
  padding: 15px;
  background: #f5f7fa;
  border-radius: 4px;
  color: #606266;
  line-height: 1.6;
  min-height: 60px;
}

.assessment-text {
  padding: 10px 0;
}

.log-content {
  padding: 10px;
  background: #f5f7fa;
  border-radius: 4px;
  border-left: 4px solid #409eff;
}

.log-operator {
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.log-action {
  color: #606266;
  margin-bottom: 5px;
}

.log-remark {
  color: #909399;
  font-size: 12px;
  font-style: italic;
}

.dialog-footer {
  text-align: right;
}
</style>
