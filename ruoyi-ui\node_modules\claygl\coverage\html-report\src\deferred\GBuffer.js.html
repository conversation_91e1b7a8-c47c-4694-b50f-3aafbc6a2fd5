<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for src/deferred/GBuffer.js</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../../index.html">all files</a> / <a href="index.html">src/deferred/</a> GBuffer.js
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">1.54% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>3/195</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>0/84</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>0/18</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">1.54% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>3/195</span>
      </div>
    </div>
  </div>
  <div class='status-line low'></div>
<pre><table class="coverage">
<tr><td class="line-count quiet">1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
116
117
118
119
120
121
122
123
124
125
126
127
128
129
130
131
132
133
134
135
136
137
138
139
140
141
142
143
144
145
146
147
148
149
150
151
152
153
154
155
156
157
158
159
160
161
162
163
164
165
166
167
168
169
170
171
172
173
174
175
176
177
178
179
180
181
182
183
184
185
186
187
188
189
190
191
192
193
194
195
196
197
198
199
200
201
202
203
204
205
206
207
208
209
210
211
212
213
214
215
216
217
218
219
220
221
222
223
224
225
226
227
228
229
230
231
232
233
234
235
236
237
238
239
240
241
242
243
244
245
246
247
248
249
250
251
252
253
254
255
256
257
258
259
260
261
262
263
264
265
266
267
268
269
270
271
272
273
274
275
276
277
278
279
280
281
282
283
284
285
286
287
288
289
290
291
292
293
294
295
296
297
298
299
300
301
302
303
304
305
306
307
308
309
310
311
312
313
314
315
316
317
318
319
320
321
322
323
324
325
326
327
328
329
330
331
332
333
334
335
336
337
338
339
340
341
342
343
344
345
346
347
348
349
350
351
352
353
354
355
356
357
358
359
360
361
362
363
364
365
366
367
368
369
370
371
372
373
374
375
376
377
378
379
380
381
382
383
384
385
386
387
388
389
390
391
392
393
394
395
396
397
398
399
400
401
402
403
404
405
406
407
408
409
410
411
412
413
414
415
416
417
418
419
420
421
422
423
424
425
426
427
428
429
430
431
432
433
434
435
436
437
438
439
440
441
442
443
444
445
446
447
448
449
450
451
452
453
454
455
456
457
458
459
460
461
462
463
464
465
466
467
468
469
470
471
472
473
474
475
476
477
478
479
480
481
482
483
484
485
486
487
488
489
490
491
492
493
494
495
496
497
498
499
500
501
502
503
504
505
506
507
508
509
510
511
512
513
514
515
516
517
518
519
520
521
522
523
524</td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">import Base from '../core/Base';
import Texture2D from '../Texture2D';
import Texture from '../Texture';
import Material from '../Material';
import FrameBuffer from '../FrameBuffer';
import Shader from '../Shader';
import ForwardRenderer from '../Renderer';
import Pass from '../compositor/Pass';
import Matrix4 from '../math/Matrix4';
&nbsp;
import gbufferEssl from '../shader/source/deferred/gbuffer.glsl.js';
import chunkEssl from '../shader/source/deferred/chunk.glsl.js';
&nbsp;
Shader.import(gbufferEssl);
Shader.import(chunkEssl);
&nbsp;
function createFillCanvas(color) <span class="fstat-no" title="function not covered" >{</span>
    var canvas = <span class="cstat-no" title="statement not covered" >document.createElement('canvas');</span>
<span class="cstat-no" title="statement not covered" >    canvas.width = canvas.height = 1;</span>
    var ctx = <span class="cstat-no" title="statement not covered" >canvas.getContext('2d');</span>
<span class="cstat-no" title="statement not covered" >    ctx.fillStyle = color || '#000';</span>
<span class="cstat-no" title="statement not covered" >    ctx.fillRect(0, 0, 1, 1);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return canvas;</span>
}
&nbsp;
function attachTextureToSlot(renderer, program, symbol, texture, slot) <span class="fstat-no" title="function not covered" >{</span>
    var gl = <span class="cstat-no" title="statement not covered" >renderer.gl;</span>
<span class="cstat-no" title="statement not covered" >    program.setUniform(gl, '1i', symbol, slot);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    gl.activeTexture(gl.TEXTURE0 + slot);</span>
    // Maybe texture is not loaded yet;
<span class="cstat-no" title="statement not covered" >    if (texture.isRenderable()) {</span>
<span class="cstat-no" title="statement not covered" >        texture.bind(renderer);</span>
    }
    else {
        // Bind texture to null
<span class="cstat-no" title="statement not covered" >        texture.unbind(renderer);</span>
    }
}
&nbsp;
// TODO Use globalShader insteadof globalMaterial?
function getBeforeRenderHook1 (gl, defaultNormalMap, defaultRoughnessMap) <span class="fstat-no" title="function not covered" >{</span>
&nbsp;
    var previousNormalMap;
    var previousRougGlossMap;
    var previousRenderable;
&nbsp;
<span class="cstat-no" title="statement not covered" >    return function (renderable, gBufferMat, prevMaterial) <span class="fstat-no" title="function not covered" >{</span></span>
        // Material not change
<span class="cstat-no" title="statement not covered" >        if (previousRenderable &amp;&amp; previousRenderable.material === renderable.material) {</span>
<span class="cstat-no" title="statement not covered" >            return;</span>
        }
&nbsp;
        var standardMaterial = <span class="cstat-no" title="statement not covered" >renderable.material;</span>
        var program = <span class="cstat-no" title="statement not covered" >renderable.__program;</span>
&nbsp;
        var glossiness;
        var roughGlossMap;
        var useRoughnessWorkflow = <span class="cstat-no" title="statement not covered" >standardMaterial.isDefined('fragment', 'USE_ROUGHNESS');</span>
        var doubleSided = <span class="cstat-no" title="statement not covered" >standardMaterial.isDefined('fragment', 'DOUBLE_SIDED');</span>
        var roughGlossChannel;
<span class="cstat-no" title="statement not covered" >        if (useRoughnessWorkflow) {</span>
<span class="cstat-no" title="statement not covered" >            glossiness = 1.0 - standardMaterial.get('roughness');</span>
<span class="cstat-no" title="statement not covered" >            roughGlossMap = standardMaterial.get('roughnessMap');</span>
<span class="cstat-no" title="statement not covered" >            roughGlossChannel = standardMaterial.getDefine('fragment', 'ROUGHNESS_CHANNEL');</span>
        }
        else {
<span class="cstat-no" title="statement not covered" >            glossiness = standardMaterial.get('glossiness');</span>
<span class="cstat-no" title="statement not covered" >            roughGlossMap = standardMaterial.get('glossinessMap');</span>
<span class="cstat-no" title="statement not covered" >            roughGlossChannel = standardMaterial.getDefine('fragment', 'GLOSSINESS_CHANNEL');</span>
        }
        var useRoughGlossMap = <span class="cstat-no" title="statement not covered" >!!roughGlossMap;</span>
&nbsp;
        var normalMap = <span class="cstat-no" title="statement not covered" >standardMaterial.get('normalMap') || defaultNormalMap;</span>
        var uvRepeat = <span class="cstat-no" title="statement not covered" >standardMaterial.get('uvRepeat');</span>
        var uvOffset = <span class="cstat-no" title="statement not covered" >standardMaterial.get('uvOffset');</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        roughGlossMap = roughGlossMap || defaultRoughnessMap;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        if (prevMaterial !== gBufferMat) {</span>
<span class="cstat-no" title="statement not covered" >            gBufferMat.set('glossiness', glossiness);</span>
<span class="cstat-no" title="statement not covered" >            gBufferMat.set('normalMap', normalMap);</span>
<span class="cstat-no" title="statement not covered" >            gBufferMat.set('roughGlossMap', roughGlossMap);</span>
<span class="cstat-no" title="statement not covered" >            gBufferMat.set('useRoughGlossMap', +useRoughGlossMap);</span>
<span class="cstat-no" title="statement not covered" >            gBufferMat.set('useRoughness', +useRoughnessWorkflow);</span>
<span class="cstat-no" title="statement not covered" >            gBufferMat.set('doubleSided', +doubleSided);</span>
<span class="cstat-no" title="statement not covered" >            gBufferMat.set('roughGlossChannel', +roughGlossChannel || 0);</span>
<span class="cstat-no" title="statement not covered" >            gBufferMat.set('uvRepeat', uvRepeat);</span>
<span class="cstat-no" title="statement not covered" >            gBufferMat.set('uvOffset', uvOffset);</span>
        }
        else {
<span class="cstat-no" title="statement not covered" >            program.setUniform(</span>
                gl, '1f', 'glossiness', glossiness
            );
&nbsp;
<span class="cstat-no" title="statement not covered" >            if (previousNormalMap !== normalMap) {</span>
<span class="cstat-no" title="statement not covered" >                attachTextureToSlot(this, program, 'normalMap', normalMap, 0);</span>
            }
<span class="cstat-no" title="statement not covered" >            if (previousRougGlossMap !== roughGlossMap) {</span>
<span class="cstat-no" title="statement not covered" >                attachTextureToSlot(this, program, 'roughGlossMap', roughGlossMap, 1);</span>
            }
<span class="cstat-no" title="statement not covered" >            program.setUniform(gl, '1i', 'useRoughGlossMap', +useRoughGlossMap);</span>
<span class="cstat-no" title="statement not covered" >            program.setUniform(gl, '1i', 'useRoughness', +useRoughnessWorkflow);</span>
<span class="cstat-no" title="statement not covered" >            program.setUniform(gl, '1i', 'doubleSided', +doubleSided);</span>
<span class="cstat-no" title="statement not covered" >            program.setUniform(gl, '1i', 'roughGlossChannel', +roughGlossChannel || 0);</span>
<span class="cstat-no" title="statement not covered" >            if (uvRepeat != null) {</span>
<span class="cstat-no" title="statement not covered" >                program.setUniform(gl, '2f', 'uvRepeat', uvRepeat);</span>
            }
<span class="cstat-no" title="statement not covered" >            if (uvOffset != null) {</span>
<span class="cstat-no" title="statement not covered" >                program.setUniform(gl, '2f', 'uvOffset', uvOffset);</span>
            }
        }
&nbsp;
<span class="cstat-no" title="statement not covered" >        previousNormalMap = normalMap;</span>
<span class="cstat-no" title="statement not covered" >        previousRougGlossMap = roughGlossMap;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        previousRenderable = renderable;</span>
    };
}
&nbsp;
function getBeforeRenderHook2(gl, defaultDiffuseMap, defaultMetalnessMap) <span class="fstat-no" title="function not covered" >{</span>
    var previousDiffuseMap;
    var previousRenderable;
    var previousMetalnessMap;
&nbsp;
<span class="cstat-no" title="statement not covered" >    return function (renderable, gBufferMat, prevMaterial) <span class="fstat-no" title="function not covered" >{</span></span>
        // Material not change
<span class="cstat-no" title="statement not covered" >        if (previousRenderable &amp;&amp; previousRenderable.material === renderable.material) {</span>
<span class="cstat-no" title="statement not covered" >            return;</span>
        }
&nbsp;
        var program = <span class="cstat-no" title="statement not covered" >renderable.__program;</span>
        var standardMaterial = <span class="cstat-no" title="statement not covered" >renderable.material;</span>
&nbsp;
        var color = <span class="cstat-no" title="statement not covered" >standardMaterial.get('color');</span>
        var metalness = <span class="cstat-no" title="statement not covered" >standardMaterial.get('metalness');</span>
&nbsp;
        var diffuseMap = <span class="cstat-no" title="statement not covered" >standardMaterial.get('diffuseMap');</span>
        var metalnessMap = <span class="cstat-no" title="statement not covered" >standardMaterial.get('metalnessMap');</span>
&nbsp;
        var uvRepeat = <span class="cstat-no" title="statement not covered" >standardMaterial.get('uvRepeat');</span>
        var uvOffset = <span class="cstat-no" title="statement not covered" >standardMaterial.get('uvOffset');</span>
&nbsp;
        var useMetalnessMap = <span class="cstat-no" title="statement not covered" >!!metalnessMap;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        diffuseMap = diffuseMap || defaultDiffuseMap;</span>
<span class="cstat-no" title="statement not covered" >        metalnessMap = metalnessMap || defaultMetalnessMap;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        if (prevMaterial !== gBufferMat) {</span>
<span class="cstat-no" title="statement not covered" >            gBufferMat.set('color', color);</span>
<span class="cstat-no" title="statement not covered" >            gBufferMat.set('metalness', metalness);</span>
<span class="cstat-no" title="statement not covered" >            gBufferMat.set('diffuseMap', diffuseMap);</span>
<span class="cstat-no" title="statement not covered" >            gBufferMat.set('metalnessMap', metalnessMap);</span>
<span class="cstat-no" title="statement not covered" >            gBufferMat.set('useMetalnessMap', +useMetalnessMap);</span>
<span class="cstat-no" title="statement not covered" >            gBufferMat.set('uvRepeat', uvRepeat);</span>
<span class="cstat-no" title="statement not covered" >            gBufferMat.set('uvOffset', uvOffset);</span>
            // TODO
<span class="cstat-no" title="statement not covered" >            gBufferMat.set('linear', +standardMaterial.linear || 0);</span>
        }
        else {
<span class="cstat-no" title="statement not covered" >            program.setUniform(gl, '1f', 'metalness', metalness);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >            program.setUniform(gl, '3f', 'color', color);</span>
<span class="cstat-no" title="statement not covered" >            if (previousDiffuseMap !== diffuseMap) {</span>
<span class="cstat-no" title="statement not covered" >                attachTextureToSlot(this, program, 'diffuseMap', diffuseMap, 0);</span>
            }
<span class="cstat-no" title="statement not covered" >            if (previousMetalnessMap !== metalnessMap) {</span>
<span class="cstat-no" title="statement not covered" >                attachTextureToSlot(this, program, 'metalnessMap', metalnessMap, 1);</span>
            }
<span class="cstat-no" title="statement not covered" >            program.setUniform(gl, '1i', 'useMetalnessMap', +useMetalnessMap);</span>
<span class="cstat-no" title="statement not covered" >            program.setUniform(gl, '2f', 'uvRepeat', uvRepeat);</span>
<span class="cstat-no" title="statement not covered" >            program.setUniform(gl, '2f', 'uvOffset', uvOffset);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >            program.setUniform(gl, '1i', 'linear', +standardMaterial.linear || 0);</span>
        }
&nbsp;
<span class="cstat-no" title="statement not covered" >        previousDiffuseMap = diffuseMap;</span>
<span class="cstat-no" title="statement not covered" >        previousMetalnessMap = metalnessMap;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        previousRenderable = renderable;</span>
    };
}
&nbsp;
/**
 * GBuffer is provided for deferred rendering and SSAO, SSR pass.
 * It will do two passes rendering to three target textures. See
 * + {@link clay.deferred.GBuffer#getTargetTexture1}
 * + {@link clay.deferred.GBuffer#getTargetTexture2}
 * + {@link clay.deferred.GBuffer#getTargetTexture3}
 * @constructor
 * @alias clay.deferred.GBuffer
 * @extends clay.core.Base
 */
var GBuffer = Base.extend(function () <span class="fstat-no" title="function not covered" >{</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return {</span>
&nbsp;
        enableTargetTexture1: true,
&nbsp;
        enableTargetTexture2: true,
&nbsp;
        enableTargetTexture3: true,
&nbsp;
        renderTransparent: false,
&nbsp;
        _gBufferRenderList: [],
        // - R: normal.x
        // - G: normal.y
        // - B: normal.z
        // - A: glossiness
        _gBufferTex1: new Texture2D({
            minFilter: Texture.NEAREST,
            magFilter: Texture.NEAREST,
            // PENDING
            type: Texture.HALF_FLOAT
        }),
&nbsp;
        // - R: depth
        _gBufferTex2: new Texture2D({
            minFilter: Texture.NEAREST,
            magFilter: Texture.NEAREST,
            // format: Texture.DEPTH_COMPONENT,
            // type: Texture.UNSIGNED_INT
&nbsp;
            format: Texture.DEPTH_STENCIL,
            type: Texture.UNSIGNED_INT_24_8_WEBGL
        }),
&nbsp;
        // - R: albedo.r
        // - G: albedo.g
        // - B: albedo.b
        // - A: metalness
        _gBufferTex3: new Texture2D({
            minFilter: Texture.NEAREST,
            magFilter: Texture.NEAREST
        }),
&nbsp;
        _defaultNormalMap: new Texture2D({
            image: createFillCanvas('#000')
        }),
        _defaultRoughnessMap: new Texture2D({
            image: createFillCanvas('#fff')
        }),
        _defaultMetalnessMap: new Texture2D({
            image: createFillCanvas('#fff')
        }),
        _defaultDiffuseMap: new Texture2D({
            image: createFillCanvas('#fff')
        }),
&nbsp;
        _frameBuffer: new FrameBuffer(),
&nbsp;
        _gBufferMaterial1: new Material({
            shader: new Shader(
                Shader.source('clay.deferred.gbuffer.vertex'),
                Shader.source('clay.deferred.gbuffer1.fragment')
            ),
            vertexDefines: {
                FIRST_PASS: null
            },
            fragmentDefines: {
                FIRST_PASS: null
            }
        }),
        _gBufferMaterial2: new Material({
            shader: new Shader(
                Shader.source('clay.deferred.gbuffer.vertex'),
                Shader.source('clay.deferred.gbuffer2.fragment')
            )
        }),
&nbsp;
        _debugPass: new Pass({
            fragment: Shader.source('clay.deferred.gbuffer.debug')
        })
    };
}, /** @lends clay.deferred.GBuffer# */{
&nbsp;
    /**
     * Set G Buffer size.
     * @param {number} width
     * @param {number} height
     */
    resize: function (width, height) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        if (this._gBufferTex1.width === width</span>
            &amp;&amp; this._gBufferTex1.height === height
        ) {
<span class="cstat-no" title="statement not covered" >            return;</span>
        }
<span class="cstat-no" title="statement not covered" >        this._gBufferTex1.width = width;</span>
<span class="cstat-no" title="statement not covered" >        this._gBufferTex1.height = height;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        this._gBufferTex2.width = width;</span>
<span class="cstat-no" title="statement not covered" >        this._gBufferTex2.height = height;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        this._gBufferTex3.width = width;</span>
<span class="cstat-no" title="statement not covered" >        this._gBufferTex3.height = height;</span>
    },
&nbsp;
    // TODO is dpr needed?
    setViewport: function (x, y, width, height, dpr) <span class="fstat-no" title="function not covered" >{</span>
        var viewport;
<span class="cstat-no" title="statement not covered" >        if (typeof x === 'object') {</span>
<span class="cstat-no" title="statement not covered" >            viewport = x;</span>
        }
        else {
<span class="cstat-no" title="statement not covered" >            viewport = {</span>
                x: x, y: y,
                width: width, height: height,
                devicePixelRatio: dpr || 1
            };
        }
<span class="cstat-no" title="statement not covered" >        this._frameBuffer.viewport = viewport;</span>
    },
&nbsp;
    getViewport: function () <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        if (this._frameBuffer.viewport) {</span>
<span class="cstat-no" title="statement not covered" >            return this._frameBuffer.viewport;</span>
        }
        else {
<span class="cstat-no" title="statement not covered" >            return {</span>
                x: 0, y: 0,
                width: this._gBufferTex1.width,
                height: this._gBufferTex1.height,
                devicePixelRatio: 1
            };
        }
    },
&nbsp;
    /**
     * Update G Buffer
     * @param {clay.Renderer} renderer
     * @param {clay.Scene} scene
     * @param {clay.camera.Perspective} camera
     */
    update: function (renderer, scene, camera) <span class="fstat-no" title="function not covered" >{</span>
&nbsp;
        var gl = <span class="cstat-no" title="statement not covered" >renderer.gl;</span>
&nbsp;
        var frameBuffer = <span class="cstat-no" title="statement not covered" >this._frameBuffer;</span>
        var viewport = <span class="cstat-no" title="statement not covered" >frameBuffer.viewport;</span>
&nbsp;
        var renderList = <span class="cstat-no" title="statement not covered" >scene.updateRenderList(camera);</span>
&nbsp;
        var opaqueList = <span class="cstat-no" title="statement not covered" >renderList.opaque;</span>
        var transparentList = <span class="cstat-no" title="statement not covered" >renderList.transparent;</span>
&nbsp;
        var offset = <span class="cstat-no" title="statement not covered" >0;</span>
        var gBufferRenderList = <span class="cstat-no" title="statement not covered" >this._gBufferRenderList;</span>
<span class="cstat-no" title="statement not covered" >        for (var i = 0; i &lt; opaqueList.length; i++) {</span>
<span class="cstat-no" title="statement not covered" >            if (!opaqueList[i].ignoreGBuffer) {</span>
<span class="cstat-no" title="statement not covered" >                gBufferRenderList[offset++] = opaqueList[i];</span>
            }
        }
<span class="cstat-no" title="statement not covered" >        if (this.renderTransparent) {</span>
<span class="cstat-no" title="statement not covered" >            for (var i = 0; i &lt; transparentList.length; i++) {</span>
<span class="cstat-no" title="statement not covered" >                if (!transparentList[i].ignoreGBuffer) {</span>
<span class="cstat-no" title="statement not covered" >                    gBufferRenderList[offset++] = transparentList[i];</span>
                }
            }
        }
<span class="cstat-no" title="statement not covered" >        gBufferRenderList.length = offset;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        gl.clearColor(0, 0, 0, 0);</span>
<span class="cstat-no" title="statement not covered" >        gl.depthMask(true);</span>
<span class="cstat-no" title="statement not covered" >        gl.colorMask(true, true, true, true);</span>
<span class="cstat-no" title="statement not covered" >        gl.disable(gl.BLEND);</span>
&nbsp;
        var enableTargetTexture1 = <span class="cstat-no" title="statement not covered" >this.enableTargetTexture1;</span>
        var enableTargetTexture2 = <span class="cstat-no" title="statement not covered" >this.enableTargetTexture2;</span>
        var enableTargetTexture3 = <span class="cstat-no" title="statement not covered" >this.enableTargetTexture3;</span>
<span class="cstat-no" title="statement not covered" >        if (!enableTargetTexture1 &amp;&amp; !enableTargetTexture3) {</span>
<span class="cstat-no" title="statement not covered" >            console.warn('Can\'t disable targetTexture1 targetTexture3 both');</span>
<span class="cstat-no" title="statement not covered" >            enableTargetTexture1 = true;</span>
        }
&nbsp;
<span class="cstat-no" title="statement not covered" >        if (enableTargetTexture2) {</span>
<span class="cstat-no" title="statement not covered" >            frameBuffer.attach(this._gBufferTex2, renderer.gl.DEPTH_STENCIL_ATTACHMENT);</span>
        }
&nbsp;
        // PENDING, scene.boundingBoxLastFrame needs be updated if have shadow
<span class="cstat-no" title="statement not covered" >        renderer.bindSceneRendering(scene);</span>
<span class="cstat-no" title="statement not covered" >        if (enableTargetTexture1) {</span>
            // Pass 1
<span class="cstat-no" title="statement not covered" >            frameBuffer.attach(this._gBufferTex1);</span>
<span class="cstat-no" title="statement not covered" >            frameBuffer.bind(renderer);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >            if (viewport) {</span>
                var dpr = <span class="cstat-no" title="statement not covered" >viewport.devicePixelRatio;</span>
                // use scissor to make sure only clear the viewport
<span class="cstat-no" title="statement not covered" >                gl.enable(gl.SCISSOR_TEST);</span>
<span class="cstat-no" title="statement not covered" >                gl.scissor(viewport.x * dpr, viewport.y * dpr, viewport.width * dpr, viewport.height * dpr);</span>
            }
<span class="cstat-no" title="statement not covered" >            gl.clear(gl.COLOR_BUFFER_BIT | gl.DEPTH_BUFFER_BIT);</span>
<span class="cstat-no" title="statement not covered" >            if (viewport) {</span>
<span class="cstat-no" title="statement not covered" >                gl.disable(gl.SCISSOR_TEST);</span>
            }
            var gBufferMaterial1 = <span class="cstat-no" title="statement not covered" >this._gBufferMaterial1;</span>
            var passConfig = <span class="cstat-no" title="statement not covered" >{</span>
                getMaterial: function () <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >                    return gBufferMaterial1;</span>
                },
                beforeRender: getBeforeRenderHook1(gl, this._defaultNormalMap, this._defaultRoughnessMap),
                sortCompare: renderer.opaqueSortCompare
            };
            // FIXME Use MRT if possible
<span class="cstat-no" title="statement not covered" >            renderer.renderPass(gBufferRenderList, camera, passConfig);</span>
&nbsp;
        }
<span class="cstat-no" title="statement not covered" >        if (enableTargetTexture3) {</span>
&nbsp;
            // Pass 2
<span class="cstat-no" title="statement not covered" >            frameBuffer.attach(this._gBufferTex3);</span>
<span class="cstat-no" title="statement not covered" >            frameBuffer.bind(renderer);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >            if (viewport) {</span>
                var dpr = <span class="cstat-no" title="statement not covered" >viewport.devicePixelRatio;</span>
                // use scissor to make sure only clear the viewport
<span class="cstat-no" title="statement not covered" >                gl.enable(gl.SCISSOR_TEST);</span>
<span class="cstat-no" title="statement not covered" >                gl.scissor(viewport.x * dpr, viewport.y * dpr, viewport.width * dpr, viewport.height * dpr);</span>
            }
<span class="cstat-no" title="statement not covered" >            gl.clear(gl.COLOR_BUFFER_BIT | gl.DEPTH_BUFFER_BIT);</span>
<span class="cstat-no" title="statement not covered" >            if (viewport) {</span>
<span class="cstat-no" title="statement not covered" >                gl.disable(gl.SCISSOR_TEST);</span>
            }
&nbsp;
            var gBufferMaterial2 = <span class="cstat-no" title="statement not covered" >this._gBufferMaterial2;</span>
            var passConfig = <span class="cstat-no" title="statement not covered" >{</span>
                getMaterial: function () <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >                    return gBufferMaterial2;</span>
                },
                beforeRender: getBeforeRenderHook2(gl, this._defaultDiffuseMap, this._defaultMetalnessMap),
                sortCompare: renderer.opaqueSortCompare
            };
<span class="cstat-no" title="statement not covered" >            renderer.renderPass(gBufferRenderList, camera, passConfig);</span>
        }
&nbsp;
<span class="cstat-no" title="statement not covered" >        renderer.bindSceneRendering(null);</span>
<span class="cstat-no" title="statement not covered" >        frameBuffer.unbind(renderer);</span>
    },
&nbsp;
    renderDebug: function (renderer, camera, type, viewport) <span class="fstat-no" title="function not covered" >{</span>
        var debugTypes = <span class="cstat-no" title="statement not covered" >{</span>
            normal: 0,
            depth: 1,
            position: 2,
            glossiness: 3,
            metalness: 4,
            albedo: 5
        };
<span class="cstat-no" title="statement not covered" >        if (debugTypes[type] == null) {</span>
<span class="cstat-no" title="statement not covered" >            console.warn('Unkown type "' + type + '"');</span>
            // Default use normal
<span class="cstat-no" title="statement not covered" >            type = 'normal';</span>
        }
&nbsp;
<span class="cstat-no" title="statement not covered" >        renderer.saveClear();</span>
<span class="cstat-no" title="statement not covered" >        renderer.saveViewport();</span>
<span class="cstat-no" title="statement not covered" >        renderer.clearBit = renderer.gl.DEPTH_BUFFER_BIT;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        if (viewport) {</span>
<span class="cstat-no" title="statement not covered" >            renderer.setViewport(viewport);</span>
        }
        var viewProjectionInv = <span class="cstat-no" title="statement not covered" >new Matrix4();</span>
<span class="cstat-no" title="statement not covered" >        Matrix4.multiply(viewProjectionInv, camera.worldTransform, camera.invProjectionMatrix);</span>
&nbsp;
        var debugPass = <span class="cstat-no" title="statement not covered" >this._debugPass;</span>
<span class="cstat-no" title="statement not covered" >        debugPass.setUniform('viewportSize', [renderer.getWidth(), renderer.getHeight()]);</span>
<span class="cstat-no" title="statement not covered" >        debugPass.setUniform('gBufferTexture1', this._gBufferTex1);</span>
<span class="cstat-no" title="statement not covered" >        debugPass.setUniform('gBufferTexture2', this._gBufferTex2);</span>
<span class="cstat-no" title="statement not covered" >        debugPass.setUniform('gBufferTexture3', this._gBufferTex3);</span>
<span class="cstat-no" title="statement not covered" >        debugPass.setUniform('debug', debugTypes[type]);</span>
<span class="cstat-no" title="statement not covered" >        debugPass.setUniform('viewProjectionInv', viewProjectionInv.array);</span>
<span class="cstat-no" title="statement not covered" >        debugPass.render(renderer);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        renderer.restoreViewport();</span>
<span class="cstat-no" title="statement not covered" >        renderer.restoreClear();</span>
    },
&nbsp;
    /**
     * Get first target texture.
     * Channel storage:
     * + R: normal.x * 0.5 + 0.5
     * + G: normal.y * 0.5 + 0.5
     * + B: normal.z * 0.5 + 0.5
     * + A: glossiness
     * @return {clay.Texture2D}
     */
    getTargetTexture1: function () <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        return this._gBufferTex1;</span>
    },
&nbsp;
    /**
     * Get second target texture.
     * Channel storage:
     * + R: depth
     * @return {clay.Texture2D}
     */
    getTargetTexture2: function () <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        return this._gBufferTex2;</span>
    },
&nbsp;
    /**
     * Get third target texture.
     * Channel storage:
     * + R: albedo.r
     * + G: albedo.g
     * + B: albedo.b
     * + A: metalness
     * @return {clay.Texture2D}
     */
    getTargetTexture3: function () <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        return this._gBufferTex3;</span>
    },
&nbsp;
&nbsp;
    /**
     * @param  {clay.Renderer} renderer
     */
    dispose: function (renderer) <span class="fstat-no" title="function not covered" >{</span>
    }
});
&nbsp;
export default GBuffer;</pre></td></tr>
</table></pre>
<div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Fri Feb 23 2018 01:09:23 GMT+0800 (CST)
</div>
</div>
<script src="../../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../../sorter.js"></script>
</body>
</html>
