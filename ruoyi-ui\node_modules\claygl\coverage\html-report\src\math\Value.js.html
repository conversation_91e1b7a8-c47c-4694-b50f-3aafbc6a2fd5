<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for src/math/Value.js</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../../index.html">all files</a> / <a href="index.html">src/math/</a> Value.js
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">41.51% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>22/53</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>0/6</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">5.88% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>1/17</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">41.51% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>22/53</span>
      </div>
    </div>
  </div>
  <div class='status-line low'></div>
<pre><table class="coverage">
<tr><td class="line-count quiet">1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
116
117
118
119
120
121
122
123
124
125
126
127
128
129
130
131
132
133
134
135
136
137
138
139
140
141
142
143
144</td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">import Vector3 from './Vector3';
import Vector2 from './Vector2';
&nbsp;
/**
 * Random or constant 1d, 2d, 3d vector generator
 * @constructor
 * @alias clay.Value
 */
var Value = function() {};
&nbsp;
/**
 * @function
 * @param {number|clay.Vector2|clay.Vector3} [out]
 * @return {number|clay.Vector2|clay.Vector3}
 */
Value.prototype.get = function(out) <span class="fstat-no" title="function not covered" >{};</span>
&nbsp;
// Constant
var ConstantValue = function(val) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    this.get = function() <span class="fstat-no" title="function not covered" >{</span></span>
<span class="cstat-no" title="statement not covered" >        return val;</span>
    };
};
ConstantValue.prototype = new Value();
ConstantValue.prototype.constructor = ConstantValue;
&nbsp;
// Vector
var VectorValue = function(val) <span class="fstat-no" title="function not covered" >{</span>
    var Constructor = <span class="cstat-no" title="statement not covered" >val.constructor;</span>
<span class="cstat-no" title="statement not covered" >    this.get = function(out) <span class="fstat-no" title="function not covered" >{</span></span>
<span class="cstat-no" title="statement not covered" >        if (!out) {</span>
<span class="cstat-no" title="statement not covered" >            out = new Constructor();</span>
        }
<span class="cstat-no" title="statement not covered" >        out.copy(val);</span>
<span class="cstat-no" title="statement not covered" >        return out;</span>
    };
};
VectorValue.prototype = new Value();
VectorValue.prototype.constructor = VectorValue;
//Random 1D
var Random1D = function(min, max) <span class="fstat-no" title="function not covered" >{</span>
    var range = <span class="cstat-no" title="statement not covered" >max - min;</span>
<span class="cstat-no" title="statement not covered" >    this.get = function() <span class="fstat-no" title="function not covered" >{</span></span>
<span class="cstat-no" title="statement not covered" >        return Math.random() * range + min;</span>
    };
};
Random1D.prototype = new Value();
Random1D.prototype.constructor = Random1D;
&nbsp;
// Random2D
var Random2D = function(min, max) <span class="fstat-no" title="function not covered" >{</span>
    var rangeX = <span class="cstat-no" title="statement not covered" >max.x - min.x;</span>
    var rangeY = <span class="cstat-no" title="statement not covered" >max.y - min.y;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    this.get = function(out) <span class="fstat-no" title="function not covered" >{</span></span>
<span class="cstat-no" title="statement not covered" >        if (!out) {</span>
<span class="cstat-no" title="statement not covered" >            out = new Vector2();</span>
        }
<span class="cstat-no" title="statement not covered" >        Vector2.set(</span>
            out,
            rangeX * Math.random() + min.array[0],
            rangeY * Math.random() + min.array[1]
        );
&nbsp;
<span class="cstat-no" title="statement not covered" >        return out;</span>
    };
};
Random2D.prototype = new Value();
Random2D.prototype.constructor = Random2D;
&nbsp;
var Random3D = function(min, max) <span class="fstat-no" title="function not covered" >{</span>
    var rangeX = <span class="cstat-no" title="statement not covered" >max.x - min.x;</span>
    var rangeY = <span class="cstat-no" title="statement not covered" >max.y - min.y;</span>
    var rangeZ = <span class="cstat-no" title="statement not covered" >max.z - min.z;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    this.get = function(out) <span class="fstat-no" title="function not covered" >{</span></span>
<span class="cstat-no" title="statement not covered" >        if (!out) {</span>
<span class="cstat-no" title="statement not covered" >            out = new Vector3();</span>
        }
<span class="cstat-no" title="statement not covered" >        Vector3.set(</span>
            out,
            rangeX * Math.random() + min.array[0],
            rangeY * Math.random() + min.array[1],
            rangeZ * Math.random() + min.array[2]
        );
&nbsp;
<span class="cstat-no" title="statement not covered" >        return out;</span>
    };
};
Random3D.prototype = new Value();
Random3D.prototype.constructor = Random3D;
&nbsp;
// Factory methods
&nbsp;
/**
 * Create a constant 1d value generator
 * @param  {number} constant
 * @return {clay.Value}
 */
Value.constant = function(constant) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    return new ConstantValue(constant);</span>
};
&nbsp;
/**
 * Create a constant vector value(2d or 3d) generator
 * @param  {clay.Vector2|clay.Vector3} vector
 * @return {clay.Value}
 */
Value.vector = function(vector) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    return new VectorValue(vector);</span>
};
&nbsp;
/**
 * Create a random 1d value generator
 * @param  {number} min
 * @param  {number} max
 * @return {clay.Value}
 */
Value.random1D = function(min, max) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    return new Random1D(min, max);</span>
};
&nbsp;
/**
 * Create a random 2d value generator
 * @param  {clay.Vector2} min
 * @param  {clay.Vector2} max
 * @return {clay.Value}
 */
Value.random2D = function(min, max) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    return new Random2D(min, max);</span>
};
&nbsp;
/**
 * Create a random 3d value generator
 * @param  {clay.Vector3} min
 * @param  {clay.Vector3} max
 * @return {clay.Value}
 */
Value.random3D = function(min, max) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    return new Random3D(min, max);</span>
};
&nbsp;
export default Value;
&nbsp;</pre></td></tr>
</table></pre>
<div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Fri Feb 23 2018 01:09:23 GMT+0800 (CST)
</div>
</div>
<script src="../../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../../sorter.js"></script>
</body>
</html>
