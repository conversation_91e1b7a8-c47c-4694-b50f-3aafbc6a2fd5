import request from '@/utils/request'

// 查询乘客信息列表
export function listPassenger(query) {
  return request({
    url: '/ky/passenger/list',
    method: 'get',
    params: query
  })
}

// 查询乘客信息详细
export function getPassenger(passengerId) {
  return request({
    url: '/ky/passenger/' + passengerId,
    method: 'get'
  })
}

// 根据航班ID查询乘客列表
export function getPassengersByFlightId(flightId) {
  return request({
    url: '/ky/passenger/flight/' + flightId,
    method: 'get'
  })
}

// 根据航班号查询乘客列表
export function getPassengersByFlightNumber(flightNumber) {
  return request({
    url: '/ky/passenger/flightNumber/' + flightNumber,
    method: 'get'
  })
}

// 查询重点人员乘客列表
export function getKeyPersons(query) {
  return request({
    url: '/ky/passenger/keyPersons',
    method: 'get',
    params: query
  })
}

// 根据座位号查询乘客信息
export function getPassengerBySeat(flightId, seatNumber) {
  return request({
    url: '/ky/passenger/seat/' + flightId + '/' + seatNumber,
    method: 'get'
  })
}

// 获取航班舱位分布数据
export function getCabinLayoutData(flightId) {
  return request({
    url: '/ky/passenger/cabin/layout/' + flightId,
    method: 'get'
  })
}

// 获取航班舱位统计信息
export function getCabinStatistics(flightId) {
  return request({
    url: '/ky/passenger/cabin/statistics/' + flightId,
    method: 'get'
  })
}

// 获取重点人员统计信息
export function getKeyPersonStatistics(flightId) {
  return request({
    url: '/ky/passenger/keyPerson/statistics/' + flightId,
    method: 'get'
  })
}

// 根据身份证号查询乘客信息
export function getPassengerByIdCard(idCard) {
  return request({
    url: '/ky/passenger/idCard/' + idCard,
    method: 'get'
  })
}

// 新增乘客信息
export function addPassenger(data) {
  return request({
    url: '/ky/passenger',
    method: 'post',
    data: data
  })
}

// 修改乘客信息
export function updatePassenger(data) {
  return request({
    url: '/ky/passenger',
    method: 'put',
    data: data
  })
}

// 更新乘客票务状态
export function updatePassengerTicketStatus(passengerId, ticketStatus) {
  return request({
    url: '/ky/passenger/ticketStatus/' + passengerId + '/' + ticketStatus,
    method: 'put'
  })
}

// 批量更新航班乘客登机状态
export function batchUpdateTicketStatus(flightId, ticketStatus) {
  return request({
    url: '/ky/passenger/batchTicketStatus/' + flightId + '/' + ticketStatus,
    method: 'put'
  })
}

// 删除乘客信息
export function delPassenger(passengerIds) {
  return request({
    url: '/ky/passenger/' + passengerIds,
    method: 'delete'
  })
}
