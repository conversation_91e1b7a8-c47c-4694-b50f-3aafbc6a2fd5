package com.ruoyi.ky.service.impl;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.ky.mapper.PassengerHistoryMapper;
import com.ruoyi.ky.mapper.PassengerMapper;
import com.ruoyi.ky.domain.PassengerHistory;
import com.ruoyi.ky.domain.Passenger;
import com.ruoyi.ky.service.IPassengerHistoryService;

/**
 * 乘客历史记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class PassengerHistoryServiceImpl implements IPassengerHistoryService 
{
    @Autowired
    private PassengerHistoryMapper passengerHistoryMapper;
    
    @Autowired
    private PassengerMapper passengerMapper;

    /**
     * 查询乘客历史记录
     * 
     * @param historyId 乘客历史记录主键
     * @return 乘客历史记录
     */
    @Override
    public PassengerHistory selectPassengerHistoryByHistoryId(Long historyId)
    {
        return passengerHistoryMapper.selectPassengerHistoryByHistoryId(historyId);
    }

    /**
     * 查询乘客历史记录列表
     * 
     * @param passengerHistory 乘客历史记录
     * @return 乘客历史记录
     */
    @Override
    public List<PassengerHistory> selectPassengerHistoryList(PassengerHistory passengerHistory)
    {
        return passengerHistoryMapper.selectPassengerHistoryList(passengerHistory);
    }

    /**
     * 根据乘客ID查询历史记录
     * 
     * @param passengerId 乘客ID
     * @return 乘客历史记录集合
     */
    @Override
    public List<PassengerHistory> selectHistoryByPassengerId(Long passengerId)
    {
        return passengerHistoryMapper.selectHistoryByPassengerId(passengerId);
    }

    /**
     * 根据身份证号查询历史记录
     * 
     * @param idCard 身份证号
     * @return 乘客历史记录集合
     */
    @Override
    public List<PassengerHistory> selectHistoryByIdCard(String idCard)
    {
        return passengerHistoryMapper.selectHistoryByIdCard(idCard);
    }

    /**
     * 查询异常记录
     * 
     * @param passengerHistory 查询条件
     * @return 异常记录集合
     */
    @Override
    public List<PassengerHistory> selectAbnormalRecords(PassengerHistory passengerHistory)
    {
        return passengerHistoryMapper.selectAbnormalRecords(passengerHistory);
    }

    /**
     * 查询安全事件记录
     * 
     * @param passengerHistory 查询条件
     * @return 安全事件记录集合
     */
    @Override
    public List<PassengerHistory> selectSecurityEvents(PassengerHistory passengerHistory)
    {
        return passengerHistoryMapper.selectSecurityEvents(passengerHistory);
    }

    /**
     * 获取乘客详细信息（包含历史记录统计）
     * 
     * @param idCard 身份证号
     * @return 乘客详细信息
     */
    @Override
    public Map<String, Object> getPassengerDetailInfo(String idCard)
    {
        Map<String, Object> result = new HashMap<>();
        
        // 获取乘客基本信息
        Passenger passenger = passengerMapper.selectPassengerByIdCard(idCard);
        result.put("passenger", passenger);
        
        // 获取历史记录
        List<PassengerHistory> historyList = passengerHistoryMapper.selectHistoryByIdCard(idCard);
        result.put("historyList", historyList);
        
        // 统计信息
        int totalTravelTimes = passengerHistoryMapper.countTravelTimes(idCard);
        int abnormalRecords = passengerHistoryMapper.countAbnormalRecords(idCard);
        
        result.put("totalTravelTimes", totalTravelTimes);
        result.put("abnormalRecords", abnormalRecords);
        
        // 异常记录详情
        PassengerHistory abnormalQuery = new PassengerHistory();
        abnormalQuery.setIdCard(idCard);
        List<PassengerHistory> abnormalList = passengerHistoryMapper.selectAbnormalRecords(abnormalQuery);
        result.put("abnormalList", abnormalList);
        
        // 安全事件记录
        List<PassengerHistory> securityEvents = passengerHistoryMapper.selectSecurityEvents(abnormalQuery);
        result.put("securityEvents", securityEvents);
        
        return result;
    }

    /**
     * 获取乘客风险评估信息
     * 
     * @param idCard 身份证号
     * @return 风险评估信息
     */
    @Override
    public Map<String, Object> getPassengerRiskAssessment(String idCard)
    {
        Map<String, Object> assessment = new HashMap<>();
        
        // 获取基本信息
        Passenger passenger = passengerMapper.selectPassengerByIdCard(idCard);
        if (passenger == null) {
            assessment.put("riskLevel", "1"); // 低风险
            assessment.put("riskScore", 0);
            assessment.put("riskFactors", new String[]{});
            return assessment;
        }
        
        // 计算风险分数
        int riskScore = 0;
        List<String> riskFactors = new java.util.ArrayList<>();
        
        // 是否重点人员
        if ("1".equals(passenger.getIsKeyPerson())) {
            riskScore += 50;
            riskFactors.add("重点人员");
            
            // 重点人员类型加分
            String keyPersonType = passenger.getKeyPersonType();
            if ("1".equals(keyPersonType) || "2".equals(keyPersonType) || "3".equals(keyPersonType) || "4".equals(keyPersonType)) {
                riskScore += 30; // 涉恐、涉毒、涉黑、逃犯
                riskFactors.add("高危人员类型");
            }
        }
        
        // 异常记录次数
        int abnormalCount = passengerHistoryMapper.countAbnormalRecords(idCard);
        if (abnormalCount > 0) {
            riskScore += abnormalCount * 10;
            riskFactors.add("存在异常记录(" + abnormalCount + "次)");
        }
        
        // 出行频率（过于频繁可能有问题）
        int travelTimes = passengerHistoryMapper.countTravelTimes(idCard);
        if (travelTimes > 50) {
            riskScore += 20;
            riskFactors.add("出行频率过高");
        }
        
        // 确定风险等级
        String riskLevel;
        if (riskScore >= 80) {
            riskLevel = "3"; // 高风险
        } else if (riskScore >= 40) {
            riskLevel = "2"; // 中风险
        } else {
            riskLevel = "1"; // 低风险
        }
        
        assessment.put("riskLevel", riskLevel);
        assessment.put("riskScore", riskScore);
        assessment.put("riskFactors", riskFactors);
        assessment.put("totalTravelTimes", travelTimes);
        assessment.put("abnormalRecords", abnormalCount);
        
        return assessment;
    }

    /**
     * 统计乘客出行次数
     * 
     * @param idCard 身份证号
     * @return 出行次数
     */
    @Override
    public int countTravelTimes(String idCard)
    {
        return passengerHistoryMapper.countTravelTimes(idCard);
    }

    /**
     * 统计乘客异常记录次数
     * 
     * @param idCard 身份证号
     * @return 异常记录次数
     */
    @Override
    public int countAbnormalRecords(String idCard)
    {
        return passengerHistoryMapper.countAbnormalRecords(idCard);
    }

    /**
     * 新增乘客历史记录
     * 
     * @param passengerHistory 乘客历史记录
     * @return 结果
     */
    @Override
    public int insertPassengerHistory(PassengerHistory passengerHistory)
    {
        return passengerHistoryMapper.insertPassengerHistory(passengerHistory);
    }

    /**
     * 修改乘客历史记录
     * 
     * @param passengerHistory 乘客历史记录
     * @return 结果
     */
    @Override
    public int updatePassengerHistory(PassengerHistory passengerHistory)
    {
        return passengerHistoryMapper.updatePassengerHistory(passengerHistory);
    }

    /**
     * 批量删除乘客历史记录
     * 
     * @param historyIds 需要删除的乘客历史记录主键
     * @return 结果
     */
    @Override
    public int deletePassengerHistoryByHistoryIds(Long[] historyIds)
    {
        return passengerHistoryMapper.deletePassengerHistoryByHistoryIds(historyIds);
    }

    /**
     * 删除乘客历史记录信息
     * 
     * @param historyId 乘客历史记录主键
     * @return 结果
     */
    @Override
    public int deletePassengerHistoryByHistoryId(Long historyId)
    {
        return passengerHistoryMapper.deletePassengerHistoryByHistoryId(historyId);
    }
}
