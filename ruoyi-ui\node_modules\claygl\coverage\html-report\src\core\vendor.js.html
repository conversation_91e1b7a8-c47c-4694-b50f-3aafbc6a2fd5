<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for src/core/vendor.js</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../../index.html">all files</a> / <a href="index.html">src/core/</a> vendor.js
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">82.35% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>14/17</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">50% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>9/18</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>0/1</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">82.35% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>14/17</span>
      </div>
    </div>
  </div>
  <div class='status-line high'></div>
<pre><table class="coverage">
<tr><td class="line-count quiet">1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38</td><td class="line-coverage quiet"><span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">var supportWebGL = true;
try {
    var canvas = document.createElement('canvas');
    var gl = canvas.getContext('webgl') || <span class="branch-1 cbranch-no" title="branch not covered" >canvas.getContext('experimental-webgl');</span>
    <span class="missing-if-branch" title="if path not taken" >I</span>if (!gl) {
<span class="cstat-no" title="statement not covered" >        throw new Error();</span>
    }
} catch (e) {
<span class="cstat-no" title="statement not covered" >    supportWebGL = false;</span>
}
&nbsp;
var vendor = {};
&nbsp;
/**
 * If support WebGL
 * @return {boolean}
 */
vendor.supportWebGL = function () <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    return supportWebGL;</span>
};
&nbsp;
&nbsp;
vendor.Int8Array = typeof Int8Array == 'undefined' ? <span class="branch-0 cbranch-no" title="branch not covered" >Array </span>: Int8Array;
&nbsp;
vendor.Uint8Array = typeof Uint8Array == 'undefined' ? <span class="branch-0 cbranch-no" title="branch not covered" >Array </span>: Uint8Array;
&nbsp;
vendor.Uint16Array = typeof Uint16Array == 'undefined' ? <span class="branch-0 cbranch-no" title="branch not covered" >Array </span>: Uint16Array;
&nbsp;
vendor.Uint32Array = typeof Uint32Array == 'undefined' ? <span class="branch-0 cbranch-no" title="branch not covered" >Array </span>: Uint32Array;
&nbsp;
vendor.Int16Array = typeof Int16Array == 'undefined' ? <span class="branch-0 cbranch-no" title="branch not covered" >Array </span>: Int16Array;
&nbsp;
vendor.Float32Array = typeof Float32Array == 'undefined' ? <span class="branch-0 cbranch-no" title="branch not covered" >Array </span>: Float32Array;
&nbsp;
vendor.Float64Array = typeof Float64Array == 'undefined' ? <span class="branch-0 cbranch-no" title="branch not covered" >Array </span>: Float64Array;
&nbsp;
export default vendor;
&nbsp;</pre></td></tr>
</table></pre>
<div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Fri Feb 23 2018 01:09:23 GMT+0800 (CST)
</div>
</div>
<script src="../../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../../sorter.js"></script>
</body>
</html>
