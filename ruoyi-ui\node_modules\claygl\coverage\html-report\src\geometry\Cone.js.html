<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for src/geometry/Cone.js</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../../index.html">all files</a> / <a href="index.html">src/geometry/</a> Cone.js
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">100% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>63/63</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">100% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>0/0</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">100% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>2/2</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">100% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>63/63</span>
      </div>
    </div>
  </div>
  <div class='status-line high'></div>
<pre><table class="coverage">
<tr><td class="line-count quiet">1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
116
117
118
119
120
121
122
123
124
125
126
127
128
129
130
131
132
133
134
135
136
137
138
139
140
141
142</td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">10×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">10×</span>
<span class="cline-any cline-yes">10×</span>
<span class="cline-any cline-yes">10×</span>
<span class="cline-any cline-yes">10×</span>
<span class="cline-any cline-yes">10×</span>
<span class="cline-any cline-yes">10×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">10×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">10×</span>
<span class="cline-any cline-yes">10×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">10×</span>
<span class="cline-any cline-yes">10×</span>
<span class="cline-any cline-yes">10×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">10×</span>
<span class="cline-any cline-yes">10×</span>
<span class="cline-any cline-yes">10×</span>
<span class="cline-any cline-yes">350×</span>
<span class="cline-any cline-yes">350×</span>
<span class="cline-any cline-yes">350×</span>
<span class="cline-any cline-yes">350×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">350×</span>
<span class="cline-any cline-yes">350×</span>
<span class="cline-any cline-yes">350×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">10×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">10×</span>
<span class="cline-any cline-yes">10×</span>
<span class="cline-any cline-yes">10×</span>
<span class="cline-any cline-yes">350×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">350×</span>
<span class="cline-any cline-yes">350×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">10×</span>
<span class="cline-any cline-yes">10×</span>
<span class="cline-any cline-yes">10×</span>
<span class="cline-any cline-yes">10×</span>
<span class="cline-any cline-yes">350×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">350×</span>
<span class="cline-any cline-yes">350×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">10×</span>
<span class="cline-any cline-yes">10×</span>
<span class="cline-any cline-yes">10×</span>
<span class="cline-any cline-yes">350×</span>
<span class="cline-any cline-yes">770×</span>
<span class="cline-any cline-yes">770×</span>
<span class="cline-any cline-yes">770×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">10×</span>
<span class="cline-any cline-yes">350×</span>
<span class="cline-any cline-yes">420×</span>
<span class="cline-any cline-yes">420×</span>
<span class="cline-any cline-yes">420×</span>
<span class="cline-any cline-yes">420×</span>
<span class="cline-any cline-yes">420×</span>
<span class="cline-any cline-yes">420×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">10×</span>
<span class="cline-any cline-yes">10×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">10×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">10×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">10×</span>
<span class="cline-any cline-yes">10×</span>
<span class="cline-any cline-yes">10×</span>
<span class="cline-any cline-yes">10×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">import Geometry from '../Geometry';
import BoundingBox from '../math/BoundingBox';
import glMatrix from '../dep/glmatrix';
var vec3 = glMatrix.vec3;
var vec2 = glMatrix.vec2;
&nbsp;
/**
 * @constructor clay.geometry.Cone
 * @extends clay.Geometry
 * @param {Object} [opt]
 * @param {number} [opt.topRadius]
 * @param {number} [opt.bottomRadius]
 * @param {number} [opt.height]
 * @param {number} [opt.capSegments]
 * @param {number} [opt.heightSegments]
 */
var Cone = Geometry.extend(
/** @lends clay.geometry.Cone# */
{
    dynamic: false,
    /**
     * @type {number}
     */
    topRadius: 0,
&nbsp;
    /**
     * @type {number}
     */
    bottomRadius: 1,
&nbsp;
    /**
     * @type {number}
     */
    height: 2,
&nbsp;
    /**
     * @type {number}
     */
    capSegments: 20,
&nbsp;
    /**
     * @type {number}
     */
    heightSegments: 1
}, function() {
    this.build();
},
/** @lends clay.geometry.Cone.prototype */
{
    /**
     * Build cone geometry
     */
    build: function() {
        var positions = [];
        var texcoords = [];
        var faces = [];
        positions.length = 0;
        texcoords.length = 0;
        faces.length = 0;
        // Top cap
        var capSegRadial = Math.PI * 2 / this.capSegments;
&nbsp;
        var topCap = [];
        var bottomCap = [];
&nbsp;
        var r1 = this.topRadius;
        var r2 = this.bottomRadius;
        var y = this.height / 2;
&nbsp;
        var c1 = vec3.fromValues(0, y, 0);
        var c2 = vec3.fromValues(0, -y, 0);
        for (var i = 0; i &lt; this.capSegments; i++) {
            var theta = i * capSegRadial;
            var x = r1 * Math.sin(theta);
            var z = r1 * Math.cos(theta);
            topCap.push(vec3.fromValues(x, y, z));
&nbsp;
            x = r2 * Math.sin(theta);
            z = r2 * Math.cos(theta);
            bottomCap.push(vec3.fromValues(x, -y, z));
        }
&nbsp;
        // Build top cap
        positions.push(c1);
        // FIXME
        texcoords.push(vec2.fromValues(0, 1));
        var n = this.capSegments;
        for (var i = 0; i &lt; n; i++) {
            positions.push(topCap[i]);
            // FIXME
            texcoords.push(vec2.fromValues(i / n, 0));
            faces.push([0, i+1, (i+1) % n + 1]);
        }
&nbsp;
        // Build bottom cap
        var offset = positions.length;
        positions.push(c2);
        texcoords.push(vec2.fromValues(0, 1));
        for (var i = 0; i &lt; n; i++) {
            positions.push(bottomCap[i]);
            // FIXME
            texcoords.push(vec2.fromValues(i / n, 0));
            faces.push([offset, offset+((i+1) % n + 1), offset+i+1]);
        }
&nbsp;
        // Build side
        offset = positions.length;
        var n2 = this.heightSegments;
        for (var i = 0; i &lt; n; i++) {
            for (var j = 0; j &lt; n2+1; j++) {
                var v = j / n2;
                positions.push(vec3.lerp(vec3.create(), topCap[i], bottomCap[i], v));
                texcoords.push(vec2.fromValues(i / n, v));
            }
        }
        for (var i = 0; i &lt; n; i++) {
            for (var j = 0; j &lt; n2; j++) {
                var i1 = i * (n2 + 1) + j;
                var i2 = ((i + 1) % n) * (n2 + 1) + j;
                var i3 = ((i + 1) % n) * (n2 + 1) + j + 1;
                var i4 = i * (n2 + 1) + j + 1;
                faces.push([offset+i2, offset+i1, offset+i4]);
                faces.push([offset+i4, offset+i3, offset+i2]);
            }
        }
&nbsp;
        this.attributes.position.fromArray(positions);
        this.attributes.texcoord0.fromArray(texcoords);
&nbsp;
        this.initIndicesFromArray(faces);
&nbsp;
        this.generateVertexNormals();
&nbsp;
        this.boundingBox = new BoundingBox();
        var r = Math.max(this.topRadius, this.bottomRadius);
        this.boundingBox.min.set(-r, -this.height/2, -r);
        this.boundingBox.max.set(r, this.height/2, r);
    }
});
&nbsp;
export default Cone;
&nbsp;</pre></td></tr>
</table></pre>
<div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Fri Feb 23 2018 01:09:23 GMT+0800 (CST)
</div>
</div>
<script src="../../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../../sorter.js"></script>
</body>
</html>
