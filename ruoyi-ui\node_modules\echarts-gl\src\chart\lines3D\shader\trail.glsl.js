export default "@export ecgl.trail.vertex\n\nuniform mat4 worldViewProjection : WORLDVIEWPROJECTION;\nuniform float percent : 0.0;\nuniform float trailLength: 0.3;\n\nuniform sampler2D pointsTexture;\nuniform float textureWidth : 1024;\n\nuniform vec4 viewport : VIEWPORT;\nuniform float near : NEAR;\n\nattribute vec2 uv;\nattribute vec4 color : COLOR;\n\nattribute float start;\nattribute float prevT;\nattribute float currT;\nattribute float nextT;\nattribute float offset;\n\nvarying vec4 v_Color;\n\n@import ecgl.lines3D.clipNear\n\nvec3 getPointAt(in float off, in vec3 p0, in vec3 p1, in vec3 p2, in vec3 p3) {\n float t = max(min(mod(start + percent, (1.0 + trailLength)) + off, 1.0), 0.0);\n float onet = 1.0 - t;\n return onet * onet * (onet * p0 + 3.0 * t * p1)\n + t * t * (t * p3 + 3.0 * onet * p2);\n}\n\nvoid main()\n{\n vec2 unit = vec2(1.0 / textureWidth, 0.0);\n vec3 p0 = texture2D(pointsTexture, uv).rgb;\n vec3 p1 = texture2D(pointsTexture, uv + unit).rgb;\n vec3 p2 = texture2D(pointsTexture, uv + unit * 2.0).rgb;\n vec3 p3 = texture2D(pointsTexture, uv + unit * 3.0).rgb;\n\n vec3 positionPrev = getPointAt(prevT, p0, p1, p2, p3);\n vec3 position = getPointAt(currT, p0, p1, p2, p3);\n vec3 positionNext = getPointAt(nextT, p0, p1, p2, p3);\n\n @import ecgl.lines3D.expandLine\n\n gl_Position = currProj;\n\n v_Color = color;\n}\n\n@end\n\n@export ecgl.trail.fragment\n\nvarying vec4 v_Color;\n\nuniform sampler2D sprite;\n\nvoid main()\n{\n gl_FragColor = v_Color;\n\n#ifdef SPRITE_ENABLED\n gl_FragColor *= texture2D(sprite, gl_PointCoord);\n#endif\n\n}\n@end";
