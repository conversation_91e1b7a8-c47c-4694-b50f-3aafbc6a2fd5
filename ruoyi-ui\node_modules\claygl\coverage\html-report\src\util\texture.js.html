<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for src/util/texture.js</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../../index.html">all files</a> / <a href="index.html">src/util/</a> texture.js
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">23.36% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>32/137</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">21.13% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>15/71</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">36.36% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>4/11</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">23.36% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>32/137</span>
      </div>
    </div>
  </div>
  <div class='status-line low'></div>
<pre><table class="coverage">
<tr><td class="line-count quiet">1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
116
117
118
119
120
121
122
123
124
125
126
127
128
129
130
131
132
133
134
135
136
137
138
139
140
141
142
143
144
145
146
147
148
149
150
151
152
153
154
155
156
157
158
159
160
161
162
163
164
165
166
167
168
169
170
171
172
173
174
175
176
177
178
179
180
181
182
183
184
185
186
187
188
189
190
191
192
193
194
195
196
197
198
199
200
201
202
203
204
205
206
207
208
209
210
211
212
213
214
215
216
217
218
219
220
221
222
223
224
225
226
227
228
229
230
231
232
233
234
235
236
237
238
239
240
241
242
243
244
245
246
247
248
249
250
251
252
253
254
255
256
257
258
259
260
261
262
263
264
265
266
267
268
269
270
271
272
273
274
275
276
277
278
279
280
281
282
283
284
285
286
287
288
289
290
291
292
293
294
295
296
297
298
299
300
301
302
303
304
305
306
307
308
309
310
311
312
313</td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">import Texture2D from '../Texture2D';
import TextureCube from '../TextureCube';
import request from '../core/request';
import EnvironmentMapPass from '../prePass/EnvironmentMap';
import Skydome from '../plugin/Skydome';
import Scene from '../Scene';
&nbsp;
import dds from './dds';
import hdr from './hdr';
&nbsp;
/**
 * @alias clay.util.texture
 */
var textureUtil = {
    /**
     * @param  {string|object} path
     * @param  {object} [option]
     * @param  {Function} [onsuccess]
     * @param  {Function} [onerror]
     * @return {clay.Texture}
     */
    loadTexture: function (path, option, onsuccess, onerror) {
        var texture;
        if (typeof(option) === 'function') {
            onsuccess = option;
            onerror = onsuccess;
            option = {};
        }
        else {
            option = option || <span class="branch-1 cbranch-no" title="branch not covered" >{};</span>
        }
        <span class="missing-if-branch" title="else path not taken" >E</span>if (typeof(path) === 'string') {
            if (path.match(/.hdr$/) || option.fileType === 'hdr') {
                texture = new Texture2D({
                    width: 0,
                    height: 0,
                    sRGB: false
                });
                textureUtil._fetchTexture(
                    path,
                    function (data) {
                        hdr.parseRGBE(data, texture, option.exposure);
                        texture.dirty();
                        onsuccess &amp;&amp; onsuccess(texture);
                    },
                    onerror
                );
                return texture;
            }
            else <span class="missing-if-branch" title="if path not taken" >I</span>if (path.match(/.dds$/) || option.fileType === 'dds') {
<span class="cstat-no" title="statement not covered" >                texture = new Texture2D({</span>
                    width: 0,
                    height: 0
                });
<span class="cstat-no" title="statement not covered" >                textureUtil._fetchTexture(</span>
                    path,
                    function (data) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >                        dds.parse(data, texture);</span>
<span class="cstat-no" title="statement not covered" >                        texture.dirty();</span>
<span class="cstat-no" title="statement not covered" >                        onsuccess &amp;&amp; onsuccess(texture);</span>
                    },
                    onerror
                );
            }
            else {
                texture = new Texture2D();
                texture.load(path);
                texture.success(onsuccess);
                texture.error(onerror);
            }
        }
        else <span class="cstat-no" title="statement not covered" >if (typeof path === 'object' &amp;&amp; typeof(path.px) !== 'undefined') {</span>
<span class="cstat-no" title="statement not covered" >            texture = new TextureCube();</span>
<span class="cstat-no" title="statement not covered" >            texture.load(path);</span>
<span class="cstat-no" title="statement not covered" >            texture.success(onsuccess);</span>
<span class="cstat-no" title="statement not covered" >            texture.error(onerror);</span>
        }
        return texture;
    },
&nbsp;
    /**
     * Load a panorama texture and render it to a cube map
     * @param  {clay.Renderer} renderer
     * @param  {string} path
     * @param  {clay.TextureCube} cubeMap
     * @param  {object} [option]
     * @param  {boolean} [option.encodeRGBM]
     * @param  {number} [option.exposure]
     * @param  {Function} [onsuccess]
     * @param  {Function} [onerror]
     */
    loadPanorama: function (renderer, path, cubeMap, option, onsuccess, onerror) <span class="fstat-no" title="function not covered" >{</span>
        var self = <span class="cstat-no" title="statement not covered" >this;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        if (typeof(option) === 'function') {</span>
<span class="cstat-no" title="statement not covered" >            onsuccess = option;</span>
<span class="cstat-no" title="statement not covered" >            onerror = onsuccess;</span>
<span class="cstat-no" title="statement not covered" >            option = {};</span>
        }
        else {
<span class="cstat-no" title="statement not covered" >            option = option || {};</span>
        }
&nbsp;
<span class="cstat-no" title="statement not covered" >        textureUtil.loadTexture(path, option, function (texture) <span class="fstat-no" title="function not covered" >{</span></span>
            // PENDING
<span class="cstat-no" title="statement not covered" >            texture.flipY = option.flipY || false;</span>
<span class="cstat-no" title="statement not covered" >            self.panoramaToCubeMap(renderer, texture, cubeMap, option);</span>
<span class="cstat-no" title="statement not covered" >            texture.dispose(renderer);</span>
<span class="cstat-no" title="statement not covered" >            onsuccess &amp;&amp; onsuccess(cubeMap);</span>
        }, onerror);
    },
&nbsp;
    /**
     * Render a panorama texture to a cube map
     * @param  {clay.Renderer} renderer
     * @param  {clay.Texture2D} panoramaMap
     * @param  {clay.TextureCube} cubeMap
     * @param  {Object} option
     * @param  {boolean} [option.encodeRGBM]
     */
    panoramaToCubeMap: function (renderer, panoramaMap, cubeMap, option) {
        var environmentMapPass = new EnvironmentMapPass();
        var skydome = new Skydome({
            scene: new Scene()
        });
        skydome.material.set('diffuseMap', panoramaMap);
&nbsp;
        option = option || <span class="branch-1 cbranch-no" title="branch not covered" >{};</span>
        <span class="missing-if-branch" title="if path not taken" >I</span>if (option.encodeRGBM) {
<span class="cstat-no" title="statement not covered" >            skydome.material.define('fragment', 'RGBM_ENCODE');</span>
        }
&nbsp;
        // Share sRGB
        cubeMap.sRGB = panoramaMap.sRGB;
&nbsp;
        environmentMapPass.texture = cubeMap;
        environmentMapPass.render(renderer, skydome.scene);
        environmentMapPass.texture = null;
        environmentMapPass.dispose(renderer);
        return cubeMap;
    },
&nbsp;
    /**
     * Convert height map to normal map
     * @param {HTMLImageElement|HTMLCanvasElement} image
     * @param {boolean} [checkBump=false]
     * @return {HTMLCanvasElement}
     */
    heightToNormal: function (image, checkBump) <span class="fstat-no" title="function not covered" >{</span>
        var canvas = <span class="cstat-no" title="statement not covered" >document.createElement('canvas');</span>
        var width = <span class="cstat-no" title="statement not covered" >canvas.width = image.width;</span>
        var height = <span class="cstat-no" title="statement not covered" >canvas.height = image.height;</span>
        var ctx = <span class="cstat-no" title="statement not covered" >canvas.getContext('2d');</span>
<span class="cstat-no" title="statement not covered" >        ctx.drawImage(image, 0, 0, width, height);</span>
<span class="cstat-no" title="statement not covered" >        checkBump = checkBump || false;</span>
        var srcData = <span class="cstat-no" title="statement not covered" >ctx.getImageData(0, 0, width, height);</span>
        var dstData = <span class="cstat-no" title="statement not covered" >ctx.createImageData(width, height);</span>
<span class="cstat-no" title="statement not covered" >        for (var i = 0; i &lt; srcData.data.length; i += 4) {</span>
<span class="cstat-no" title="statement not covered" >            if (checkBump) {</span>
                var r = <span class="cstat-no" title="statement not covered" >srcData.data[i];</span>
                var g = <span class="cstat-no" title="statement not covered" >srcData.data[i + 1];</span>
                var b = <span class="cstat-no" title="statement not covered" >srcData.data[i + 2];</span>
                var diff = <span class="cstat-no" title="statement not covered" >Math.abs(r - g) + Math.abs(g - b);</span>
<span class="cstat-no" title="statement not covered" >                if (diff &gt; 20) {</span>
<span class="cstat-no" title="statement not covered" >                    console.warn('Given image is not a height map');</span>
<span class="cstat-no" title="statement not covered" >                    return image;</span>
                }
            }
            // Modified from http://mrdoob.com/lab/javascript/height2normal/
            var x1, y1, x2, y2;
<span class="cstat-no" title="statement not covered" >            if (i % (width * 4) === 0) {</span>
                // left edge
<span class="cstat-no" title="statement not covered" >                x1 = srcData.data[i];</span>
<span class="cstat-no" title="statement not covered" >                x2 = srcData.data[i + 4];</span>
            }
            else <span class="cstat-no" title="statement not covered" >if (i % (width * 4) === (width - 1) * 4) {</span>
                // right edge
<span class="cstat-no" title="statement not covered" >                x1 = srcData.data[i - 4];</span>
<span class="cstat-no" title="statement not covered" >                x2 = srcData.data[i];</span>
            }
            else {
<span class="cstat-no" title="statement not covered" >                x1 = srcData.data[i - 4];</span>
<span class="cstat-no" title="statement not covered" >                x2 = srcData.data[i + 4];</span>
            }
&nbsp;
<span class="cstat-no" title="statement not covered" >            if (i &lt; width * 4) {</span>
                // top edge
<span class="cstat-no" title="statement not covered" >                y1 = srcData.data[i];</span>
<span class="cstat-no" title="statement not covered" >                y2 = srcData.data[i + width * 4];</span>
            }
            else <span class="cstat-no" title="statement not covered" >if (i &gt; width * (height - 1) * 4) {</span>
                // bottom edge
<span class="cstat-no" title="statement not covered" >                y1 = srcData.data[i - width * 4];</span>
<span class="cstat-no" title="statement not covered" >                y2 = srcData.data[i];</span>
            }
            else {
<span class="cstat-no" title="statement not covered" >                y1 = srcData.data[i - width * 4];</span>
<span class="cstat-no" title="statement not covered" >                y2 = srcData.data[i + width * 4];</span>
            }
&nbsp;
<span class="cstat-no" title="statement not covered" >            dstData.data[i] = (x1 - x2) + 127;</span>
<span class="cstat-no" title="statement not covered" >            dstData.data[i + 1] = (y1 - y2) + 127;</span>
<span class="cstat-no" title="statement not covered" >            dstData.data[i + 2] = 255;</span>
<span class="cstat-no" title="statement not covered" >            dstData.data[i + 3] = 255;</span>
        }
<span class="cstat-no" title="statement not covered" >        ctx.putImageData(dstData, 0, 0);</span>
<span class="cstat-no" title="statement not covered" >        return canvas;</span>
    },
&nbsp;
    /**
     * Convert height map to normal map
     * @param {HTMLImageElement|HTMLCanvasElement} image
     * @param {boolean} [checkBump=false]
     * @param {number} [threshold=20]
     * @return {HTMLCanvasElement}
     */
    isHeightImage: function (img, downScaleSize, threshold) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        if (!img || !img.width || !img.height) {</span>
<span class="cstat-no" title="statement not covered" >            return false;</span>
        }
&nbsp;
        var canvas = <span class="cstat-no" title="statement not covered" >document.createElement('canvas');</span>
        var ctx = <span class="cstat-no" title="statement not covered" >canvas.getContext('2d');</span>
        var size = <span class="cstat-no" title="statement not covered" >downScaleSize || 32;</span>
<span class="cstat-no" title="statement not covered" >        threshold = threshold || 20;</span>
<span class="cstat-no" title="statement not covered" >        canvas.width = canvas.height = size;</span>
<span class="cstat-no" title="statement not covered" >        ctx.drawImage(img, 0, 0, size, size);</span>
        var srcData = <span class="cstat-no" title="statement not covered" >ctx.getImageData(0, 0, size, size);</span>
<span class="cstat-no" title="statement not covered" >        for (var i = 0; i &lt; srcData.data.length; i += 4) {</span>
            var r = <span class="cstat-no" title="statement not covered" >srcData.data[i];</span>
            var g = <span class="cstat-no" title="statement not covered" >srcData.data[i + 1];</span>
            var b = <span class="cstat-no" title="statement not covered" >srcData.data[i + 2];</span>
            var diff = <span class="cstat-no" title="statement not covered" >Math.abs(r - g) + Math.abs(g - b);</span>
<span class="cstat-no" title="statement not covered" >            if (diff &gt; threshold) {</span>
<span class="cstat-no" title="statement not covered" >                return false;</span>
            }
        }
<span class="cstat-no" title="statement not covered" >        return true;</span>
    },
&nbsp;
    _fetchTexture: function (path, onsuccess, onerror) {
        request.get({
            url: path,
            responseType: 'arraybuffer',
            onload: onsuccess,
            onerror: onerror
        });
    },
&nbsp;
    /**
     * Create a chessboard texture
     * @param  {number} [size]
     * @param  {number} [unitSize]
     * @param  {string} [color1]
     * @param  {string} [color2]
     * @return {clay.Texture2D}
     */
    createChessboard: function (size, unitSize, color1, color2) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        size = size || 512;</span>
<span class="cstat-no" title="statement not covered" >        unitSize = unitSize || 64;</span>
<span class="cstat-no" title="statement not covered" >        color1 = color1 || 'black';</span>
<span class="cstat-no" title="statement not covered" >        color2 = color2 || 'white';</span>
&nbsp;
        var repeat = <span class="cstat-no" title="statement not covered" >Math.ceil(size / unitSize);</span>
&nbsp;
        var canvas = <span class="cstat-no" title="statement not covered" >document.createElement('canvas');</span>
<span class="cstat-no" title="statement not covered" >        canvas.width = size;</span>
<span class="cstat-no" title="statement not covered" >        canvas.height = size;</span>
        var ctx = <span class="cstat-no" title="statement not covered" >canvas.getContext('2d');</span>
<span class="cstat-no" title="statement not covered" >        ctx.fillStyle = color2;</span>
<span class="cstat-no" title="statement not covered" >        ctx.fillRect(0, 0, size, size);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        ctx.fillStyle = color1;</span>
<span class="cstat-no" title="statement not covered" >        for (var i = 0; i &lt; repeat; i++) {</span>
<span class="cstat-no" title="statement not covered" >            for (var j = 0; j &lt; repeat; j++) {</span>
                var isFill = <span class="cstat-no" title="statement not covered" >j % 2 ? (i % 2) : (i % 2 - 1);</span>
<span class="cstat-no" title="statement not covered" >                if (isFill) {</span>
<span class="cstat-no" title="statement not covered" >                    ctx.fillRect(i * unitSize, j * unitSize, unitSize, unitSize);</span>
                }
            }
        }
&nbsp;
        var texture = <span class="cstat-no" title="statement not covered" >new Texture2D({</span>
            image: canvas,
            anisotropic: 8
        });
&nbsp;
<span class="cstat-no" title="statement not covered" >        return texture;</span>
    },
&nbsp;
    /**
     * Create a blank pure color 1x1 texture
     * @param  {string} color
     * @return {clay.Texture2D}
     */
    createBlank: function (color) <span class="fstat-no" title="function not covered" >{</span>
        var canvas = <span class="cstat-no" title="statement not covered" >document.createElement('canvas');</span>
<span class="cstat-no" title="statement not covered" >        canvas.width = 1;</span>
<span class="cstat-no" title="statement not covered" >        canvas.height = 1;</span>
        var ctx = <span class="cstat-no" title="statement not covered" >canvas.getContext('2d');</span>
<span class="cstat-no" title="statement not covered" >        ctx.fillStyle = color;</span>
<span class="cstat-no" title="statement not covered" >        ctx.fillRect(0, 0, 1, 1);</span>
&nbsp;
        var texture = <span class="cstat-no" title="statement not covered" >new Texture2D({</span>
            image: canvas
        });
&nbsp;
<span class="cstat-no" title="statement not covered" >        return texture;</span>
    }
};
&nbsp;
export default textureUtil;
&nbsp;</pre></td></tr>
</table></pre>
<div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Fri Feb 23 2018 01:09:23 GMT+0800 (CST)
</div>
</div>
<script src="../../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../../sorter.js"></script>
</body>
</html>
