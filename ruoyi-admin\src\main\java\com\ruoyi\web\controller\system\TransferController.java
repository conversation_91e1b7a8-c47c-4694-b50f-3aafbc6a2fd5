package com.ruoyi.web.controller.system;

import com.baomidou.dynamic.datasource.annotation.Slave;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.system.domain.TUserMoney;
import com.ruoyi.system.mapper.TUserMoneyMapper;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Objects;

@RestController
@RequestMapping("/system/transfer")
public class TransferController {

    @Autowired
    private TUserMoneyMapper userMoneyMapper;

    @GetMapping("/amount")
    @Transactional
    public AjaxResult transferAmount(String transferA, String transferB, BigDecimal amount) {
        System.out.println("amount start, transferA" + transferA + ", transferB" + transferB + ", amount" + amount);
        //减少自己金额
        userMoneyMapper.updateAmount(transferA, amount.negate());

        try {
            Thread.sleep(5000L);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

        //新增他人金额
        userMoneyMapper.updateAmount(transferB, amount);

        return AjaxResult.success();
    }

    @GetMapping("/amountV2")
    @Transactional
    public AjaxResult transferAmountV2(String transferA, String transferB, BigDecimal amount) {
        System.out.println("amount start, transferA" + transferA + ", transferB" + transferB + ", amount" + amount);

        if (transferA.hashCode() >= transferB.hashCode()) {
            //减少自己金额
            userMoneyMapper.updateAmount(transferA, amount.negate());

            try {
                Thread.sleep(5000L);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }

            //新增他人金额
            userMoneyMapper.updateAmount(transferB, amount);
        } else {
            //新增他人金额
            userMoneyMapper.updateAmount(transferB, amount);

            try {
                Thread.sleep(5000L);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }

            //减少自己金额
            userMoneyMapper.updateAmount(transferA, amount.negate());
        }

        return AjaxResult.success();
    }

    @GetMapping("/amountV3")
    @Transactional
    public AjaxResult transferAmountV3(String transferA, String transferB, BigDecimal amount) {
        System.out.println("amount start, transferA" + transferA + ", transferB" + transferB + ", amount" + amount);

        TUserMoney update01 = new TUserMoney();
        update01.setUserName(transferA);
        update01.setAmount(amount.negate());

        TUserMoney update02 = new TUserMoney();
        update02.setUserName(transferB);
        update02.setAmount(amount);

        userMoneyMapper.updateBatch(Arrays.asList(update01, update02));

        return AjaxResult.success();
    }
}
