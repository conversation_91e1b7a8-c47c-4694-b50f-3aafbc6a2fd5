<template>
  <div class="flight-list-container">
    <el-tabs v-model="activeTab" @tab-click="handleTabClick">
      <el-tab-pane label="进港" name="inbound">
        <div class="tab-header">
          <span class="tab-title">正在进入禄口机场</span>
          <span class="flight-count">{{ inboundFlights.length }}架</span>
        </div>
        <div class="flight-list">
          <div 
            v-for="flight in inboundFlights" 
            :key="flight.flightId"
            :class="['flight-item', { 'important': flight.isImportant === '1' }]"
          >
            <div class="flight-header">
              <span class="flight-number">{{ flight.flightNumber }}</span>
              <span v-if="flight.isImportant === '1'" class="important-tag">重点</span>
              <span class="status-tag" :class="getStatusClass(flight.status)">
                {{ getStatusText(flight.status) }}
              </span>
            </div>
            <div class="flight-info">
              <div class="route-info">
                <span class="origin">{{ flight.origin }}</span>
                <i class="el-icon-right"></i>
                <span class="destination">{{ flight.destination }}</span>
              </div>
              <div class="time-info">
                <span class="time-label">起飞:</span>
                <span class="time-value">{{ formatTime(flight.departureTime) }}</span>
                <span class="time-label">预计到达:</span>
                <span class="time-value">{{ formatTime(flight.arrivalTime) }}</span>
              </div>
              <div class="detail-info">
                <span>{{ flight.airline }}</span>
                <span>{{ flight.aircraftType }}</span>
                <span v-if="flight.gate">{{ flight.gate }}登机口</span>
              </div>
            </div>
          </div>
        </div>
      </el-tab-pane>
      
      <el-tab-pane label="出港" name="outbound">
        <div class="tab-header">
          <span class="tab-title">正在飞离禄口机场</span>
          <span class="flight-count">{{ outboundFlights.length }}架</span>
        </div>
        <div class="flight-list">
          <div 
            v-for="flight in outboundFlights" 
            :key="flight.flightId"
            :class="['flight-item', { 'important': flight.isImportant === '1' }]"
          >
            <div class="flight-header">
              <span class="flight-number">{{ flight.flightNumber }}</span>
              <span v-if="flight.isImportant === '1'" class="important-tag">重点</span>
              <span class="status-tag" :class="getStatusClass(flight.status)">
                {{ getStatusText(flight.status) }}
              </span>
            </div>
            <div class="flight-info">
              <div class="route-info">
                <span class="origin">{{ flight.origin }}</span>
                <i class="el-icon-right"></i>
                <span class="destination">{{ flight.destination }}</span>
              </div>
              <div class="time-info">
                <span class="time-label">起飞:</span>
                <span class="time-value">{{ formatTime(flight.departureTime) }}</span>
                <span class="time-label">预计到达:</span>
                <span class="time-value">{{ formatTime(flight.arrivalTime) }}</span>
              </div>
              <div class="detail-info">
                <span>{{ flight.airline }}</span>
                <span>{{ flight.aircraftType }}</span>
                <span v-if="flight.gate">{{ flight.gate }}登机口</span>
              </div>
            </div>
          </div>
        </div>
      </el-tab-pane>
      
      <el-tab-pane label="即将起飞" name="departing">
        <div class="tab-header">
          <span class="tab-title">即将起飞航班</span>
          <span class="flight-count">{{ departingFlights.length }}架</span>
        </div>
        <div class="flight-list">
          <div 
            v-for="flight in departingFlights" 
            :key="flight.flightId"
            :class="['flight-item', { 'important': flight.isImportant === '1' }]"
          >
            <div class="flight-header">
              <span class="flight-number">{{ flight.flightNumber }}</span>
              <span v-if="flight.isImportant === '1'" class="important-tag">重点</span>
              <span class="status-tag" :class="getStatusClass(flight.status)">
                {{ getStatusText(flight.status) }}
              </span>
            </div>
            <div class="flight-info">
              <div class="route-info">
                <span class="origin">{{ flight.origin }}</span>
                <i class="el-icon-right"></i>
                <span class="destination">{{ flight.destination }}</span>
              </div>
              <div class="time-info">
                <span class="time-label">计划起飞:</span>
                <span class="time-value">{{ formatTime(flight.departureTime) }}</span>
                <span class="time-label">预计到达:</span>
                <span class="time-value">{{ formatTime(flight.arrivalTime) }}</span>
              </div>
              <div class="detail-info">
                <span>{{ flight.airline }}</span>
                <span>{{ flight.aircraftType }}</span>
                <span v-if="flight.gate">{{ flight.gate }}登机口</span>
                <span v-if="flight.terminal">{{ flight.terminal }}</span>
              </div>
            </div>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import { getInboundFlights, getOutboundFlights, getDepartingFlights } from '@/api/ky/flight'

export default {
  name: 'FlightList',
  data() {
    return {
      activeTab: 'inbound',
      inboundFlights: [],
      outboundFlights: [],
      departingFlights: [],
      timer: null
    }
  },
  mounted() {
    this.loadFlightData()
    // 每30秒刷新一次数据
    this.timer = setInterval(() => {
      this.loadFlightData()
    }, 30000)
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer)
    }
  },
  methods: {
    async loadFlightData() {
      try {
        const [inboundRes, outboundRes, departingRes] = await Promise.all([
          getInboundFlights({}),
          getOutboundFlights({}),
          getDepartingFlights({})
        ])
        
        this.inboundFlights = inboundRes.data || []
        this.outboundFlights = outboundRes.data || []
        this.departingFlights = departingRes.data || []
        
        // 重点航班置顶
        this.sortFlightsByImportance()
      } catch (error) {
        console.error('加载航班数据失败:', error)
      }
    },
    
    sortFlightsByImportance() {
      const sortFn = (a, b) => {
        if (a.isImportant === '1' && b.isImportant !== '1') return -1
        if (a.isImportant !== '1' && b.isImportant === '1') return 1
        return new Date(a.departureTime) - new Date(b.departureTime)
      }
      
      this.inboundFlights.sort(sortFn)
      this.outboundFlights.sort(sortFn)
      this.departingFlights.sort(sortFn)
    },
    
    handleTabClick(tab) {
      this.activeTab = tab.name
    },
    
    formatTime(timeStr) {
      if (!timeStr) return '--'
      const date = new Date(timeStr)
      return date.toLocaleTimeString('zh-CN', { 
        hour: '2-digit', 
        minute: '2-digit' 
      })
    },
    
    getStatusText(status) {
      const statusMap = {
        '0': '即将起飞',
        '1': '飞行中',
        '2': '已抵达',
        '3': '延误',
        '4': '取消'
      }
      return statusMap[status] || '未知'
    },
    
    getStatusClass(status) {
      const classMap = {
        '0': 'status-ready',
        '1': 'status-flying',
        '2': 'status-arrived',
        '3': 'status-delayed',
        '4': 'status-cancelled'
      }
      return classMap[status] || ''
    }
  }
}
</script>

<style scoped>
.flight-list-container {
  height: 600px;
  background: #f5f7fa;
  border-radius: 8px;
  overflow: hidden;
}

.tab-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  margin-bottom: 10px;
}

.tab-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.flight-count {
  background: #409eff;
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
}

.flight-list {
  height: 500px;
  overflow-y: auto;
  padding: 0 10px;
}

.flight-item {
  background: white;
  margin-bottom: 10px;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.flight-item:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.flight-item.important {
  border-left: 4px solid #f56c6c;
}

.flight-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.flight-number {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin-right: 10px;
}

.important-tag {
  background: #f56c6c;
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  margin-right: 10px;
}

.status-tag {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  color: white;
}

.status-ready {
  background: #e6a23c;
}

.status-flying {
  background: #67c23a;
}

.status-arrived {
  background: #909399;
}

.status-delayed {
  background: #f56c6c;
}

.status-cancelled {
  background: #f56c6c;
}

.flight-info {
  font-size: 14px;
  color: #606266;
}

.route-info {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.origin, .destination {
  font-weight: 500;
}

.route-info i {
  margin: 0 8px;
  color: #c0c4cc;
}

.time-info {
  margin-bottom: 8px;
}

.time-label {
  color: #909399;
  margin-right: 5px;
}

.time-value {
  color: #303133;
  font-weight: 500;
  margin-right: 15px;
}

.detail-info span {
  margin-right: 15px;
  color: #909399;
}

/* 滚动条样式 */
.flight-list::-webkit-scrollbar {
  width: 6px;
}

.flight-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.flight-list::-webkit-scrollbar-thumb {
  background: #c0c4cc;
  border-radius: 3px;
}

.flight-list::-webkit-scrollbar-thumb:hover {
  background: #a8abb2;
}
</style>
