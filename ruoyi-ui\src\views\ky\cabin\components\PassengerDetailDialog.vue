<template>
  <el-dialog
    title="乘客详细信息"
    :visible.sync="dialogVisible"
    width="80%"
    :before-close="handleClose"
  >
    <div class="passenger-detail" v-if="passengerData">
      <!-- 基本信息 -->
      <el-card class="info-card">
        <div slot="header">
          <span>基本信息</span>
          <el-tag 
            v-if="passengerData.isKeyPerson === '1'" 
            type="danger" 
            class="key-person-tag"
          >
            重点人员
          </el-tag>
        </div>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>姓名:</label>
              <span>{{ passengerData.passengerName }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>身份证号:</label>
              <span>{{ passengerData.idCard }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>座位号:</label>
              <span>{{ passengerData.seatNumber }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>性别:</label>
              <span>{{ getGenderName(passengerData.gender) }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>年龄:</label>
              <span>{{ passengerData.age }}岁</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>国籍:</label>
              <span>{{ passengerData.nationality }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>联系电话:</label>
              <span>{{ passengerData.phone }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>舱位等级:</label>
              <span>{{ getCabinClassName(passengerData.cabinClass) }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>票务状态:</label>
              <el-tag :type="getTicketStatusTag(passengerData.ticketStatus)">
                {{ getTicketStatusName(passengerData.ticketStatus) }}
              </el-tag>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 重点人员信息 -->
      <el-card class="info-card" v-if="passengerData.isKeyPerson === '1'">
        <div slot="header">
          <span>重点人员信息</span>
        </div>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>人员类型:</label>
              <el-tag :type="getKeyPersonTypeTag(passengerData.keyPersonType)">
                {{ getKeyPersonTypeName(passengerData.keyPersonType) }}
              </el-tag>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>风险等级:</label>
              <el-tag :type="getRiskLevelTag(passengerData.riskLevel)">
                {{ getRiskLevelName(passengerData.riskLevel) }}
              </el-tag>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>风险评估:</label>
              <el-button size="mini" @click="loadRiskAssessment">查看详情</el-button>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 历史记录 -->
      <el-card class="info-card">
        <div slot="header">
          <span>历史记录</span>
          <el-button size="mini" @click="loadHistoryRecords">刷新</el-button>
        </div>
        <div v-if="historyLoading" class="loading-container">
          <el-loading-spinner></el-loading-spinner>
          <span>加载中...</span>
        </div>
        <div v-else>
          <div class="history-summary">
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="summary-item">
                  <div class="summary-value">{{ historyStatistics.totalTravelTimes || 0 }}</div>
                  <div class="summary-label">总出行次数</div>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="summary-item">
                  <div class="summary-value">{{ historyStatistics.abnormalRecords || 0 }}</div>
                  <div class="summary-label">异常记录</div>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="summary-item">
                  <div class="summary-value">{{ getLastTravelDate() }}</div>
                  <div class="summary-label">最近出行</div>
                </div>
              </el-col>
            </el-row>
          </div>
          
          <el-table :data="historyRecords" style="width: 100%" max-height="300">
            <el-table-column prop="flightDate" label="日期" width="100">
              <template slot-scope="scope">
                {{ formatDate(scope.row.flightDate) }}
              </template>
            </el-table-column>
            <el-table-column prop="flightNumber" label="航班号" width="100"></el-table-column>
            <el-table-column prop="origin" label="起点" width="80"></el-table-column>
            <el-table-column prop="destination" label="终点" width="80"></el-table-column>
            <el-table-column prop="recordType" label="记录类型" width="100">
              <template slot-scope="scope">
                <el-tag :type="getRecordTypeTag(scope.row.recordType)">
                  {{ getRecordTypeName(scope.row.recordType) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="eventDescription" label="事件描述" show-overflow-tooltip></el-table-column>
            <el-table-column prop="riskAssessment" label="风险评估" width="100">
              <template slot-scope="scope">
                <el-tag v-if="scope.row.riskAssessment" size="mini" :type="getRiskAssessmentTag(scope.row.riskAssessment)">
                  {{ scope.row.riskAssessment }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>

      <!-- 风险评估详情 -->
      <el-card class="info-card" v-if="riskAssessment">
        <div slot="header">
          <span>风险评估详情</span>
        </div>
        <div class="risk-assessment">
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="risk-item">
                <label>风险分数:</label>
                <span class="risk-score" :class="getRiskScoreClass(riskAssessment.riskScore)">
                  {{ riskAssessment.riskScore }}
                </span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="risk-item">
                <label>风险等级:</label>
                <el-tag :type="getRiskLevelTag(riskAssessment.riskLevel)">
                  {{ getRiskLevelName(riskAssessment.riskLevel) }}
                </el-tag>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="risk-item">
                <label>评估时间:</label>
                <span>{{ formatDateTime(new Date()) }}</span>
              </div>
            </el-col>
          </el-row>
          <div class="risk-factors" v-if="riskAssessment.riskFactors && riskAssessment.riskFactors.length > 0">
            <label>风险因素:</label>
            <div class="factors-list">
              <el-tag 
                v-for="(factor, index) in riskAssessment.riskFactors" 
                :key="index"
                type="warning"
                class="factor-tag"
              >
                {{ factor }}
              </el-tag>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关闭</el-button>
      <el-button type="primary" @click="exportPassengerInfo">导出信息</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getPassengerDetailInfo, getPassengerRiskAssessment } from "@/api/ky/passengerHistory"

export default {
  name: "PassengerDetailDialog",
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    passengerData: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      dialogVisible: false,
      historyRecords: [],
      historyStatistics: {},
      riskAssessment: null,
      historyLoading: false
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
      if (val && this.passengerData) {
        this.loadHistoryRecords()
      }
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    }
  },
  methods: {
    async loadHistoryRecords() {
      if (!this.passengerData || !this.passengerData.idCard) return
      
      this.historyLoading = true
      try {
        const response = await getPassengerDetailInfo(this.passengerData.idCard)
        const data = response.data
        this.historyRecords = data.historyList || []
        this.historyStatistics = {
          totalTravelTimes: data.totalTravelTimes || 0,
          abnormalRecords: data.abnormalRecords || 0
        }
      } catch (error) {
        console.error('加载历史记录失败:', error)
        this.$message.error('加载历史记录失败')
      } finally {
        this.historyLoading = false
      }
    },
    async loadRiskAssessment() {
      if (!this.passengerData || !this.passengerData.idCard) return
      
      try {
        const response = await getPassengerRiskAssessment(this.passengerData.idCard)
        this.riskAssessment = response.data
      } catch (error) {
        console.error('加载风险评估失败:', error)
        this.$message.error('加载风险评估失败')
      }
    },
    handleClose() {
      this.dialogVisible = false
      this.riskAssessment = null
      this.historyRecords = []
      this.historyStatistics = {}
    },
    exportPassengerInfo() {
      // 导出乘客信息功能
      this.$message.success('导出功能开发中...')
    },
    getGenderName(gender) {
      return gender === '1' ? '男' : gender === '0' ? '女' : '未知'
    },
    getCabinClassName(cabinClass) {
      const classes = {
        '0': '经济舱',
        '1': '商务舱', 
        '2': '头等舱'
      }
      return classes[cabinClass] || '未知'
    },
    getTicketStatusName(status) {
      const statuses = {
        '0': '已订票',
        '1': '已值机',
        '2': '已登机',
        '3': '已起飞'
      }
      return statuses[status] || '未知'
    },
    getTicketStatusTag(status) {
      const tags = {
        '0': 'info',
        '1': 'warning',
        '2': 'success',
        '3': 'primary'
      }
      return tags[status] || 'info'
    },
    getKeyPersonTypeName(type) {
      const types = {
        '1': '涉恐',
        '2': '涉毒',
        '3': '涉黑', 
        '4': '逃犯',
        '5': '精神病',
        '6': '其他危险',
        '7': 'VIP'
      }
      return types[type] || '未知'
    },
    getKeyPersonTypeTag(type) {
      const tags = {
        '1': 'danger',
        '2': 'danger',
        '3': 'danger',
        '4': 'danger', 
        '5': 'warning',
        '6': 'warning',
        '7': 'success'
      }
      return tags[type] || 'info'
    },
    getRiskLevelName(level) {
      const levels = {
        '1': '低风险',
        '2': '中风险',
        '3': '高风险'
      }
      return levels[level] || '未知'
    },
    getRiskLevelTag(level) {
      const tags = {
        '1': 'success',
        '2': 'warning',
        '3': 'danger'
      }
      return tags[level] || 'info'
    },
    getRecordTypeName(type) {
      const types = {
        '0': '正常记录',
        '1': '异常行为',
        '2': '安全事件',
        '3': '违规记录'
      }
      return types[type] || '未知'
    },
    getRecordTypeTag(type) {
      const tags = {
        '0': 'success',
        '1': 'warning', 
        '2': 'danger',
        '3': 'danger'
      }
      return tags[type] || 'info'
    },
    getRiskAssessmentTag(assessment) {
      if (assessment.includes('高')) return 'danger'
      if (assessment.includes('中')) return 'warning'
      return 'success'
    },
    getRiskScoreClass(score) {
      if (score >= 80) return 'high-risk'
      if (score >= 40) return 'medium-risk'
      return 'low-risk'
    },
    getLastTravelDate() {
      if (this.historyRecords.length === 0) return '无记录'
      return this.formatDate(this.historyRecords[0].flightDate)
    },
    formatDate(date) {
      if (!date) return ''
      return new Date(date).toLocaleDateString()
    },
    formatDateTime(date) {
      if (!date) return ''
      return new Date(date).toLocaleString()
    }
  }
}
</script>

<style scoped>
.passenger-detail {
  max-height: 70vh;
  overflow-y: auto;
}

.info-card {
  margin-bottom: 20px;
}

.key-person-tag {
  float: right;
}

.info-item {
  margin-bottom: 15px;
}

.info-item label {
  font-weight: bold;
  color: #606266;
  margin-right: 10px;
  min-width: 80px;
  display: inline-block;
}

.loading-container {
  text-align: center;
  padding: 40px;
  color: #909399;
}

.history-summary {
  margin-bottom: 20px;
  padding: 15px;
  background: #f5f7fa;
  border-radius: 4px;
}

.summary-item {
  text-align: center;
}

.summary-value {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 5px;
}

.summary-label {
  font-size: 12px;
  color: #909399;
}

.risk-assessment {
  padding: 15px;
}

.risk-item {
  margin-bottom: 15px;
}

.risk-item label {
  font-weight: bold;
  color: #606266;
  margin-right: 10px;
  min-width: 80px;
  display: inline-block;
}

.risk-score {
  font-size: 18px;
  font-weight: bold;
  padding: 5px 10px;
  border-radius: 4px;
}

.risk-score.low-risk {
  background: #f0f9ff;
  color: #67c23a;
}

.risk-score.medium-risk {
  background: #fdf6ec;
  color: #e6a23c;
}

.risk-score.high-risk {
  background: #fef0f0;
  color: #f56c6c;
}

.risk-factors {
  margin-top: 20px;
}

.risk-factors label {
  font-weight: bold;
  color: #606266;
  margin-bottom: 10px;
  display: block;
}

.factors-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.factor-tag {
  margin: 0;
}

.dialog-footer {
  text-align: right;
}
</style>
