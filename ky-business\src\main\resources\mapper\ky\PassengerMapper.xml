<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.ky.mapper.PassengerMapper">
    
    <resultMap type="Passenger" id="PassengerResult">
        <result property="passengerId"    column="passenger_id"    />
        <result property="flightId"    column="flight_id"    />
        <result property="flightNumber"    column="flight_number"    />
        <result property="passengerName"    column="passenger_name"    />
        <result property="idCard"    column="id_card"    />
        <result property="passport"    column="passport"    />
        <result property="gender"    column="gender"    />
        <result property="age"    column="age"    />
        <result property="nationality"    column="nationality"    />
        <result property="phone"    column="phone"    />
        <result property="seatNumber"    column="seat_number"    />
        <result property="cabinClass"    column="cabin_class"    />
        <result property="ticketStatus"    column="ticket_status"    />
        <result property="isKeyPerson"    column="is_key_person"    />
        <result property="keyPersonType"    column="key_person_type"    />
        <result property="riskLevel"    column="risk_level"    />
        <result property="boardingTime"    column="boarding_time"    />
        <result property="checkinTime"    column="checkin_time"    />
        <result property="specialRequests"    column="special_requests"    />
        <result property="emergencyContact"    column="emergency_contact"    />
        <result property="emergencyPhone"    column="emergency_phone"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectPassengerVo">
        select passenger_id, flight_id, flight_number, passenger_name, id_card, passport, gender, age, nationality, phone, seat_number, cabin_class, ticket_status, is_key_person, key_person_type, risk_level, boarding_time, checkin_time, special_requests, emergency_contact, emergency_phone, create_time, update_time, remark from ky_passenger
    </sql>

    <select id="selectPassengerList" parameterType="Passenger" resultMap="PassengerResult">
        <include refid="selectPassengerVo"/>
        <where>  
            <if test="flightId != null "> and flight_id = #{flightId}</if>
            <if test="flightNumber != null  and flightNumber != ''"> and flight_number like concat('%', #{flightNumber}, '%')</if>
            <if test="passengerName != null  and passengerName != ''"> and passenger_name like concat('%', #{passengerName}, '%')</if>
            <if test="idCard != null  and idCard != ''"> and id_card = #{idCard}</if>
            <if test="passport != null  and passport != ''"> and passport = #{passport}</if>
            <if test="gender != null  and gender != ''"> and gender = #{gender}</if>
            <if test="nationality != null  and nationality != ''"> and nationality = #{nationality}</if>
            <if test="seatNumber != null  and seatNumber != ''"> and seat_number = #{seatNumber}</if>
            <if test="cabinClass != null  and cabinClass != ''"> and cabin_class = #{cabinClass}</if>
            <if test="ticketStatus != null  and ticketStatus != ''"> and ticket_status = #{ticketStatus}</if>
            <if test="isKeyPerson != null  and isKeyPerson != ''"> and is_key_person = #{isKeyPerson}</if>
            <if test="keyPersonType != null  and keyPersonType != ''"> and key_person_type = #{keyPersonType}</if>
            <if test="riskLevel != null  and riskLevel != ''"> and risk_level = #{riskLevel}</if>
        </where>
        order by is_key_person desc, risk_level desc, seat_number
    </select>
    
    <select id="selectPassengerByPassengerId" parameterType="Long" resultMap="PassengerResult">
        <include refid="selectPassengerVo"/>
        where passenger_id = #{passengerId}
    </select>

    <select id="selectPassengerListByFlightId" parameterType="Long" resultMap="PassengerResult">
        <include refid="selectPassengerVo"/>
        where flight_id = #{flightId}
        order by is_key_person desc, risk_level desc, seat_number
    </select>

    <select id="selectPassengerListByFlightNumber" parameterType="String" resultMap="PassengerResult">
        <include refid="selectPassengerVo"/>
        where flight_number = #{flightNumber}
        order by is_key_person desc, risk_level desc, seat_number
    </select>

    <select id="selectKeyPersonList" parameterType="Passenger" resultMap="PassengerResult">
        <include refid="selectPassengerVo"/>
        <where>
            and is_key_person = '1'
            <if test="flightId != null "> and flight_id = #{flightId}</if>
            <if test="flightNumber != null  and flightNumber != ''"> and flight_number like concat('%', #{flightNumber}, '%')</if>
            <if test="keyPersonType != null  and keyPersonType != ''"> and key_person_type = #{keyPersonType}</if>
            <if test="riskLevel != null  and riskLevel != ''"> and risk_level = #{riskLevel}</if>
        </where>
        order by risk_level desc, key_person_type, seat_number
    </select>

    <select id="selectPassengerByIdCard" parameterType="String" resultMap="PassengerResult">
        <include refid="selectPassengerVo"/>
        where id_card = #{idCard}
        order by create_time desc
        limit 1
    </select>

    <select id="selectPassengerBySeat" resultMap="PassengerResult">
        <include refid="selectPassengerVo"/>
        where flight_id = #{flightId} and seat_number = #{seatNumber}
    </select>

    <select id="selectCabinDistribution" parameterType="Long" resultMap="PassengerResult">
        <include refid="selectPassengerVo"/>
        where flight_id = #{flightId}
        order by cabin_class, seat_number
    </select>
        
    <insert id="insertPassenger" parameterType="Passenger" useGeneratedKeys="true" keyProperty="passengerId">
        insert into ky_passenger
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="flightId != null">flight_id,</if>
            <if test="flightNumber != null and flightNumber != ''">flight_number,</if>
            <if test="passengerName != null and passengerName != ''">passenger_name,</if>
            <if test="idCard != null and idCard != ''">id_card,</if>
            <if test="passport != null">passport,</if>
            <if test="gender != null">gender,</if>
            <if test="age != null">age,</if>
            <if test="nationality != null">nationality,</if>
            <if test="phone != null">phone,</if>
            <if test="seatNumber != null and seatNumber != ''">seat_number,</if>
            <if test="cabinClass != null">cabin_class,</if>
            <if test="ticketStatus != null">ticket_status,</if>
            <if test="isKeyPerson != null">is_key_person,</if>
            <if test="keyPersonType != null">key_person_type,</if>
            <if test="riskLevel != null">risk_level,</if>
            <if test="boardingTime != null">boarding_time,</if>
            <if test="checkinTime != null">checkin_time,</if>
            <if test="specialRequests != null">special_requests,</if>
            <if test="emergencyContact != null">emergency_contact,</if>
            <if test="emergencyPhone != null">emergency_phone,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="flightId != null">#{flightId},</if>
            <if test="flightNumber != null and flightNumber != ''">#{flightNumber},</if>
            <if test="passengerName != null and passengerName != ''">#{passengerName},</if>
            <if test="idCard != null and idCard != ''">#{idCard},</if>
            <if test="passport != null">#{passport},</if>
            <if test="gender != null">#{gender},</if>
            <if test="age != null">#{age},</if>
            <if test="nationality != null">#{nationality},</if>
            <if test="phone != null">#{phone},</if>
            <if test="seatNumber != null and seatNumber != ''">#{seatNumber},</if>
            <if test="cabinClass != null">#{cabinClass},</if>
            <if test="ticketStatus != null">#{ticketStatus},</if>
            <if test="isKeyPerson != null">#{isKeyPerson},</if>
            <if test="keyPersonType != null">#{keyPersonType},</if>
            <if test="riskLevel != null">#{riskLevel},</if>
            <if test="boardingTime != null">#{boardingTime},</if>
            <if test="checkinTime != null">#{checkinTime},</if>
            <if test="specialRequests != null">#{specialRequests},</if>
            <if test="emergencyContact != null">#{emergencyContact},</if>
            <if test="emergencyPhone != null">#{emergencyPhone},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updatePassenger" parameterType="Passenger">
        update ky_passenger
        <trim prefix="SET" suffixOverrides=",">
            <if test="flightId != null">flight_id = #{flightId},</if>
            <if test="flightNumber != null and flightNumber != ''">flight_number = #{flightNumber},</if>
            <if test="passengerName != null and passengerName != ''">passenger_name = #{passengerName},</if>
            <if test="idCard != null and idCard != ''">id_card = #{idCard},</if>
            <if test="passport != null">passport = #{passport},</if>
            <if test="gender != null">gender = #{gender},</if>
            <if test="age != null">age = #{age},</if>
            <if test="nationality != null">nationality = #{nationality},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="seatNumber != null and seatNumber != ''">seat_number = #{seatNumber},</if>
            <if test="cabinClass != null">cabin_class = #{cabinClass},</if>
            <if test="ticketStatus != null">ticket_status = #{ticketStatus},</if>
            <if test="isKeyPerson != null">is_key_person = #{isKeyPerson},</if>
            <if test="keyPersonType != null">key_person_type = #{keyPersonType},</if>
            <if test="riskLevel != null">risk_level = #{riskLevel},</if>
            <if test="boardingTime != null">boarding_time = #{boardingTime},</if>
            <if test="checkinTime != null">checkin_time = #{checkinTime},</if>
            <if test="specialRequests != null">special_requests = #{specialRequests},</if>
            <if test="emergencyContact != null">emergency_contact = #{emergencyContact},</if>
            <if test="emergencyPhone != null">emergency_phone = #{emergencyPhone},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where passenger_id = #{passengerId}
    </update>

    <update id="updatePassengerTicketStatus">
        update ky_passenger set ticket_status = #{ticketStatus}, update_time = now()
        where passenger_id = #{passengerId}
    </update>

    <update id="batchUpdateTicketStatus">
        update ky_passenger set ticket_status = #{ticketStatus}, update_time = now()
        where flight_id = #{flightId}
    </update>

    <delete id="deletePassengerByPassengerId" parameterType="Long">
        delete from ky_passenger where passenger_id = #{passengerId}
    </delete>

    <delete id="deletePassengerByPassengerIds" parameterType="String">
        delete from ky_passenger where passenger_id in
        <foreach item="passengerId" collection="array" open="(" separator="," close=")">
            #{passengerId}
        </foreach>
    </delete>

</mapper>
