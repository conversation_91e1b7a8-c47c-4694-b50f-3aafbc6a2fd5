<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for src/compositor/CompositorNode.js</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../../index.html">all files</a> / <a href="index.html">src/compositor/</a> CompositorNode.js
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">1.33% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>1/75</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>0/50</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>0/13</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">1.33% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>1/75</span>
      </div>
    </div>
  </div>
  <div class='status-line low'></div>
<pre><table class="coverage">
<tr><td class="line-count quiet">1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
116
117
118
119
120
121
122
123
124
125
126
127
128
129
130
131
132
133
134
135
136
137
138
139
140
141
142
143
144
145
146
147
148
149
150
151
152
153
154
155
156
157
158
159
160
161
162
163
164
165
166
167
168
169
170
171
172
173
174
175
176
177
178
179
180
181
182
183
184
185
186
187
188
189
190
191
192
193
194
195
196
197
198
199
200
201
202
203
204
205
206
207
208
209
210
211
212
213
214
215
216
217
218
219
220
221
222
223
224
225
226
227
228
229
230
231
232
233
234
235
236
237
238
239
240
241
242
243
244
245
246
247</td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">import Base from '../core/Base';
&nbsp;
// PENDING
// Use topological sort ?
&nbsp;
/**
 * Node of graph based post processing.
 *
 * @constructor clay.compositor.CompositorNode
 * @extends clay.core.Base
 *
 */
var CompositorNode = Base.extend(function () <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    return /** @lends clay.compositor.CompositorNode# */ {</span>
        /**
         * @type {string}
         */
        name: '',
&nbsp;
        /**
         * Input links, will be updated by the graph
         * @example:
         *     inputName: {
         *         node: someNode,
         *         pin: 'xxxx'
         *     }
         * @type {Object}
         */
        inputLinks: {},
&nbsp;
        /**
         * Output links, will be updated by the graph
         * @example:
         *     outputName: {
         *         node: someNode,
         *         pin: 'xxxx'
         *     }
         * @type {Object}
         */
        outputLinks: {},
&nbsp;
        // Save the output texture of previous frame
        // Will be used when there exist a circular reference
        _prevOutputTextures: {},
        _outputTextures: {},
&nbsp;
        // Example: { name: 2 }
        _outputReferences: {},
&nbsp;
        _rendering: false,
        // If rendered in this frame
        _rendered: false,
&nbsp;
        _compositor: null
    };
},
/** @lends clay.compositor.CompositorNode.prototype */
{
&nbsp;
    // TODO Remove parameter function callback
    updateParameter: function (outputName, renderer) <span class="fstat-no" title="function not covered" >{</span>
        var outputInfo = <span class="cstat-no" title="statement not covered" >this.outputs[outputName];</span>
        var parameters = <span class="cstat-no" title="statement not covered" >outputInfo.parameters;</span>
        var parametersCopy = <span class="cstat-no" title="statement not covered" >outputInfo._parametersCopy;</span>
<span class="cstat-no" title="statement not covered" >        if (!parametersCopy) {</span>
<span class="cstat-no" title="statement not covered" >            parametersCopy = outputInfo._parametersCopy = {};</span>
        }
<span class="cstat-no" title="statement not covered" >        if (parameters) {</span>
<span class="cstat-no" title="statement not covered" >            for (var key in parameters) {</span>
<span class="cstat-no" title="statement not covered" >                if (key !== 'width' &amp;&amp; key !== 'height') {</span>
<span class="cstat-no" title="statement not covered" >                    parametersCopy[key] = parameters[key];</span>
                }
            }
        }
        var width, height;
<span class="cstat-no" title="statement not covered" >        if (parameters.width instanceof Function) {</span>
<span class="cstat-no" title="statement not covered" >            width = parameters.width.call(this, renderer);</span>
        }
        else {
<span class="cstat-no" title="statement not covered" >            width = parameters.width;</span>
        }
<span class="cstat-no" title="statement not covered" >        if (parameters.height instanceof Function) {</span>
<span class="cstat-no" title="statement not covered" >            height = parameters.height.call(this, renderer);</span>
        }
        else {
<span class="cstat-no" title="statement not covered" >            height = parameters.height;</span>
        }
<span class="cstat-no" title="statement not covered" >        if (</span>
            parametersCopy.width !== width
            || parametersCopy.height !== height
        ) {
<span class="cstat-no" title="statement not covered" >            if (this._outputTextures[outputName]) {</span>
<span class="cstat-no" title="statement not covered" >                this._outputTextures[outputName].dispose(renderer.gl);</span>
            }
        }
<span class="cstat-no" title="statement not covered" >        parametersCopy.width = width;</span>
<span class="cstat-no" title="statement not covered" >        parametersCopy.height = height;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        return parametersCopy;</span>
    },
&nbsp;
    /**
     * Set parameter
     * @param {string} name
     * @param {} value
     */
    setParameter: function (name, value) <span class="fstat-no" title="function not covered" >{},</span>
    /**
     * Get parameter value
     * @param  {string} name
     * @return {}
     */
    getParameter: function (name) <span class="fstat-no" title="function not covered" >{},</span>
    /**
     * Set parameters
     * @param {Object} obj
     */
    setParameters: function (obj) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        for (var name in obj) {</span>
<span class="cstat-no" title="statement not covered" >            this.setParameter(name, obj[name]);</span>
        }
    },
&nbsp;
    render: function () <span class="fstat-no" title="function not covered" >{},</span>
&nbsp;
    getOutput: function (renderer /*optional*/, name) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        if (name == null) {</span>
            // Return the output texture without rendering
<span class="cstat-no" title="statement not covered" >            name = renderer;</span>
<span class="cstat-no" title="statement not covered" >            return this._outputTextures[name];</span>
        }
        var outputInfo = <span class="cstat-no" title="statement not covered" >this.outputs[name];</span>
<span class="cstat-no" title="statement not covered" >        if (!outputInfo) {</span>
<span class="cstat-no" title="statement not covered" >            return ;</span>
        }
&nbsp;
        // Already been rendered in this frame
<span class="cstat-no" title="statement not covered" >        if (this._rendered) {</span>
            // Force return texture in last frame
<span class="cstat-no" title="statement not covered" >            if (outputInfo.outputLastFrame) {</span>
<span class="cstat-no" title="statement not covered" >                return this._prevOutputTextures[name];</span>
            }
            else {
<span class="cstat-no" title="statement not covered" >                return this._outputTextures[name];</span>
            }
        }
        else <span class="cstat-no" title="statement not covered" >if (</span>
            // TODO
            this._rendering   // Solve Circular Reference
        ) {
<span class="cstat-no" title="statement not covered" >            if (!this._prevOutputTextures[name]) {</span>
                // Create a blank texture at first pass
<span class="cstat-no" title="statement not covered" >                this._prevOutputTextures[name] = this._compositor.allocateTexture(outputInfo.parameters || {});</span>
            }
<span class="cstat-no" title="statement not covered" >            return this._prevOutputTextures[name];</span>
        }
&nbsp;
<span class="cstat-no" title="statement not covered" >        this.render(renderer);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        return this._outputTextures[name];</span>
    },
&nbsp;
    removeReference: function (outputName) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        this._outputReferences[outputName]--;</span>
<span class="cstat-no" title="statement not covered" >        if (this._outputReferences[outputName] === 0) {</span>
            var outputInfo = <span class="cstat-no" title="statement not covered" >this.outputs[outputName];</span>
<span class="cstat-no" title="statement not covered" >            if (outputInfo.keepLastFrame) {</span>
<span class="cstat-no" title="statement not covered" >                if (this._prevOutputTextures[outputName]) {</span>
<span class="cstat-no" title="statement not covered" >                    this._compositor.releaseTexture(this._prevOutputTextures[outputName]);</span>
                }
<span class="cstat-no" title="statement not covered" >                this._prevOutputTextures[outputName] = this._outputTextures[outputName];</span>
            }
            else {
                // Output of this node have alreay been used by all other nodes
                // Put the texture back to the pool.
<span class="cstat-no" title="statement not covered" >                this._compositor.releaseTexture(this._outputTextures[outputName]);</span>
            }
        }
    },
&nbsp;
    link: function (inputPinName, fromNode, fromPinName) <span class="fstat-no" title="function not covered" >{</span>
&nbsp;
        // The relationship from output pin to input pin is one-on-multiple
<span class="cstat-no" title="statement not covered" >        this.inputLinks[inputPinName] = {</span>
            node: fromNode,
            pin: fromPinName
        };
<span class="cstat-no" title="statement not covered" >        if (!fromNode.outputLinks[fromPinName]) {</span>
<span class="cstat-no" title="statement not covered" >            fromNode.outputLinks[fromPinName] = [];</span>
        }
<span class="cstat-no" title="statement not covered" >        fromNode.outputLinks[fromPinName].push({</span>
            node: this,
            pin: inputPinName
        });
&nbsp;
        // Enabled the pin texture in shader
<span class="cstat-no" title="statement not covered" >        this.pass.material.enableTexture(inputPinName);</span>
    },
&nbsp;
    clear: function () <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        this.inputLinks = {};</span>
<span class="cstat-no" title="statement not covered" >        this.outputLinks = {};</span>
    },
&nbsp;
    updateReference: function (outputName) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        if (!this._rendering) {</span>
<span class="cstat-no" title="statement not covered" >            this._rendering = true;</span>
<span class="cstat-no" title="statement not covered" >            for (var inputName in this.inputLinks) {</span>
                var link = <span class="cstat-no" title="statement not covered" >this.inputLinks[inputName];</span>
<span class="cstat-no" title="statement not covered" >                link.node.updateReference(link.pin);</span>
            }
<span class="cstat-no" title="statement not covered" >            this._rendering = false;</span>
        }
<span class="cstat-no" title="statement not covered" >        if (outputName) {</span>
<span class="cstat-no" title="statement not covered" >            this._outputReferences[outputName] ++;</span>
        }
    },
&nbsp;
    beforeFrame: function () <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        this._rendered = false;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        for (var name in this.outputLinks) {</span>
<span class="cstat-no" title="statement not covered" >            this._outputReferences[name] = 0;</span>
        }
    },
&nbsp;
    afterFrame: function () <span class="fstat-no" title="function not covered" >{</span>
        // Put back all the textures to pool
<span class="cstat-no" title="statement not covered" >        for (var name in this.outputLinks) {</span>
<span class="cstat-no" title="statement not covered" >            if (this._outputReferences[name] &gt; 0) {</span>
                var outputInfo = <span class="cstat-no" title="statement not covered" >this.outputs[name];</span>
<span class="cstat-no" title="statement not covered" >                if (outputInfo.keepLastFrame) {</span>
<span class="cstat-no" title="statement not covered" >                    if (this._prevOutputTextures[name]) {</span>
<span class="cstat-no" title="statement not covered" >                        this._compositor.releaseTexture(this._prevOutputTextures[name]);</span>
                    }
<span class="cstat-no" title="statement not covered" >                    this._prevOutputTextures[name] = this._outputTextures[name];</span>
                }
                else {
<span class="cstat-no" title="statement not covered" >                    this._compositor.releaseTexture(this._outputTextures[name]);</span>
                }
            }
        }
    }
});
&nbsp;
export default CompositorNode;
&nbsp;</pre></td></tr>
</table></pre>
<div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Fri Feb 23 2018 01:09:23 GMT+0800 (CST)
</div>
</div>
<script src="../../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../../sorter.js"></script>
</body>
</html>
