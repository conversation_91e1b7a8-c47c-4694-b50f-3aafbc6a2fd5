<template>
  <el-dialog
    title="安全详情"
    :visible.sync="dialogVisible"
    width="70%"
    :before-close="handleClose"
  >
    <div class="security-detail-content" v-if="recordData">
      <!-- 安全事件概览 -->
      <el-card class="security-overview-card">
        <div slot="header">
          <span>安全事件概览</span>
          <el-tag :type="getSecurityLevelTagType(securityLevel)" style="float: right;">
            {{ getSecurityLevelText(securityLevel) }}
          </el-tag>
        </div>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="overview-item">
              <div class="overview-icon security">
                <i class="el-icon-warning"></i>
              </div>
              <div class="overview-content">
                <div class="overview-title">事件等级</div>
                <div class="overview-value">{{ getSecurityLevelText(securityLevel) }}</div>
              </div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="overview-item">
              <div class="overview-icon time">
                <i class="el-icon-time"></i>
              </div>
              <div class="overview-content">
                <div class="overview-title">发生时间</div>
                <div class="overview-value">{{ formatDateTime(recordData.createTime) }}</div>
              </div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="overview-item">
              <div class="overview-icon status">
                <i class="el-icon-check"></i>
              </div>
              <div class="overview-content">
                <div class="overview-title">处理状态</div>
                <div class="overview-value">{{ getHandleStatusText() }}</div>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 事件详细信息 -->
      <el-card class="event-details-card">
        <div slot="header">
          <span>事件详细信息</span>
        </div>
        
        <el-descriptions :column="2" border>
          <el-descriptions-item label="事件类型">
            <el-tag :type="getRecordTypeTagType(recordData.recordType)">
              {{ getRecordTypeText(recordData.recordType) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="航班信息">
            {{ recordData.flightNumber }} ({{ recordData.origin }} → {{ recordData.destination }})
          </el-descriptions-item>
          <el-descriptions-item label="座位号">
            {{ recordData.seatNumber }}
          </el-descriptions-item>
          <el-descriptions-item label="案件编号" v-if="recordData.caseNumber">
            {{ recordData.caseNumber }}
          </el-descriptions-item>
          <el-descriptions-item label="记录来源">
            {{ recordData.recordSource || '系统记录' }}
          </el-descriptions-item>
          <el-descriptions-item label="风险评估" v-if="recordData.riskAssessment">
            <el-tag :type="getRiskAssessmentTagType(recordData.riskAssessment)">
              {{ recordData.riskAssessment }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
        
        <div class="event-description-section">
          <h4>事件描述</h4>
          <div class="description-content">
            {{ recordData.eventDescription || '无详细描述' }}
          </div>
        </div>
        
        <div class="handle-result-section" v-if="recordData.handleResult">
          <h4>处理结果</h4>
          <div class="result-content">
            {{ recordData.handleResult }}
          </div>
        </div>
      </el-card>

      <!-- 安全评估 -->
      <el-card class="security-assessment-card">
        <div slot="header">
          <span>安全评估</span>
          <el-button size="mini" style="float: right;" @click="refreshAssessment">
            <i class="el-icon-refresh"></i> 重新评估
          </el-button>
        </div>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="assessment-item">
              <h5>威胁等级</h5>
              <div class="threat-level">
                <el-rate
                  v-model="securityAssessment.threatLevel"
                  :max="5"
                  disabled
                  show-score
                  text-color="#ff9900"
                  score-template="{value} 级威胁"
                >
                </el-rate>
              </div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="assessment-item">
              <h5>影响范围</h5>
              <div class="impact-scope">
                <el-tag 
                  v-for="scope in securityAssessment.impactScope" 
                  :key="scope"
                  type="warning"
                  style="margin-right: 8px;"
                >
                  {{ scope }}
                </el-tag>
              </div>
            </div>
          </el-col>
        </el-row>
        
        <div class="risk-factors-section">
          <h5>风险因素</h5>
          <div class="risk-factors-list">
            <div 
              v-for="factor in securityAssessment.riskFactors" 
              :key="factor.name"
              class="risk-factor-item"
            >
              <div class="factor-name">{{ factor.name }}</div>
              <div class="factor-level">
                <el-progress 
                  :percentage="factor.level" 
                  :color="getProgressColor(factor.level)"
                  :show-text="false"
                ></el-progress>
                <span class="level-text">{{ factor.level }}%</span>
              </div>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 处理流程 -->
      <el-card class="process-card">
        <div slot="header">
          <span>处理流程</span>
        </div>
        
        <el-steps :active="currentStep" finish-status="success">
          <el-step
            v-for="(step, index) in processSteps"
            :key="index"
            :title="step.title"
            :description="step.description"
            :status="step.status"
          ></el-step>
        </el-steps>
        
        <div class="process-actions" style="margin-top: 20px;">
          <el-button 
            v-if="currentStep < processSteps.length - 1"
            type="primary" 
            @click="nextStep"
          >
            下一步
          </el-button>
          <el-button @click="viewProcessDetail">查看详细流程</el-button>
        </div>
      </el-card>

      <!-- 相关人员 -->
      <el-card class="related-persons-card">
        <div slot="header">
          <span>相关人员</span>
        </div>
        
        <el-table :data="relatedPersons" style="width: 100%">
          <el-table-column prop="name" label="姓名" width="120"></el-table-column>
          <el-table-column prop="role" label="角色" width="120">
            <template slot-scope="scope">
              <el-tag :type="getRoleTagType(scope.row.role)" size="mini">
                {{ scope.row.role }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="department" label="部门" width="150"></el-table-column>
          <el-table-column prop="contact" label="联系方式"></el-table-column>
          <el-table-column prop="involvement" label="参与情况" show-overflow-tooltip></el-table-column>
          <el-table-column label="操作" width="100">
            <template slot-scope="scope">
              <el-button size="mini" @click="contactPerson(scope.row)">联系</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <!-- 附件和证据 -->
      <el-card class="attachments-card">
        <div slot="header">
          <span>附件和证据</span>
          <el-button size="mini" style="float: right;" @click="uploadAttachment">
            <i class="el-icon-upload"></i> 上传附件
          </el-button>
        </div>
        
        <div class="attachments-list">
          <div 
            v-for="attachment in attachments" 
            :key="attachment.id"
            class="attachment-item"
          >
            <div class="attachment-icon">
              <i :class="getAttachmentIcon(attachment.type)"></i>
            </div>
            <div class="attachment-info">
              <div class="attachment-name">{{ attachment.name }}</div>
              <div class="attachment-meta">
                {{ attachment.size }} | {{ formatDateTime(attachment.uploadTime) }}
              </div>
            </div>
            <div class="attachment-actions">
              <el-button size="mini" @click="previewAttachment(attachment)">预览</el-button>
              <el-button size="mini" @click="downloadAttachment(attachment)">下载</el-button>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关闭</el-button>
      <el-button type="primary" @click="exportSecurityReport">
        <i class="el-icon-download"></i> 导出安全报告
      </el-button>
      <el-button type="warning" @click="escalateEvent">
        <i class="el-icon-warning"></i> 事件升级
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: "SecurityDetailDialog",
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    recordData: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      dialogVisible: false,
      securityLevel: 'medium',
      currentStep: 1,
      securityAssessment: {
        threatLevel: 3,
        impactScope: ['航班安全', '乘客安全', '机场秩序'],
        riskFactors: [
          { name: '人员风险', level: 60 },
          { name: '行为风险', level: 45 },
          { name: '环境风险', level: 30 },
          { name: '时间风险', level: 25 }
        ]
      },
      processSteps: [
        { title: '事件发现', description: '安全事件被发现并记录', status: 'finish' },
        { title: '初步评估', description: '对事件进行初步风险评估', status: 'process' },
        { title: '深入调查', description: '收集证据，深入调查事件', status: 'wait' },
        { title: '处理决策', description: '制定处理方案并执行', status: 'wait' },
        { title: '结案归档', description: '事件处理完毕，归档备案', status: 'wait' }
      ],
      relatedPersons: [
        {
          name: '张安检',
          role: '安检员',
          department: '安检部',
          contact: '13800138001',
          involvement: '发现异常并上报'
        },
        {
          name: '李队长',
          role: '安保队长',
          department: '安保部',
          contact: '13800138002',
          involvement: '现场处置负责人'
        }
      ],
      attachments: [
        {
          id: 1,
          name: '现场照片.jpg',
          type: 'image',
          size: '2.5MB',
          uploadTime: new Date()
        },
        {
          id: 2,
          name: '监控录像.mp4',
          type: 'video',
          size: '15.2MB',
          uploadTime: new Date()
        }
      ]
    }
  },
  computed: {
    
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
      if (val && this.recordData) {
        this.initSecurityData()
      }
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    }
  },
  methods: {
    initSecurityData() {
      // 根据记录类型初始化安全数据
      if (this.recordData.recordType === '2') {
        this.securityLevel = 'high'
        this.currentStep = 2
      } else if (this.recordData.recordType === '3') {
        this.securityLevel = 'medium'
        this.currentStep = 1
      }
    },
    refreshAssessment() {
      this.$message.success('安全评估已更新')
    },
    nextStep() {
      if (this.currentStep < this.processSteps.length - 1) {
        this.currentStep++
        this.processSteps[this.currentStep - 1].status = 'finish'
        this.processSteps[this.currentStep].status = 'process'
        this.$message.success('流程已推进到下一步')
      }
    },
    viewProcessDetail() {
      this.$message.info('查看详细流程功能开发中...')
    },
    contactPerson(person) {
      this.$message.success(`正在联系 ${person.name}...`)
    },
    uploadAttachment() {
      this.$message.info('上传附件功能开发中...')
    },
    previewAttachment(attachment) {
      this.$message.info(`预览 ${attachment.name}`)
    },
    downloadAttachment(attachment) {
      this.$message.success(`下载 ${attachment.name}`)
    },
    exportSecurityReport() {
      this.$message.success('导出安全报告功能开发中...')
    },
    escalateEvent() {
      this.$confirm('确认要升级此安全事件吗？', '事件升级', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$message.success('事件已升级')
      }).catch(() => {
        this.$message.info('已取消升级')
      })
    },
    handleClose() {
      this.dialogVisible = false
    },
    getSecurityLevelText(level) {
      const levels = {
        'low': '低风险',
        'medium': '中风险',
        'high': '高风险'
      }
      return levels[level] || '未知'
    },
    getSecurityLevelTagType(level) {
      const types = {
        'low': 'success',
        'medium': 'warning',
        'high': 'danger'
      }
      return types[level] || 'info'
    },
    getHandleStatusText() {
      return this.recordData.handleResult ? '已处理' : '处理中'
    },
    getRecordTypeText(type) {
      const types = {
        '0': '正常记录',
        '1': '异常行为',
        '2': '安全事件',
        '3': '违规记录'
      }
      return types[type] || '未知'
    },
    getRecordTypeTagType(type) {
      const types = {
        '0': 'success',
        '1': 'warning',
        '2': 'danger',
        '3': 'danger'
      }
      return types[type] || 'info'
    },
    getRiskAssessmentTagType(assessment) {
      if (!assessment) return 'info'
      if (assessment.includes('高')) return 'danger'
      if (assessment.includes('中')) return 'warning'
      return 'success'
    },
    getProgressColor(percentage) {
      if (percentage >= 70) return '#f56c6c'
      if (percentage >= 40) return '#e6a23c'
      return '#67c23a'
    },
    getRoleTagType(role) {
      const types = {
        '安检员': 'primary',
        '安保队长': 'success',
        '调查员': 'warning'
      }
      return types[role] || 'info'
    },
    getAttachmentIcon(type) {
      const icons = {
        'image': 'el-icon-picture',
        'video': 'el-icon-video-camera',
        'document': 'el-icon-document'
      }
      return icons[type] || 'el-icon-document'
    },
    formatDateTime(datetime) {
      if (!datetime) return ''
      return new Date(datetime).toLocaleString()
    }
  }
}
</script>

<style scoped>
.security-detail-content {
  max-height: 80vh;
  overflow-y: auto;
}

.security-overview-card,
.event-details-card,
.security-assessment-card,
.process-card,
.related-persons-card,
.attachments-card {
  margin-bottom: 20px;
}

.overview-item {
  display: flex;
  align-items: center;
  padding: 15px;
  background: #f5f7fa;
  border-radius: 8px;
}

.overview-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
  margin-right: 15px;
}

.overview-icon.security {
  background: linear-gradient(135deg, #f56c6c, #f78989);
}

.overview-icon.time {
  background: linear-gradient(135deg, #409eff, #66b1ff);
}

.overview-icon.status {
  background: linear-gradient(135deg, #67c23a, #85ce61);
}

.overview-content {
  flex: 1;
}

.overview-title {
  font-size: 12px;
  color: #909399;
  margin-bottom: 5px;
}

.overview-value {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.event-description-section,
.handle-result-section {
  margin-top: 20px;
}

.event-description-section h4,
.handle-result-section h4 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 16px;
  border-bottom: 1px solid #e4e7ed;
  padding-bottom: 8px;
}

.description-content,
.result-content {
  padding: 15px;
  background: #f5f7fa;
  border-radius: 4px;
  color: #606266;
  line-height: 1.6;
  min-height: 60px;
}

.assessment-item {
  margin-bottom: 20px;
}

.assessment-item h5 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 14px;
}

.threat-level {
  padding: 10px 0;
}

.impact-scope {
  padding: 10px 0;
}

.risk-factors-section {
  margin-top: 20px;
}

.risk-factors-section h5 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 14px;
  border-bottom: 1px solid #e4e7ed;
  padding-bottom: 8px;
}

.risk-factors-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.risk-factor-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background: #f5f7fa;
  border-radius: 4px;
}

.factor-name {
  font-weight: bold;
  color: #606266;
  min-width: 80px;
}

.factor-level {
  display: flex;
  align-items: center;
  flex: 1;
  margin-left: 20px;
}

.factor-level .el-progress {
  flex: 1;
  margin-right: 10px;
}

.level-text {
  font-size: 12px;
  color: #909399;
  min-width: 35px;
}

.process-actions {
  text-align: center;
}

.attachments-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.attachment-item {
  display: flex;
  align-items: center;
  padding: 15px;
  background: #f5f7fa;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.attachment-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #409eff;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
  margin-right: 15px;
}

.attachment-info {
  flex: 1;
}

.attachment-name {
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.attachment-meta {
  font-size: 12px;
  color: #909399;
}

.attachment-actions {
  display: flex;
  gap: 8px;
}

.dialog-footer {
  text-align: right;
}
</style>
