import request from '@/utils/request'

// 查询乘客历史记录列表
export function listPassengerHistory(query) {
  return request({
    url: '/ky/passengerHistory/list',
    method: 'get',
    params: query
  })
}

// 查询乘客历史记录详细
export function getPassengerHistory(historyId) {
  return request({
    url: '/ky/passengerHistory/' + historyId,
    method: 'get'
  })
}

// 根据乘客ID查询历史记录
export function getHistoryByPassengerId(passengerId) {
  return request({
    url: '/ky/passengerHistory/passenger/' + passengerId,
    method: 'get'
  })
}

// 根据身份证号查询历史记录
export function getHistoryByIdCard(idCard) {
  return request({
    url: '/ky/passengerHistory/idCard/' + idCard,
    method: 'get'
  })
}

// 查询异常记录
export function getAbnormalRecords(query) {
  return request({
    url: '/ky/passengerHistory/abnormal',
    method: 'get',
    params: query
  })
}

// 查询安全事件记录
export function getSecurityEvents(query) {
  return request({
    url: '/ky/passengerHistory/security',
    method: 'get',
    params: query
  })
}

// 获取乘客详细信息（包含历史记录统计）
export function getPassengerDetailInfo(idCard) {
  return request({
    url: '/ky/passengerHistory/detail/' + idCard,
    method: 'get'
  })
}

// 获取乘客风险评估信息
export function getPassengerRiskAssessment(idCard) {
  return request({
    url: '/ky/passengerHistory/risk/' + idCard,
    method: 'get'
  })
}

// 统计乘客出行次数
export function getTravelCount(idCard) {
  return request({
    url: '/ky/passengerHistory/travelCount/' + idCard,
    method: 'get'
  })
}

// 统计乘客异常记录次数
export function getAbnormalCount(idCard) {
  return request({
    url: '/ky/passengerHistory/abnormalCount/' + idCard,
    method: 'get'
  })
}

// 新增乘客历史记录
export function addPassengerHistory(data) {
  return request({
    url: '/ky/passengerHistory',
    method: 'post',
    data: data
  })
}

// 修改乘客历史记录
export function updatePassengerHistory(data) {
  return request({
    url: '/ky/passengerHistory',
    method: 'put',
    data: data
  })
}

// 删除乘客历史记录
export function delPassengerHistory(historyIds) {
  return request({
    url: '/ky/passengerHistory/' + historyIds,
    method: 'delete'
  })
}
