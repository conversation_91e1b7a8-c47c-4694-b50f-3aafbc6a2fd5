<template>
  <el-dialog
    title="乘客详细信息"
    :visible.sync="dialogVisible"
    width="90%"
    :before-close="handleClose"
    class="passenger-detail-modal"
  >
    <div class="passenger-detail-content" v-if="passengerData">
      <!-- 乘客基本信息卡片 -->
      <el-row :gutter="20">
        <el-col :span="8">
          <el-card class="passenger-card">
            <div class="passenger-header">
              <div class="passenger-avatar-large">
                <i :class="getPassengerIcon(passengerData)"></i>
                <div v-if="passengerData.isKeyPerson === '1'" class="key-person-badge-large">
                  <i class="el-icon-warning"></i>
                </div>
              </div>
              <div class="passenger-basic">
                <h3>{{ passengerData.passengerName }}</h3>
                <p>{{ passengerData.seatNumber }} | {{ getCabinClassText(passengerData.cabinClass) }}</p>
                <el-tag :type="getStatusTagType(passengerData.ticketStatus)">
                  {{ getStatusText(passengerData.ticketStatus) }}
                </el-tag>
              </div>
            </div>
            
            <div class="passenger-info-grid">
              <div class="info-item">
                <label>身份证号:</label>
                <span>{{ passengerData.idCard }}</span>
              </div>
              <div class="info-item">
                <label>性别:</label>
                <span>{{ getGenderText(passengerData.gender) }}</span>
              </div>
              <div class="info-item">
                <label>年龄:</label>
                <span>{{ passengerData.age }}岁</span>
              </div>
              <div class="info-item">
                <label>国籍:</label>
                <span>{{ passengerData.nationality }}</span>
              </div>
              <div class="info-item">
                <label>联系电话:</label>
                <span>{{ passengerData.phone }}</span>
              </div>
              <div class="info-item" v-if="passengerData.emergencyContact">
                <label>紧急联系人:</label>
                <span>{{ passengerData.emergencyContact }}</span>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <!-- 重点人员信息 -->
        <el-col :span="8" v-if="passengerData.isKeyPerson === '1'">
          <el-card class="key-person-card">
            <div slot="header">
              <span>重点人员信息</span>
              <el-tag type="danger" style="float: right;">
                {{ getKeyPersonTypeText(passengerData.keyPersonType) }}
              </el-tag>
            </div>
            
            <div class="key-person-info">
              <div class="risk-level-display">
                <div class="risk-circle" :class="getRiskLevelClass(passengerData.riskLevel)">
                  <span class="risk-text">{{ getRiskLevelText(passengerData.riskLevel) }}</span>
                </div>
              </div>
              
              <div class="key-person-details">
                <div class="detail-item">
                  <label>人员类型:</label>
                  <el-tag :type="getKeyPersonTypeTagType(passengerData.keyPersonType)">
                    {{ getKeyPersonTypeText(passengerData.keyPersonType) }}
                  </el-tag>
                </div>
                <div class="detail-item">
                  <label>风险等级:</label>
                  <el-tag :type="getRiskLevelTagType(passengerData.riskLevel)">
                    {{ getRiskLevelText(passengerData.riskLevel) }}
                  </el-tag>
                </div>
                <div class="detail-item" v-if="passengerData.remark">
                  <label>备注信息:</label>
                  <span>{{ passengerData.remark }}</span>
                </div>
              </div>
              
              <div class="action-buttons">
                <el-button size="small" @click="loadRiskAssessment">
                  <i class="el-icon-document"></i> 风险评估
                </el-button>
                <el-button size="small" @click="viewSecurityRecords">
                  <i class="el-icon-warning"></i> 安全记录
                </el-button>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <!-- 航班信息 -->
        <el-col :span="8">
          <el-card class="flight-info-card">
            <div slot="header">
              <span>航班信息</span>
            </div>
            
            <div class="flight-details">
              <div class="flight-route">
                <div class="route-item">
                  <i class="el-icon-takeoff-landing"></i>
                  <span>{{ flightInfo.origin }}</span>
                </div>
                <div class="route-arrow">
                  <i class="el-icon-right"></i>
                </div>
                <div class="route-item">
                  <i class="el-icon-takeoff-landing"></i>
                  <span>{{ flightInfo.destination }}</span>
                </div>
              </div>
              
              <div class="flight-time-info">
                <div class="time-item">
                  <label>计划起飞:</label>
                  <span>{{ formatDateTime(flightInfo.scheduledDeparture) }}</span>
                </div>
                <div class="time-item">
                  <label>计划到达:</label>
                  <span>{{ formatDateTime(flightInfo.scheduledArrival) }}</span>
                </div>
                <div class="time-item" v-if="passengerData.checkinTime">
                  <label>值机时间:</label>
                  <span>{{ formatDateTime(passengerData.checkinTime) }}</span>
                </div>
                <div class="time-item" v-if="passengerData.boardingTime">
                  <label>登机时间:</label>
                  <span>{{ formatDateTime(passengerData.boardingTime) }}</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
      
      <!-- 历史记录和统计信息 -->
      <el-row :gutter="20" style="margin-top: 20px;">
        <el-col :span="24">
          <el-card>
            <div slot="header">
              <span>历史记录与统计</span>
              <el-button size="mini" style="float: right;" @click="refreshHistoryData">
                <i class="el-icon-refresh"></i> 刷新
              </el-button>
            </div>
            
            <div v-if="historyLoading" class="loading-container">
              <el-loading-spinner></el-loading-spinner>
              <span>加载历史数据中...</span>
            </div>
            
            <div v-else>
              <!-- 统计概览 -->
              <div class="history-stats">
                <el-row :gutter="20">
                  <el-col :span="6">
                    <div class="stat-box">
                      <div class="stat-number">{{ historyStats.totalTravelTimes || 0 }}</div>
                      <div class="stat-label">总出行次数</div>
                    </div>
                  </el-col>
                  <el-col :span="6">
                    <div class="stat-box">
                      <div class="stat-number">{{ historyStats.abnormalRecords || 0 }}</div>
                      <div class="stat-label">异常记录</div>
                    </div>
                  </el-col>
                  <el-col :span="6">
                    <div class="stat-box">
                      <div class="stat-number">{{ getRecentTravelDays() }}</div>
                      <div class="stat-label">最近出行(天前)</div>
                    </div>
                  </el-col>
                  <el-col :span="6">
                    <div class="stat-box">
                      <div class="stat-number">{{ getRiskScore() }}</div>
                      <div class="stat-label">风险评分</div>
                    </div>
                  </el-col>
                </el-row>
              </div>
              
              <!-- 历史记录表格 -->
              <div class="history-table">
                <el-table :data="historyRecords" style="width: 100%" max-height="400">
                  <el-table-column prop="flightDate" label="日期" width="100">
                    <template slot-scope="scope">
                      {{ formatDate(scope.row.flightDate) }}
                    </template>
                  </el-table-column>
                  <el-table-column prop="flightNumber" label="航班号" width="100"></el-table-column>
                  <el-table-column prop="origin" label="起点" width="80"></el-table-column>
                  <el-table-column prop="destination" label="终点" width="80"></el-table-column>
                  <el-table-column prop="seatNumber" label="座位" width="80"></el-table-column>
                  <el-table-column prop="recordType" label="记录类型" width="100">
                    <template slot-scope="scope">
                      <el-tag :type="getRecordTypeTagType(scope.row.recordType)" size="mini">
                        {{ getRecordTypeText(scope.row.recordType) }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="eventDescription" label="事件描述" show-overflow-tooltip></el-table-column>
                  <el-table-column prop="handleResult" label="处理结果" width="120" show-overflow-tooltip></el-table-column>
                  <el-table-column label="操作" width="100">
                    <template slot-scope="scope">
                      <el-button size="mini" @click="viewRecordDetail(scope.row)">详情</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关闭</el-button>
      <el-button type="primary" @click="exportPassengerData">
        <i class="el-icon-download"></i> 导出数据
      </el-button>
      <el-button type="warning" v-if="passengerData.isKeyPerson === '1'" @click="reportSecurity">
        <i class="el-icon-warning"></i> 安全上报
      </el-button>
    </div>
    
    <!-- 风险评估对话框 -->
    <risk-assessment-dialog
      :visible.sync="riskDialogVisible"
      :passenger-data="passengerData"
    />
    
    <!-- 记录详情对话框 -->
    <record-detail-dialog
      :visible.sync="recordDialogVisible"
      :record-data="selectedRecord"
    />
  </el-dialog>
</template>

<script>
import { getPassengerDetailInfo, getPassengerRiskAssessment } from "@/api/ky/passengerHistory"
import { getFlightInfo } from "@/api/ky/flight"
import RiskAssessmentDialog from "./RiskAssessmentDialog"
import RecordDetailDialog from "./RecordDetailDialog"

export default {
  name: "PassengerDetailModal",
  components: {
    RiskAssessmentDialog,
    RecordDetailDialog
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    passengerData: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      dialogVisible: false,
      flightInfo: {},
      historyRecords: [],
      historyStats: {},
      riskAssessment: null,
      historyLoading: false,
      riskDialogVisible: false,
      recordDialogVisible: false,
      selectedRecord: null
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
      if (val && this.passengerData) {
        this.loadFlightInfo()
        this.loadHistoryData()
      }
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    }
  },
  methods: {
    async loadFlightInfo() {
      if (!this.passengerData.flightId) return
      try {
        const response = await getFlightInfo(this.passengerData.flightId)
        this.flightInfo = response.data
      } catch (error) {
        console.error('加载航班信息失败:', error)
      }
    },
    async loadHistoryData() {
      if (!this.passengerData.idCard) return
      
      this.historyLoading = true
      try {
        const response = await getPassengerDetailInfo(this.passengerData.idCard)
        const data = response.data
        this.historyRecords = data.historyList || []
        this.historyStats = {
          totalTravelTimes: data.totalTravelTimes || 0,
          abnormalRecords: data.abnormalRecords || 0
        }
      } catch (error) {
        console.error('加载历史数据失败:', error)
        this.$message.error('加载历史数据失败')
      } finally {
        this.historyLoading = false
      }
    },
    async refreshHistoryData() {
      await this.loadHistoryData()
      this.$message.success('历史数据已刷新')
    },
    async loadRiskAssessment() {
      this.riskDialogVisible = true
    },
    viewSecurityRecords() {
      // 筛选安全记录
      const securityRecords = this.historyRecords.filter(record => 
        record.recordType === '2' || record.recordType === '3'
      )
      if (securityRecords.length === 0) {
        this.$message.info('该乘客暂无安全记录')
        return
      }
      // 可以打开一个专门的安全记录对话框
      this.$message.success(`找到 ${securityRecords.length} 条安全记录`)
    },
    viewRecordDetail(record) {
      this.selectedRecord = record
      this.recordDialogVisible = true
    },
    handleClose() {
      this.dialogVisible = false
      this.resetData()
    },
    resetData() {
      this.flightInfo = {}
      this.historyRecords = []
      this.historyStats = {}
      this.riskAssessment = null
    },
    exportPassengerData() {
      this.$message.success('导出功能开发中...')
    },
    reportSecurity() {
      this.$message.success('安全上报功能开发中...')
    },
    getRecentTravelDays() {
      if (this.historyRecords.length === 0) return '-'
      const lastTravel = new Date(this.historyRecords[0].flightDate)
      const now = new Date()
      const diffTime = Math.abs(now - lastTravel)
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
      return diffDays
    },
    getRiskScore() {
      if (!this.riskAssessment) return '-'
      return this.riskAssessment.riskScore || 0
    },
    // 工具方法
    getPassengerIcon(passenger) {
      return passenger.gender === '0' ? 'el-icon-female' : 'el-icon-male'
    },
    getGenderText(gender) {
      return gender === '1' ? '男' : gender === '0' ? '女' : '未知'
    },
    getCabinClassText(cabinClass) {
      const classes = { '0': '经济舱', '1': '商务舱', '2': '头等舱' }
      return classes[cabinClass] || '未知'
    },
    getStatusText(status) {
      const statuses = { '0': '已订票', '1': '已值机', '2': '已登机', '3': '已起飞' }
      return statuses[status] || '未知'
    },
    getStatusTagType(status) {
      const types = { '0': 'info', '1': 'warning', '2': 'success', '3': 'primary' }
      return types[status] || 'info'
    },
    getKeyPersonTypeText(type) {
      const types = { '1': '涉恐', '2': '涉毒', '3': '涉黑', '4': '逃犯', '5': '精神病', '6': '其他危险', '7': 'VIP' }
      return types[type] || '重点'
    },
    getKeyPersonTypeTagType(type) {
      const types = { '1': 'danger', '2': 'danger', '3': 'danger', '4': 'danger', '5': 'warning', '6': 'warning', '7': 'success' }
      return types[type] || 'info'
    },
    getRiskLevelText(level) {
      const levels = { '1': '低风险', '2': '中风险', '3': '高风险' }
      return levels[level] || '未知'
    },
    getRiskLevelTagType(level) {
      const types = { '1': 'success', '2': 'warning', '3': 'danger' }
      return types[level] || 'info'
    },
    getRiskLevelClass(level) {
      const classes = { '1': 'low-risk', '2': 'medium-risk', '3': 'high-risk' }
      return classes[level] || 'low-risk'
    },
    getRecordTypeText(type) {
      const types = { '0': '正常记录', '1': '异常行为', '2': '安全事件', '3': '违规记录' }
      return types[type] || '未知'
    },
    getRecordTypeTagType(type) {
      const types = { '0': 'success', '1': 'warning', '2': 'danger', '3': 'danger' }
      return types[type] || 'info'
    },
    formatDate(date) {
      if (!date) return ''
      return new Date(date).toLocaleDateString()
    },
    formatDateTime(datetime) {
      if (!datetime) return ''
      return new Date(datetime).toLocaleString()
    }
  }
}
</script>

<style scoped>
.passenger-detail-modal {
  .el-dialog__body {
    padding: 20px;
  }
}

.passenger-detail-content {
  max-height: 80vh;
  overflow-y: auto;
}

.passenger-card {
  height: 100%;
}

.passenger-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.passenger-avatar-large {
  position: relative;
  margin-right: 20px;
}

.passenger-avatar-large i {
  font-size: 64px;
  color: #409eff;
}

.key-person-badge-large {
  position: absolute;
  top: -5px;
  right: -5px;
  width: 24px;
  height: 24px;
  background: #f56c6c;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 14px;
}

.passenger-basic h3 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
}

.passenger-basic p {
  margin: 0 0 10px 0;
  color: #606266;
  font-size: 14px;
}

.passenger-info-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;
}

.info-item {
  display: flex;
  align-items: center;
}

.info-item label {
  font-weight: bold;
  color: #606266;
  min-width: 80px;
  margin-right: 10px;
}

.key-person-card {
  height: 100%;
  border: 2px solid #f56c6c;
}

.key-person-info {
  text-align: center;
}

.risk-level-display {
  margin-bottom: 20px;
}

.risk-circle {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  font-weight: bold;
  color: white;
}

.risk-circle.low-risk {
  background: linear-gradient(135deg, #67c23a, #85ce61);
}

.risk-circle.medium-risk {
  background: linear-gradient(135deg, #e6a23c, #f0a020);
}

.risk-circle.high-risk {
  background: linear-gradient(135deg, #f56c6c, #f78989);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(245, 108, 108, 0.7);
  }
  70% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(245, 108, 108, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(245, 108, 108, 0);
  }
}

.risk-text {
  font-size: 12px;
}

.key-person-details {
  margin-bottom: 20px;
}

.detail-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10px;
}

.detail-item label {
  font-weight: bold;
  color: #606266;
}

.action-buttons {
  display: flex;
  gap: 10px;
  justify-content: center;
}

.flight-info-card {
  height: 100%;
}

.flight-details {
  text-align: center;
}

.flight-route {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
}

.route-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.route-item i {
  font-size: 24px;
  color: #409eff;
  margin-bottom: 5px;
}

.route-arrow {
  margin: 0 20px;
  font-size: 20px;
  color: #909399;
}

.flight-time-info {
  text-align: left;
}

.time-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.time-item label {
  font-weight: bold;
  color: #606266;
}

.loading-container {
  text-align: center;
  padding: 40px;
  color: #909399;
}

.history-stats {
  margin-bottom: 20px;
  padding: 20px;
  background: #f5f7fa;
  border-radius: 8px;
}

.stat-box {
  text-align: center;
  padding: 15px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 12px;
  color: #909399;
}

.history-table {
  margin-top: 20px;
}

.dialog-footer {
  text-align: right;
}
</style>
