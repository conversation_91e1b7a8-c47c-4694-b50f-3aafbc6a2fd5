package com.ruoyi.ky.controller;

import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.ky.domain.PassengerHistory;
import com.ruoyi.ky.service.IPassengerHistoryService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 乘客历史记录Controller
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@RestController
@RequestMapping("/ky/passengerHistory")
public class PassengerHistoryController extends BaseController
{
    @Autowired
    private IPassengerHistoryService passengerHistoryService;

    /**
     * 查询乘客历史记录列表
     */
    @PreAuthorize("@ss.hasPermi('ky:passengerHistory:list')")
    @GetMapping("/list")
    public TableDataInfo list(PassengerHistory passengerHistory)
    {
        startPage();
        List<PassengerHistory> list = passengerHistoryService.selectPassengerHistoryList(passengerHistory);
        return getDataTable(list);
    }

    /**
     * 根据乘客ID查询历史记录
     */
    @PreAuthorize("@ss.hasPermi('ky:passengerHistory:list')")
    @GetMapping("/passenger/{passengerId}")
    public AjaxResult getHistoryByPassengerId(@PathVariable("passengerId") Long passengerId)
    {
        List<PassengerHistory> list = passengerHistoryService.selectHistoryByPassengerId(passengerId);
        return AjaxResult.success(list);
    }

    /**
     * 根据身份证号查询历史记录
     */
    @PreAuthorize("@ss.hasPermi('ky:passengerHistory:list')")
    @GetMapping("/idCard/{idCard}")
    public AjaxResult getHistoryByIdCard(@PathVariable("idCard") String idCard)
    {
        List<PassengerHistory> list = passengerHistoryService.selectHistoryByIdCard(idCard);
        return AjaxResult.success(list);
    }

    /**
     * 查询异常记录
     */
    @PreAuthorize("@ss.hasPermi('ky:passengerHistory:list')")
    @GetMapping("/abnormal")
    public TableDataInfo getAbnormalRecords(PassengerHistory passengerHistory)
    {
        startPage();
        List<PassengerHistory> list = passengerHistoryService.selectAbnormalRecords(passengerHistory);
        return getDataTable(list);
    }

    /**
     * 查询安全事件记录
     */
    @PreAuthorize("@ss.hasPermi('ky:passengerHistory:list')")
    @GetMapping("/security")
    public TableDataInfo getSecurityEvents(PassengerHistory passengerHistory)
    {
        startPage();
        List<PassengerHistory> list = passengerHistoryService.selectSecurityEvents(passengerHistory);
        return getDataTable(list);
    }

    /**
     * 获取乘客详细信息（包含历史记录统计）
     */
    @PreAuthorize("@ss.hasPermi('ky:passengerHistory:query')")
    @GetMapping("/detail/{idCard}")
    public AjaxResult getPassengerDetailInfo(@PathVariable("idCard") String idCard)
    {
        Map<String, Object> detailInfo = passengerHistoryService.getPassengerDetailInfo(idCard);
        return AjaxResult.success(detailInfo);
    }

    /**
     * 获取乘客风险评估信息
     */
    @PreAuthorize("@ss.hasPermi('ky:passengerHistory:query')")
    @GetMapping("/risk/{idCard}")
    public AjaxResult getPassengerRiskAssessment(@PathVariable("idCard") String idCard)
    {
        Map<String, Object> riskAssessment = passengerHistoryService.getPassengerRiskAssessment(idCard);
        return AjaxResult.success(riskAssessment);
    }

    /**
     * 统计乘客出行次数
     */
    @PreAuthorize("@ss.hasPermi('ky:passengerHistory:query')")
    @GetMapping("/travelCount/{idCard}")
    public AjaxResult getTravelCount(@PathVariable("idCard") String idCard)
    {
        int count = passengerHistoryService.countTravelTimes(idCard);
        return AjaxResult.success(count);
    }

    /**
     * 统计乘客异常记录次数
     */
    @PreAuthorize("@ss.hasPermi('ky:passengerHistory:query')")
    @GetMapping("/abnormalCount/{idCard}")
    public AjaxResult getAbnormalCount(@PathVariable("idCard") String idCard)
    {
        int count = passengerHistoryService.countAbnormalRecords(idCard);
        return AjaxResult.success(count);
    }

    /**
     * 导出乘客历史记录列表
     */
    @PreAuthorize("@ss.hasPermi('ky:passengerHistory:export')")
    @Log(title = "乘客历史记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PassengerHistory passengerHistory)
    {
        List<PassengerHistory> list = passengerHistoryService.selectPassengerHistoryList(passengerHistory);
        ExcelUtil<PassengerHistory> util = new ExcelUtil<PassengerHistory>(PassengerHistory.class);
        util.exportExcel(response, list, "乘客历史记录数据");
    }

    /**
     * 获取乘客历史记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('ky:passengerHistory:query')")
    @GetMapping(value = "/{historyId}")
    public AjaxResult getInfo(@PathVariable("historyId") Long historyId)
    {
        return AjaxResult.success(passengerHistoryService.selectPassengerHistoryByHistoryId(historyId));
    }

    /**
     * 新增乘客历史记录
     */
    @PreAuthorize("@ss.hasPermi('ky:passengerHistory:add')")
    @Log(title = "乘客历史记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PassengerHistory passengerHistory)
    {
        return toAjax(passengerHistoryService.insertPassengerHistory(passengerHistory));
    }

    /**
     * 修改乘客历史记录
     */
    @PreAuthorize("@ss.hasPermi('ky:passengerHistory:edit')")
    @Log(title = "乘客历史记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PassengerHistory passengerHistory)
    {
        return toAjax(passengerHistoryService.updatePassengerHistory(passengerHistory));
    }

    /**
     * 删除乘客历史记录
     */
    @PreAuthorize("@ss.hasPermi('ky:passengerHistory:remove')")
    @Log(title = "乘客历史记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{historyIds}")
    public AjaxResult remove(@PathVariable Long[] historyIds)
    {
        return toAjax(passengerHistoryService.deletePassengerHistoryByHistoryIds(historyIds));
    }
}
