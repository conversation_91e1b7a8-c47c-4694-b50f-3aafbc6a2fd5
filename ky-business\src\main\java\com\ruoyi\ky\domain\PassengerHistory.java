package com.ruoyi.ky.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 乘客历史记录对象 ky_passenger_history
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class PassengerHistory extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 历史记录ID */
    private Long historyId;

    /** 乘客ID */
    @Excel(name = "乘客ID")
    private Long passengerId;

    /** 身份证号 */
    @Excel(name = "身份证号")
    private String idCard;

    /** 乘客姓名 */
    @Excel(name = "乘客姓名")
    private String passengerName;

    /** 航班号 */
    @Excel(name = "航班号")
    private String flightNumber;

    /** 始发地 */
    @Excel(name = "始发地")
    private String origin;

    /** 目的地 */
    @Excel(name = "目的地")
    private String destination;

    /** 飞行日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "飞行日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date flightDate;

    /** 座位号 */
    @Excel(name = "座位号")
    private String seatNumber;

    /** 舱位等级 */
    @Excel(name = "舱位等级")
    private String cabinClass;

    /** 记录类型（0正常出行 1异常行为 2安全事件 3违规记录） */
    @Excel(name = "记录类型", readConverterExp = "0=正常出行,1=异常行为,2=安全事件,3=违规记录")
    private String recordType;

    /** 事件描述 */
    @Excel(name = "事件描述")
    private String eventDescription;

    /** 处理结果 */
    @Excel(name = "处理结果")
    private String handleResult;

    /** 风险评估 */
    @Excel(name = "风险评估")
    private String riskAssessment;

    /** 关联案件号 */
    @Excel(name = "关联案件号")
    private String caseNumber;

    /** 记录来源（0系统自动 1人工录入 2第三方接口） */
    @Excel(name = "记录来源", readConverterExp = "0=系统自动,1=人工录入,2=第三方接口")
    private String recordSource;

    /** 是否已核实（0未核实 1已核实） */
    @Excel(name = "是否已核实", readConverterExp = "0=未核实,1=已核实")
    private String isVerified;

    /** 核实人员 */
    @Excel(name = "核实人员")
    private String verifyPerson;

    /** 核实时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "核实时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date verifyTime;

    public void setHistoryId(Long historyId) 
    {
        this.historyId = historyId;
    }

    public Long getHistoryId() 
    {
        return historyId;
    }
    public void setPassengerId(Long passengerId) 
    {
        this.passengerId = passengerId;
    }

    public Long getPassengerId() 
    {
        return passengerId;
    }
    public void setIdCard(String idCard) 
    {
        this.idCard = idCard;
    }

    public String getIdCard() 
    {
        return idCard;
    }
    public void setPassengerName(String passengerName) 
    {
        this.passengerName = passengerName;
    }

    public String getPassengerName() 
    {
        return passengerName;
    }
    public void setFlightNumber(String flightNumber) 
    {
        this.flightNumber = flightNumber;
    }

    public String getFlightNumber() 
    {
        return flightNumber;
    }
    public void setOrigin(String origin) 
    {
        this.origin = origin;
    }

    public String getOrigin() 
    {
        return origin;
    }
    public void setDestination(String destination) 
    {
        this.destination = destination;
    }

    public String getDestination() 
    {
        return destination;
    }
    public void setFlightDate(Date flightDate) 
    {
        this.flightDate = flightDate;
    }

    public Date getFlightDate() 
    {
        return flightDate;
    }
    public void setSeatNumber(String seatNumber) 
    {
        this.seatNumber = seatNumber;
    }

    public String getSeatNumber() 
    {
        return seatNumber;
    }
    public void setCabinClass(String cabinClass) 
    {
        this.cabinClass = cabinClass;
    }

    public String getCabinClass() 
    {
        return cabinClass;
    }
    public void setRecordType(String recordType) 
    {
        this.recordType = recordType;
    }

    public String getRecordType() 
    {
        return recordType;
    }
    public void setEventDescription(String eventDescription) 
    {
        this.eventDescription = eventDescription;
    }

    public String getEventDescription() 
    {
        return eventDescription;
    }
    public void setHandleResult(String handleResult) 
    {
        this.handleResult = handleResult;
    }

    public String getHandleResult() 
    {
        return handleResult;
    }
    public void setRiskAssessment(String riskAssessment) 
    {
        this.riskAssessment = riskAssessment;
    }

    public String getRiskAssessment() 
    {
        return riskAssessment;
    }
    public void setCaseNumber(String caseNumber) 
    {
        this.caseNumber = caseNumber;
    }

    public String getCaseNumber() 
    {
        return caseNumber;
    }
    public void setRecordSource(String recordSource) 
    {
        this.recordSource = recordSource;
    }

    public String getRecordSource() 
    {
        return recordSource;
    }
    public void setIsVerified(String isVerified) 
    {
        this.isVerified = isVerified;
    }

    public String getIsVerified() 
    {
        return isVerified;
    }
    public void setVerifyPerson(String verifyPerson) 
    {
        this.verifyPerson = verifyPerson;
    }

    public String getVerifyPerson() 
    {
        return verifyPerson;
    }
    public void setVerifyTime(Date verifyTime) 
    {
        this.verifyTime = verifyTime;
    }

    public Date getVerifyTime() 
    {
        return verifyTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("historyId", getHistoryId())
            .append("passengerId", getPassengerId())
            .append("idCard", getIdCard())
            .append("passengerName", getPassengerName())
            .append("flightNumber", getFlightNumber())
            .append("origin", getOrigin())
            .append("destination", getDestination())
            .append("flightDate", getFlightDate())
            .append("seatNumber", getSeatNumber())
            .append("cabinClass", getCabinClass())
            .append("recordType", getRecordType())
            .append("eventDescription", getEventDescription())
            .append("handleResult", getHandleResult())
            .append("riskAssessment", getRiskAssessment())
            .append("caseNumber", getCaseNumber())
            .append("recordSource", getRecordSource())
            .append("isVerified", getIsVerified())
            .append("verifyPerson", getVerifyPerson())
            .append("verifyTime", getVerifyTime())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
