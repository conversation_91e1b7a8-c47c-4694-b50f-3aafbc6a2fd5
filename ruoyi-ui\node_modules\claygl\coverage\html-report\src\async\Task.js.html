<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for src/async/Task.js</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../../index.html">all files</a> / <a href="index.html">src/async/</a> Task.js
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">23.26% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>10/43</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>0/12</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>0/12</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">23.26% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>10/43</span>
      </div>
    </div>
  </div>
  <div class='status-line low'></div>
<pre><table class="coverage">
<tr><td class="line-count quiet">1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
116
117
118
119</td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">import notifier from '../core/mixin/notifier';
import request from '../core/request';
import util  from '../core/util';
&nbsp;
/**
 * @constructor
 * @alias clay.async.Task
 * @mixes clay.core.mixin.notifier
 */
var Task = function() <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    this._fullfilled = false;</span>
<span class="cstat-no" title="statement not covered" >    this._rejected = false;</span>
};
/**
 * Task successed
 * @param {} data
 */
Task.prototype.resolve = function(data) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    this._fullfilled = true;</span>
<span class="cstat-no" title="statement not covered" >    this._rejected = false;</span>
<span class="cstat-no" title="statement not covered" >    this.trigger('success', data);</span>
};
/**
 * Task failed
 * @param {} err
 */
Task.prototype.reject = function(err) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    this._rejected = true;</span>
<span class="cstat-no" title="statement not covered" >    this._fullfilled = false;</span>
<span class="cstat-no" title="statement not covered" >    this.trigger('error', err);</span>
};
/**
 * If task successed
 * @return {boolean}
 */
Task.prototype.isFullfilled = function() <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    return this._fullfilled;</span>
};
/**
 * If task failed
 * @return {boolean}
 */
Task.prototype.isRejected = function() <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    return this._rejected;</span>
};
/**
 * If task finished, either successed or failed
 * @return {boolean}
 */
Task.prototype.isSettled = function() <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    return this._fullfilled || this._rejected;</span>
};
&nbsp;
util.extend(Task.prototype, notifier);
&nbsp;
function makeRequestTask(url, responseType) <span class="fstat-no" title="function not covered" >{</span>
    var task = <span class="cstat-no" title="statement not covered" >new Task();</span>
<span class="cstat-no" title="statement not covered" >    request.get({</span>
        url: url,
        responseType: responseType,
        onload: function(res) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >            task.resolve(res);</span>
        },
        onerror: function(error) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >            task.reject(error);</span>
        }
    });
<span class="cstat-no" title="statement not covered" >    return task;</span>
}
/**
 * Make a request task
 * @param  {string|object|object[]|string[]} url
 * @param  {string} [responseType]
 * @example
 *     var task = Task.makeRequestTask('./a.json');
 *     var task = Task.makeRequestTask({
 *         url: 'b.bin',
 *         responseType: 'arraybuffer'
 *     });
 *     var tasks = Task.makeRequestTask(['./a.json', './b.json']);
 *     var tasks = Task.makeRequestTask([
 *         {url: 'a.json'},
 *         {url: 'b.bin', responseType: 'arraybuffer'}
 *     ]);
 * @return {clay.async.Task|clay.async.Task[]}
 */
Task.makeRequestTask = function(url, responseType) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    if (typeof url === 'string') {</span>
<span class="cstat-no" title="statement not covered" >        return makeRequestTask(url, responseType);</span>
    } else <span class="cstat-no" title="statement not covered" >if (url.url) {   //  Configure object</span>
        var obj = <span class="cstat-no" title="statement not covered" >url;</span>
<span class="cstat-no" title="statement not covered" >        return makeRequestTask(obj.url, obj.responseType);</span>
    } else <span class="cstat-no" title="statement not covered" >if (Array.isArray(url)) {  // Url list</span>
        var urlList = <span class="cstat-no" title="statement not covered" >url;</span>
        var tasks = <span class="cstat-no" title="statement not covered" >[];</span>
<span class="cstat-no" title="statement not covered" >        urlList.forEach(function(obj) <span class="fstat-no" title="function not covered" >{</span></span>
            var url, responseType;
<span class="cstat-no" title="statement not covered" >            if (typeof obj === 'string') {</span>
<span class="cstat-no" title="statement not covered" >                url = obj;</span>
            } else <span class="cstat-no" title="statement not covered" >if (Object(obj) === obj) {</span>
<span class="cstat-no" title="statement not covered" >                url = obj.url;</span>
<span class="cstat-no" title="statement not covered" >                responseType = obj.responseType;</span>
            }
<span class="cstat-no" title="statement not covered" >            tasks.push(makeRequestTask(url, responseType));</span>
        });
<span class="cstat-no" title="statement not covered" >        return tasks;</span>
    }
};
/**
 * @return {clay.async.Task}
 */
Task.makeTask = function() <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    return new Task();</span>
};
&nbsp;
util.extend(Task.prototype, notifier);
&nbsp;
export default Task;
&nbsp;</pre></td></tr>
</table></pre>
<div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Fri Feb 23 2018 01:09:23 GMT+0800 (CST)
</div>
</div>
<script src="../../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../../sorter.js"></script>
</body>
</html>
