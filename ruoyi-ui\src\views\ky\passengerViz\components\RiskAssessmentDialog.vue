<template>
  <el-dialog
    title="风险评估详情"
    :visible.sync="dialogVisible"
    width="70%"
    :before-close="handleClose"
  >
    <div class="risk-assessment-content" v-if="passengerData">
      <div v-if="loading" class="loading-container">
        <el-loading-spinner></el-loading-spinner>
        <span>正在进行风险评估...</span>
      </div>
      
      <div v-else>
        <!-- 风险评估概览 -->
        <div class="risk-overview">
          <el-card>
            <div class="risk-header">
              <div class="passenger-info">
                <h3>{{ passengerData.passengerName }}</h3>
                <p>{{ passengerData.idCard }}</p>
              </div>
              <div class="risk-score-display">
                <div class="score-circle" :class="getRiskScoreClass(riskData.riskScore)">
                  <div class="score-value">{{ riskData.riskScore || 0 }}</div>
                  <div class="score-label">风险评分</div>
                </div>
              </div>
              <div class="risk-level-display">
                <el-tag :type="getRiskLevelTagType(riskData.riskLevel)" size="large">
                  {{ getRiskLevelText(riskData.riskLevel) }}
                </el-tag>
              </div>
            </div>
          </el-card>
        </div>

        <!-- 风险因素分析 -->
        <div class="risk-factors">
          <el-card>
            <div slot="header">
              <span>风险因素分析</span>
            </div>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <div class="factor-category">
                  <h4>个人信息风险</h4>
                  <div class="factor-items">
                    <div class="factor-item" v-for="factor in personalRiskFactors" :key="factor.name">
                      <div class="factor-name">{{ factor.name }}</div>
                      <div class="factor-score">
                        <el-progress 
                          :percentage="factor.score" 
                          :color="getProgressColor(factor.score)"
                          :show-text="false"
                        ></el-progress>
                        <span class="score-text">{{ factor.score }}%</span>
                      </div>
                    </div>
                  </div>
                </div>
              </el-col>
              
              <el-col :span="12">
                <div class="factor-category">
                  <h4>行为风险</h4>
                  <div class="factor-items">
                    <div class="factor-item" v-for="factor in behaviorRiskFactors" :key="factor.name">
                      <div class="factor-name">{{ factor.name }}</div>
                      <div class="factor-score">
                        <el-progress 
                          :percentage="factor.score" 
                          :color="getProgressColor(factor.score)"
                          :show-text="false"
                        ></el-progress>
                        <span class="score-text">{{ factor.score }}%</span>
                      </div>
                    </div>
                  </div>
                </div>
              </el-col>
            </el-row>
          </el-card>
        </div>

        <!-- 历史记录分析 -->
        <div class="history-analysis">
          <el-card>
            <div slot="header">
              <span>历史记录分析</span>
            </div>
            
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="analysis-item">
                  <div class="analysis-icon normal">
                    <i class="el-icon-check"></i>
                  </div>
                  <div class="analysis-content">
                    <div class="analysis-value">{{ historyAnalysis.normalRecords || 0 }}</div>
                    <div class="analysis-label">正常记录</div>
                  </div>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="analysis-item">
                  <div class="analysis-icon warning">
                    <i class="el-icon-warning"></i>
                  </div>
                  <div class="analysis-content">
                    <div class="analysis-value">{{ historyAnalysis.abnormalRecords || 0 }}</div>
                    <div class="analysis-label">异常记录</div>
                  </div>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="analysis-item">
                  <div class="analysis-icon danger">
                    <i class="el-icon-close"></i>
                  </div>
                  <div class="analysis-content">
                    <div class="analysis-value">{{ historyAnalysis.securityEvents || 0 }}</div>
                    <div class="analysis-label">安全事件</div>
                  </div>
                </div>
              </el-col>
            </el-row>
            
            <div class="trend-chart" style="margin-top: 20px;">
              <div class="chart-title">风险趋势分析</div>
              <div id="riskTrendChart" style="height: 300px;"></div>
            </div>
          </el-card>
        </div>

        <!-- 风险建议 -->
        <div class="risk-recommendations">
          <el-card>
            <div slot="header">
              <span>风险建议</span>
            </div>
            
            <div class="recommendations-list">
              <div 
                v-for="(recommendation, index) in riskData.recommendations" 
                :key="index"
                class="recommendation-item"
                :class="getRecommendationClass(recommendation.level)"
              >
                <div class="recommendation-icon">
                  <i :class="getRecommendationIcon(recommendation.level)"></i>
                </div>
                <div class="recommendation-content">
                  <div class="recommendation-title">{{ recommendation.title }}</div>
                  <div class="recommendation-description">{{ recommendation.description }}</div>
                </div>
                <div class="recommendation-level">
                  <el-tag :type="getRecommendationTagType(recommendation.level)" size="mini">
                    {{ getRecommendationLevelText(recommendation.level) }}
                  </el-tag>
                </div>
              </div>
            </div>
          </el-card>
        </div>

        <!-- 评估详情 -->
        <div class="assessment-details">
          <el-card>
            <div slot="header">
              <span>评估详情</span>
            </div>
            
            <el-descriptions :column="2" border>
              <el-descriptions-item label="评估时间">
                {{ formatDateTime(riskData.assessmentTime) }}
              </el-descriptions-item>
              <el-descriptions-item label="评估模型">
                {{ riskData.assessmentModel || 'AI智能评估模型 v2.0' }}
              </el-descriptions-item>
              <el-descriptions-item label="数据来源">
                {{ riskData.dataSources || '历史记录、行为分析、第三方数据' }}
              </el-descriptions-item>
              <el-descriptions-item label="置信度">
                <el-progress 
                  :percentage="riskData.confidence || 85" 
                  :color="getProgressColor(riskData.confidence || 85)"
                ></el-progress>
              </el-descriptions-item>
              <el-descriptions-item label="有效期">
                {{ riskData.validUntil || '30天' }}
              </el-descriptions-item>
              <el-descriptions-item label="评估人员">
                {{ riskData.assessor || '系统自动评估' }}
              </el-descriptions-item>
            </el-descriptions>
          </el-card>
        </div>
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关闭</el-button>
      <el-button type="primary" @click="refreshAssessment">
        <i class="el-icon-refresh"></i> 重新评估
      </el-button>
      <el-button type="warning" @click="exportAssessment">
        <i class="el-icon-download"></i> 导出报告
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getPassengerRiskAssessment } from "@/api/ky/passengerHistory"
import * as echarts from 'echarts'

export default {
  name: "RiskAssessmentDialog",
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    passengerData: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      dialogVisible: false,
      loading: false,
      riskData: {},
      historyAnalysis: {},
      personalRiskFactors: [
        { name: '身份信息', score: 20 },
        { name: '年龄因素', score: 15 },
        { name: '国籍风险', score: 10 },
        { name: '职业背景', score: 25 }
      ],
      behaviorRiskFactors: [
        { name: '出行频率', score: 30 },
        { name: '路线偏好', score: 20 },
        { name: '异常行为', score: 60 },
        { name: '社交关系', score: 35 }
      ],
      riskChart: null
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
      if (val && this.passengerData) {
        this.loadRiskAssessment()
      }
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    }
  },
  methods: {
    async loadRiskAssessment() {
      if (!this.passengerData.idCard) return
      
      this.loading = true
      try {
        const response = await getPassengerRiskAssessment(this.passengerData.idCard)
        this.riskData = response.data || this.generateMockRiskData()
        this.historyAnalysis = this.riskData.historyAnalysis || {}
        
        this.$nextTick(() => {
          this.renderRiskTrendChart()
        })
      } catch (error) {
        console.error('加载风险评估失败:', error)
        this.riskData = this.generateMockRiskData()
        this.$nextTick(() => {
          this.renderRiskTrendChart()
        })
      } finally {
        this.loading = false
      }
    },
    generateMockRiskData() {
      // 生成模拟风险评估数据
      const riskScore = this.passengerData.isKeyPerson === '1' ? 
        (this.passengerData.riskLevel === '3' ? 85 : this.passengerData.riskLevel === '2' ? 60 : 35) : 25
      
      return {
        riskScore: riskScore,
        riskLevel: this.passengerData.riskLevel || '1',
        assessmentTime: new Date(),
        confidence: 85,
        recommendations: [
          {
            level: riskScore > 70 ? 'high' : riskScore > 40 ? 'medium' : 'low',
            title: riskScore > 70 ? '加强安检' : riskScore > 40 ? '重点关注' : '正常处理',
            description: riskScore > 70 ? '建议进行详细安检和身份核实' : riskScore > 40 ? '建议关注该乘客行为' : '按正常流程处理'
          }
        ],
        historyAnalysis: {
          normalRecords: Math.floor(Math.random() * 20) + 5,
          abnormalRecords: this.passengerData.isKeyPerson === '1' ? Math.floor(Math.random() * 5) + 1 : 0,
          securityEvents: this.passengerData.isKeyPerson === '1' ? Math.floor(Math.random() * 3) : 0
        }
      }
    },
    renderRiskTrendChart() {
      const chart = echarts.init(document.getElementById('riskTrendChart'))
      
      // 生成模拟趋势数据
      const dates = []
      const scores = []
      for (let i = 6; i >= 0; i--) {
        const date = new Date()
        date.setMonth(date.getMonth() - i)
        dates.push(date.toLocaleDateString())
        scores.push(Math.floor(Math.random() * 30) + (this.riskData.riskScore - 15))
      }
      
      const option = {
        tooltip: { trigger: 'axis' },
        xAxis: {
          type: 'category',
          data: dates
        },
        yAxis: {
          type: 'value',
          min: 0,
          max: 100
        },
        series: [{
          name: '风险评分',
          type: 'line',
          data: scores,
          smooth: true,
          itemStyle: { color: '#f56c6c' },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0, y: 0, x2: 0, y2: 1,
              colorStops: [
                { offset: 0, color: 'rgba(245, 108, 108, 0.3)' },
                { offset: 1, color: 'rgba(245, 108, 108, 0.1)' }
              ]
            }
          }
        }]
      }
      
      chart.setOption(option)
      this.riskChart = chart
    },
    async refreshAssessment() {
      await this.loadRiskAssessment()
      this.$message.success('风险评估已更新')
    },
    exportAssessment() {
      this.$message.success('导出功能开发中...')
    },
    handleClose() {
      this.dialogVisible = false
      if (this.riskChart) {
        this.riskChart.dispose()
        this.riskChart = null
      }
    },
    getRiskScoreClass(score) {
      if (score >= 70) return 'high-risk'
      if (score >= 40) return 'medium-risk'
      return 'low-risk'
    },
    getRiskLevelText(level) {
      const levels = { '1': '低风险', '2': '中风险', '3': '高风险' }
      return levels[level] || '未知'
    },
    getRiskLevelTagType(level) {
      const types = { '1': 'success', '2': 'warning', '3': 'danger' }
      return types[level] || 'info'
    },
    getProgressColor(percentage) {
      if (percentage >= 70) return '#f56c6c'
      if (percentage >= 40) return '#e6a23c'
      return '#67c23a'
    },
    getRecommendationClass(level) {
      return `recommendation-${level}`
    },
    getRecommendationIcon(level) {
      const icons = {
        'high': 'el-icon-warning',
        'medium': 'el-icon-info',
        'low': 'el-icon-check'
      }
      return icons[level] || 'el-icon-info'
    },
    getRecommendationTagType(level) {
      const types = {
        'high': 'danger',
        'medium': 'warning',
        'low': 'success'
      }
      return types[level] || 'info'
    },
    getRecommendationLevelText(level) {
      const texts = {
        'high': '高优先级',
        'medium': '中优先级',
        'low': '低优先级'
      }
      return texts[level] || '普通'
    },
    formatDateTime(datetime) {
      if (!datetime) return ''
      return new Date(datetime).toLocaleString()
    }
  },
  beforeDestroy() {
    if (this.riskChart) {
      this.riskChart.dispose()
    }
  }
}
</script>

<style scoped>
.risk-assessment-content {
  max-height: 80vh;
  overflow-y: auto;
}

.loading-container {
  text-align: center;
  padding: 60px;
  color: #909399;
}

.risk-overview {
  margin-bottom: 20px;
}

.risk-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.passenger-info h3 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 20px;
}

.passenger-info p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.risk-score-display {
  text-align: center;
}

.score-circle {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
}

.score-circle.low-risk {
  background: linear-gradient(135deg, #67c23a, #85ce61);
}

.score-circle.medium-risk {
  background: linear-gradient(135deg, #e6a23c, #f0a020);
}

.score-circle.high-risk {
  background: linear-gradient(135deg, #f56c6c, #f78989);
  animation: pulse 2s infinite;
}

.score-value {
  font-size: 28px;
  margin-bottom: 5px;
}

.score-label {
  font-size: 12px;
}

.risk-level-display {
  text-align: center;
}

.risk-factors {
  margin-bottom: 20px;
}

.factor-category h4 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
  border-bottom: 2px solid #409eff;
  padding-bottom: 8px;
}

.factor-items {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.factor-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.factor-name {
  font-weight: bold;
  color: #606266;
  min-width: 80px;
}

.factor-score {
  display: flex;
  align-items: center;
  flex: 1;
  margin-left: 20px;
}

.factor-score .el-progress {
  flex: 1;
  margin-right: 10px;
}

.score-text {
  font-size: 12px;
  color: #909399;
  min-width: 35px;
}

.history-analysis {
  margin-bottom: 20px;
}

.analysis-item {
  display: flex;
  align-items: center;
  padding: 20px;
  background: #f5f7fa;
  border-radius: 8px;
  text-align: center;
}

.analysis-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
  margin-right: 15px;
}

.analysis-icon.normal {
  background: linear-gradient(135deg, #67c23a, #85ce61);
}

.analysis-icon.warning {
  background: linear-gradient(135deg, #e6a23c, #f0a020);
}

.analysis-icon.danger {
  background: linear-gradient(135deg, #f56c6c, #f78989);
}

.analysis-content {
  flex: 1;
}

.analysis-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.analysis-label {
  font-size: 12px;
  color: #909399;
}

.chart-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 15px;
  text-align: center;
}

.risk-recommendations {
  margin-bottom: 20px;
}

.recommendations-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.recommendation-item {
  display: flex;
  align-items: center;
  padding: 15px;
  border-radius: 8px;
  border-left: 4px solid #409eff;
}

.recommendation-item.recommendation-high {
  background: #fef0f0;
  border-left-color: #f56c6c;
}

.recommendation-item.recommendation-medium {
  background: #fdf6ec;
  border-left-color: #e6a23c;
}

.recommendation-item.recommendation-low {
  background: #f0f9ff;
  border-left-color: #67c23a;
}

.recommendation-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: white;
  margin-right: 15px;
  background: #409eff;
}

.recommendation-content {
  flex: 1;
}

.recommendation-title {
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.recommendation-description {
  color: #606266;
  font-size: 14px;
}

.recommendation-level {
  margin-left: 15px;
}

.assessment-details {
  margin-bottom: 20px;
}

.dialog-footer {
  text-align: right;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(245, 108, 108, 0.7);
  }
  70% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(245, 108, 108, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(245, 108, 108, 0);
  }
}
</style>
