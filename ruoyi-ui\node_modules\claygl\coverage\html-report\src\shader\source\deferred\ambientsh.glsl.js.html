<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for src/shader/source/deferred/ambientsh.glsl.js</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../../prettify.css" />
    <link rel="stylesheet" href="../../../../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../../../../index.html">all files</a> / <a href="index.html">src/shader/source/deferred/</a> ambientsh.glsl.js
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">100% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>1/1</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">100% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>0/0</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">100% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>0/0</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">100% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>1/1</span>
      </div>
    </div>
  </div>
  <div class='status-line high'></div>
<pre><table class="coverage">
<tr><td class="line-count quiet">1
2</td><td class="line-coverage quiet"><span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">export default "@export clay.deferred.ambient_sh_light\nuniform sampler2D gBufferTexture1;\nuniform sampler2D gBufferTexture3;\nuniform vec3 lightColor;\nuniform vec3 lightCoefficients[9];\nuniform vec2 windowSize: WINDOW_SIZE;\nvec3 calcAmbientSHLight(vec3 N) {\n    return lightCoefficients[0]\n        + lightCoefficients[1] * N.x\n        + lightCoefficients[2] * N.y\n        + lightCoefficients[3] * N.z\n        + lightCoefficients[4] * N.x * N.z\n        + lightCoefficients[5] * N.z * N.y\n        + lightCoefficients[6] * N.y * N.x\n        + lightCoefficients[7] * (3.0 * N.z * N.z - 1.0)\n        + lightCoefficients[8] * (N.x * N.x - N.y * N.y);\n}\nvoid main()\n{\n    vec2 uv = gl_FragCoord.xy / windowSize;\n    vec4 texel1 = texture2D(gBufferTexture1, uv);\n    if (dot(texel1.rgb, vec3(1.0)) == 0.0) {\n        discard;\n    }\n    vec3 N = texel1.rgb * 2.0 - 1.0;\n    vec3 albedo = texture2D(gBufferTexture3, uv).rgb;\n    gl_FragColor.rgb = lightColor * albedo * calcAmbientSHLight(N);\n    gl_FragColor.a = 1.0;\n}\n@end";
&nbsp;</pre></td></tr>
</table></pre>
<div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Fri Feb 23 2018 01:09:23 GMT+0800 (CST)
</div>
</div>
<script src="../../../../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../../../../sorter.js"></script>
</body>
</html>
