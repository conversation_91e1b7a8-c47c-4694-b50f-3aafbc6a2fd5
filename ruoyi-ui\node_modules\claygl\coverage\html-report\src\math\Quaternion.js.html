<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for src/math/Quaternion.js</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../../index.html">all files</a> / <a href="index.html">src/math/</a> Quaternion.js
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">23.11% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>55/238</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">44.44% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>8/18</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">9.38% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>6/64</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">23.11% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>55/238</span>
      </div>
    </div>
  </div>
  <div class='status-line low'></div>
<pre><table class="coverage">
<tr><td class="line-count quiet">1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
116
117
118
119
120
121
122
123
124
125
126
127
128
129
130
131
132
133
134
135
136
137
138
139
140
141
142
143
144
145
146
147
148
149
150
151
152
153
154
155
156
157
158
159
160
161
162
163
164
165
166
167
168
169
170
171
172
173
174
175
176
177
178
179
180
181
182
183
184
185
186
187
188
189
190
191
192
193
194
195
196
197
198
199
200
201
202
203
204
205
206
207
208
209
210
211
212
213
214
215
216
217
218
219
220
221
222
223
224
225
226
227
228
229
230
231
232
233
234
235
236
237
238
239
240
241
242
243
244
245
246
247
248
249
250
251
252
253
254
255
256
257
258
259
260
261
262
263
264
265
266
267
268
269
270
271
272
273
274
275
276
277
278
279
280
281
282
283
284
285
286
287
288
289
290
291
292
293
294
295
296
297
298
299
300
301
302
303
304
305
306
307
308
309
310
311
312
313
314
315
316
317
318
319
320
321
322
323
324
325
326
327
328
329
330
331
332
333
334
335
336
337
338
339
340
341
342
343
344
345
346
347
348
349
350
351
352
353
354
355
356
357
358
359
360
361
362
363
364
365
366
367
368
369
370
371
372
373
374
375
376
377
378
379
380
381
382
383
384
385
386
387
388
389
390
391
392
393
394
395
396
397
398
399
400
401
402
403
404
405
406
407
408
409
410
411
412
413
414
415
416
417
418
419
420
421
422
423
424
425
426
427
428
429
430
431
432
433
434
435
436
437
438
439
440
441
442
443
444
445
446
447
448
449
450
451
452
453
454
455
456
457
458
459
460
461
462
463
464
465
466
467
468
469
470
471
472
473
474
475
476
477
478
479
480
481
482
483
484
485
486
487
488
489
490
491
492
493
494
495
496
497
498
499
500
501
502
503
504
505
506
507
508
509
510
511
512
513
514
515
516
517
518
519
520
521
522
523
524
525
526
527
528
529
530
531
532
533
534
535
536
537
538
539
540
541
542
543
544
545
546
547
548
549
550
551
552
553
554
555
556
557
558
559
560
561
562
563
564
565
566
567
568
569
570
571
572
573
574
575
576
577
578
579
580
581
582
583
584
585
586
587
588
589
590
591
592
593
594
595
596
597
598
599
600
601
602
603
604
605
606
607
608
609
610
611
612
613
614
615
616
617
618
619
620
621
622
623
624
625
626
627
628
629
630
631
632
633
634
635
636
637
638
639
640
641
642
643
644
645
646
647
648
649
650
651
652
653
654
655
656
657
658
659
660
661
662
663
664
665
666
667
668
669
670
671
672
673
674
675
676
677
678
679
680
681
682
683
684
685
686
687
688
689
690
691
692
693
694
695
696
697
698
699
700
701
702
703
704
705
706
707
708
709
710
711
712
713
714
715
716
717
718
719
720
721
722
723
724
725
726
727
728
729
730
731
732
733
734
735
736
737
738
739
740
741
742
743
744
745
746
747
748
749
750
751
752
753
754
755
756
757
758
759
760
761
762
763
764
765
766
767
768
769
770
771
772
773
774
775
776</td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">161×</span>
<span class="cline-any cline-yes">161×</span>
<span class="cline-any cline-yes">161×</span>
<span class="cline-any cline-yes">161×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">161×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">161×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">5×</span>
<span class="cline-any cline-yes">5×</span>
<span class="cline-any cline-yes">5×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">18×</span>
<span class="cline-any cline-yes">18×</span>
<span class="cline-any cline-yes">18×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">19×</span>
<span class="cline-any cline-yes">19×</span>
<span class="cline-any cline-yes">19×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">18×</span>
<span class="cline-any cline-yes">18×</span>
<span class="cline-any cline-yes">18×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">import glMatrix from '../dep/glmatrix';
var quat = glMatrix.quat;
&nbsp;
/**
 * @constructor
 * @alias clay.Quaternion
 * @param {number} x
 * @param {number} y
 * @param {number} z
 * @param {number} w
 */
var Quaternion = function (x, y, z, w) {
&nbsp;
    x = x || 0;
    y = y || 0;
    z = z || 0;
    w = w === undefined ? 1 : <span class="branch-1 cbranch-no" title="branch not covered" >w;</span>
&nbsp;
    /**
     * Storage of Quaternion, read and write of x, y, z, w will change the values in array
     * All methods also operate on the array instead of x, y, z, w components
     * @name array
     * @type {Float32Array}
     * @memberOf clay.Quaternion#
     */
    this.array = quat.fromValues(x, y, z, w);
&nbsp;
    /**
     * Dirty flag is used by the Node to determine
     * if the matrix is updated to latest
     * @name _dirty
     * @type {boolean}
     * @memberOf clay.Quaternion#
     */
    this._dirty = true;
};
&nbsp;
Quaternion.prototype = {
&nbsp;
    constructor: Quaternion,
&nbsp;
    /**
     * Add b to self
     * @param  {clay.Quaternion} b
     * @return {clay.Quaternion}
     */
    add: function (b) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        quat.add(this.array, this.array, b.array);</span>
<span class="cstat-no" title="statement not covered" >        this._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    },
&nbsp;
    /**
     * Calculate the w component from x, y, z component
     * @return {clay.Quaternion}
     */
    calculateW: function () <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        quat.calculateW(this.array, this.array);</span>
<span class="cstat-no" title="statement not covered" >        this._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    },
&nbsp;
    /**
     * Set x, y and z components
     * @param  {number}  x
     * @param  {number}  y
     * @param  {number}  z
     * @param  {number}  w
     * @return {clay.Quaternion}
     */
    set: function (x, y, z, w) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        this.array[0] = x;</span>
<span class="cstat-no" title="statement not covered" >        this.array[1] = y;</span>
<span class="cstat-no" title="statement not covered" >        this.array[2] = z;</span>
<span class="cstat-no" title="statement not covered" >        this.array[3] = w;</span>
<span class="cstat-no" title="statement not covered" >        this._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    },
&nbsp;
    /**
     * Set x, y, z and w components from array
     * @param  {Float32Array|number[]} arr
     * @return {clay.Quaternion}
     */
    setArray: function (arr) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        this.array[0] = arr[0];</span>
<span class="cstat-no" title="statement not covered" >        this.array[1] = arr[1];</span>
<span class="cstat-no" title="statement not covered" >        this.array[2] = arr[2];</span>
<span class="cstat-no" title="statement not covered" >        this.array[3] = arr[3];</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        this._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    },
&nbsp;
    /**
     * Clone a new Quaternion
     * @return {clay.Quaternion}
     */
    clone: function () <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        return new Quaternion(this.x, this.y, this.z, this.w);</span>
    },
&nbsp;
    /**
     * Calculates the conjugate of self If the quaternion is normalized,
     * this function is faster than invert and produces the same result.
     *
     * @return {clay.Quaternion}
     */
    conjugate: function () <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        quat.conjugate(this.array, this.array);</span>
<span class="cstat-no" title="statement not covered" >        this._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    },
&nbsp;
    /**
     * Copy from b
     * @param  {clay.Quaternion} b
     * @return {clay.Quaternion}
     */
    copy: function (b) {
        quat.copy(this.array, b.array);
        this._dirty = true;
        return this;
    },
&nbsp;
    /**
     * Dot product of self and b
     * @param  {clay.Quaternion} b
     * @return {number}
     */
    dot: function (b) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        return quat.dot(this.array, b.array);</span>
    },
&nbsp;
    /**
     * Set from the given 3x3 rotation matrix
     * @param  {clay.Matrix3} m
     * @return {clay.Quaternion}
     */
    fromMat3: function (m) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        quat.fromMat3(this.array, m.array);</span>
<span class="cstat-no" title="statement not covered" >        this._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    },
&nbsp;
    /**
     * Set from the given 4x4 rotation matrix
     * The 4th column and 4th row will be droped
     * @param  {clay.Matrix4} m
     * @return {clay.Quaternion}
     */
    fromMat4: (function () {
        var mat3 = glMatrix.mat3;
        var m3 = mat3.create();
        return function (m) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >            mat3.fromMat4(m3, m.array);</span>
            // TODO Not like mat4, mat3 in glmatrix seems to be row-based
<span class="cstat-no" title="statement not covered" >            mat3.transpose(m3, m3);</span>
<span class="cstat-no" title="statement not covered" >            quat.fromMat3(this.array, m3);</span>
<span class="cstat-no" title="statement not covered" >            this._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >            return this;</span>
        };
    })(),
&nbsp;
    /**
     * Set to identity quaternion
     * @return {clay.Quaternion}
     */
    identity: function () {
        quat.identity(this.array);
        this._dirty = true;
        return this;
    },
    /**
     * Invert self
     * @return {clay.Quaternion}
     */
    invert: function () <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        quat.invert(this.array, this.array);</span>
<span class="cstat-no" title="statement not covered" >        this._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    },
    /**
     * Alias of length
     * @return {number}
     */
    len: function () <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        return quat.len(this.array);</span>
    },
&nbsp;
    /**
     * Calculate the length
     * @return {number}
     */
    length: function () <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        return quat.length(this.array);</span>
    },
&nbsp;
    /**
     * Linear interpolation between a and b
     * @param  {clay.Quaternion} a
     * @param  {clay.Quaternion} b
     * @param  {number}  t
     * @return {clay.Quaternion}
     */
    lerp: function (a, b, t) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        quat.lerp(this.array, a.array, b.array, t);</span>
<span class="cstat-no" title="statement not covered" >        this._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    },
&nbsp;
    /**
     * Alias for multiply
     * @param  {clay.Quaternion} b
     * @return {clay.Quaternion}
     */
    mul: function (b) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        quat.mul(this.array, this.array, b.array);</span>
<span class="cstat-no" title="statement not covered" >        this._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    },
&nbsp;
    /**
     * Alias for multiplyLeft
     * @param  {clay.Quaternion} a
     * @return {clay.Quaternion}
     */
    mulLeft: function (a) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        quat.multiply(this.array, a.array, this.array);</span>
<span class="cstat-no" title="statement not covered" >        this._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    },
&nbsp;
    /**
     * Mutiply self and b
     * @param  {clay.Quaternion} b
     * @return {clay.Quaternion}
     */
    multiply: function (b) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        quat.multiply(this.array, this.array, b.array);</span>
<span class="cstat-no" title="statement not covered" >        this._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    },
&nbsp;
    /**
     * Mutiply a and self
     * Quaternion mutiply is not commutative, so the result of mutiplyLeft is different with multiply.
     * @param  {clay.Quaternion} a
     * @return {clay.Quaternion}
     */
    multiplyLeft: function (a) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        quat.multiply(this.array, a.array, this.array);</span>
<span class="cstat-no" title="statement not covered" >        this._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    },
&nbsp;
    /**
     * Normalize self
     * @return {clay.Quaternion}
     */
    normalize: function () <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        quat.normalize(this.array, this.array);</span>
<span class="cstat-no" title="statement not covered" >        this._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    },
&nbsp;
    /**
     * Rotate self by a given radian about X axis
     * @param {number} rad
     * @return {clay.Quaternion}
     */
    rotateX: function (rad) {
        quat.rotateX(this.array, this.array, rad);
        this._dirty = true;
        return this;
    },
&nbsp;
    /**
     * Rotate self by a given radian about Y axis
     * @param {number} rad
     * @return {clay.Quaternion}
     */
    rotateY: function (rad) {
        quat.rotateY(this.array, this.array, rad);
        this._dirty = true;
        return this;
    },
&nbsp;
    /**
     * Rotate self by a given radian about Z axis
     * @param {number} rad
     * @return {clay.Quaternion}
     */
    rotateZ: function (rad) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        quat.rotateZ(this.array, this.array, rad);</span>
<span class="cstat-no" title="statement not covered" >        this._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    },
&nbsp;
    /**
     * Sets self to represent the shortest rotation from Vector3 a to Vector3 b.
     * a and b needs to be normalized
     * @param  {clay.Vector3} a
     * @param  {clay.Vector3} b
     * @return {clay.Quaternion}
     */
    rotationTo: function (a, b) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        quat.rotationTo(this.array, a.array, b.array);</span>
<span class="cstat-no" title="statement not covered" >        this._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    },
    /**
     * Sets self with values corresponding to the given axes
     * @param {clay.Vector3} view
     * @param {clay.Vector3} right
     * @param {clay.Vector3} up
     * @return {clay.Quaternion}
     */
    setAxes: function (view, right, up) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        quat.setAxes(this.array, view.array, right.array, up.array);</span>
<span class="cstat-no" title="statement not covered" >        this._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    },
&nbsp;
    /**
     * Sets self with a rotation axis and rotation angle
     * @param {clay.Vector3} axis
     * @param {number} rad
     * @return {clay.Quaternion}
     */
    setAxisAngle: function (axis, rad) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        quat.setAxisAngle(this.array, axis.array, rad);</span>
<span class="cstat-no" title="statement not covered" >        this._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    },
    /**
     * Perform spherical linear interpolation between a and b
     * @param  {clay.Quaternion} a
     * @param  {clay.Quaternion} b
     * @param  {number} t
     * @return {clay.Quaternion}
     */
    slerp: function (a, b, t) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        quat.slerp(this.array, a.array, b.array, t);</span>
<span class="cstat-no" title="statement not covered" >        this._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    },
&nbsp;
    /**
     * Alias for squaredLength
     * @return {number}
     */
    sqrLen: function () <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        return quat.sqrLen(this.array);</span>
    },
&nbsp;
    /**
     * Squared length of self
     * @return {number}
     */
    squaredLength: function () <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        return quat.squaredLength(this.array);</span>
    },
&nbsp;
    /**
     * Set from euler
     * @param {clay.Vector3} v
     * @param {String} order
     */
    fromEuler: function (v, order) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        return Quaternion.fromEuler(this, v, order);</span>
    },
&nbsp;
    toString: function () <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        return '[' + Array.prototype.join.call(this.array, ',') + ']';</span>
    },
&nbsp;
    toArray: function () <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        return Array.prototype.slice.call(this.array);</span>
    }
};
&nbsp;
var defineProperty = Object.defineProperty;
// Getter and Setter
<span class="missing-if-branch" title="else path not taken" >E</span>if (defineProperty) {
&nbsp;
    var proto = Quaternion.prototype;
    /**
     * @name x
     * @type {number}
     * @memberOf clay.Quaternion
     * @instance
     */
    defineProperty(proto, 'x', {
        get: function () <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >            return this.array[0];</span>
        },
        set: function (value) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >            this.array[0] = value;</span>
<span class="cstat-no" title="statement not covered" >            this._dirty = true;</span>
        }
    });
&nbsp;
    /**
     * @name y
     * @type {number}
     * @memberOf clay.Quaternion
     * @instance
     */
    defineProperty(proto, 'y', {
        get: function () <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >            return this.array[1];</span>
        },
        set: function (value) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >            this.array[1] = value;</span>
<span class="cstat-no" title="statement not covered" >            this._dirty = true;</span>
        }
    });
&nbsp;
    /**
     * @name z
     * @type {number}
     * @memberOf clay.Quaternion
     * @instance
     */
    defineProperty(proto, 'z', {
        get: function () <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >            return this.array[2];</span>
        },
        set: function (value) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >            this.array[2] = value;</span>
<span class="cstat-no" title="statement not covered" >            this._dirty = true;</span>
        }
    });
&nbsp;
    /**
     * @name w
     * @type {number}
     * @memberOf clay.Quaternion
     * @instance
     */
    defineProperty(proto, 'w', {
        get: function () <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >            return this.array[3];</span>
        },
        set: function (value) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >            this.array[3] = value;</span>
<span class="cstat-no" title="statement not covered" >            this._dirty = true;</span>
        }
    });
}
&nbsp;
// Supply methods that are not in place
&nbsp;
/**
 * @param  {clay.Quaternion} out
 * @param  {clay.Quaternion} a
 * @param  {clay.Quaternion} b
 * @return {clay.Quaternion}
 */
Quaternion.add = function (out, a, b) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    quat.add(out.array, a.array, b.array);</span>
<span class="cstat-no" title="statement not covered" >    out._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >    return out;</span>
};
&nbsp;
/**
 * @param  {clay.Quaternion} out
 * @param  {number}     x
 * @param  {number}     y
 * @param  {number}     z
 * @param  {number}     w
 * @return {clay.Quaternion}
 */
Quaternion.set = function (out, x, y, z, w) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    quat.set(out.array, x, y, z, w);</span>
<span class="cstat-no" title="statement not covered" >    out._dirty = true;</span>
};
&nbsp;
/**
 * @param  {clay.Quaternion} out
 * @param  {clay.Quaternion} b
 * @return {clay.Quaternion}
 */
Quaternion.copy = function (out, b) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    quat.copy(out.array, b.array);</span>
<span class="cstat-no" title="statement not covered" >    out._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >    return out;</span>
};
&nbsp;
/**
 * @param  {clay.Quaternion} out
 * @param  {clay.Quaternion} a
 * @return {clay.Quaternion}
 */
Quaternion.calculateW = function (out, a) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    quat.calculateW(out.array, a.array);</span>
<span class="cstat-no" title="statement not covered" >    out._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >    return out;</span>
};
&nbsp;
/**
 * @param  {clay.Quaternion} out
 * @param  {clay.Quaternion} a
 * @return {clay.Quaternion}
 */
Quaternion.conjugate = function (out, a) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    quat.conjugate(out.array, a.array);</span>
<span class="cstat-no" title="statement not covered" >    out._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >    return out;</span>
};
&nbsp;
/**
 * @param  {clay.Quaternion} out
 * @return {clay.Quaternion}
 */
Quaternion.identity = function (out) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    quat.identity(out.array);</span>
<span class="cstat-no" title="statement not covered" >    out._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >    return out;</span>
};
&nbsp;
/**
 * @param  {clay.Quaternion} out
 * @param  {clay.Quaternion} a
 * @return {clay.Quaternion}
 */
Quaternion.invert = function (out, a) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    quat.invert(out.array, a.array);</span>
<span class="cstat-no" title="statement not covered" >    out._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >    return out;</span>
};
&nbsp;
/**
 * @param  {clay.Quaternion} a
 * @param  {clay.Quaternion} b
 * @return {number}
 */
Quaternion.dot = function (a, b) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    return quat.dot(a.array, b.array);</span>
};
&nbsp;
/**
 * @param  {clay.Quaternion} a
 * @return {number}
 */
Quaternion.len = function (a) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    return quat.length(a.array);</span>
};
&nbsp;
// Quaternion.length = Quaternion.len;
&nbsp;
/**
 * @param  {clay.Quaternion} out
 * @param  {clay.Quaternion} a
 * @param  {clay.Quaternion} b
 * @param  {number}     t
 * @return {clay.Quaternion}
 */
Quaternion.lerp = function (out, a, b, t) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    quat.lerp(out.array, a.array, b.array, t);</span>
<span class="cstat-no" title="statement not covered" >    out._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >    return out;</span>
};
&nbsp;
/**
 * @param  {clay.Quaternion} out
 * @param  {clay.Quaternion} a
 * @param  {clay.Quaternion} b
 * @param  {number}     t
 * @return {clay.Quaternion}
 */
Quaternion.slerp = function (out, a, b, t) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    quat.slerp(out.array, a.array, b.array, t);</span>
<span class="cstat-no" title="statement not covered" >    out._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >    return out;</span>
};
&nbsp;
/**
 * @param  {clay.Quaternion} out
 * @param  {clay.Quaternion} a
 * @param  {clay.Quaternion} b
 * @return {clay.Quaternion}
 */
Quaternion.mul = function (out, a, b) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    quat.multiply(out.array, a.array, b.array);</span>
<span class="cstat-no" title="statement not covered" >    out._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >    return out;</span>
};
&nbsp;
/**
 * @function
 * @param  {clay.Quaternion} out
 * @param  {clay.Quaternion} a
 * @param  {clay.Quaternion} b
 * @return {clay.Quaternion}
 */
Quaternion.multiply = Quaternion.mul;
&nbsp;
/**
 * @param  {clay.Quaternion} out
 * @param  {clay.Quaternion} a
 * @param  {number}     rad
 * @return {clay.Quaternion}
 */
Quaternion.rotateX = function (out, a, rad) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    quat.rotateX(out.array, a.array, rad);</span>
<span class="cstat-no" title="statement not covered" >    out._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >    return out;</span>
};
&nbsp;
/**
 * @param  {clay.Quaternion} out
 * @param  {clay.Quaternion} a
 * @param  {number}     rad
 * @return {clay.Quaternion}
 */
Quaternion.rotateY = function (out, a, rad) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    quat.rotateY(out.array, a.array, rad);</span>
<span class="cstat-no" title="statement not covered" >    out._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >    return out;</span>
};
&nbsp;
/**
 * @param  {clay.Quaternion} out
 * @param  {clay.Quaternion} a
 * @param  {number}     rad
 * @return {clay.Quaternion}
 */
Quaternion.rotateZ = function (out, a, rad) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    quat.rotateZ(out.array, a.array, rad);</span>
<span class="cstat-no" title="statement not covered" >    out._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >    return out;</span>
};
&nbsp;
/**
 * @param  {clay.Quaternion} out
 * @param  {clay.Vector3}    axis
 * @param  {number}     rad
 * @return {clay.Quaternion}
 */
Quaternion.setAxisAngle = function (out, axis, rad) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    quat.setAxisAngle(out.array, axis.array, rad);</span>
<span class="cstat-no" title="statement not covered" >    out._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >    return out;</span>
};
&nbsp;
/**
 * @param  {clay.Quaternion} out
 * @param  {clay.Quaternion} a
 * @return {clay.Quaternion}
 */
Quaternion.normalize = function (out, a) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    quat.normalize(out.array, a.array);</span>
<span class="cstat-no" title="statement not covered" >    out._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >    return out;</span>
};
&nbsp;
/**
 * @param  {clay.Quaternion} a
 * @return {number}
 */
Quaternion.sqrLen = function (a) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    return quat.sqrLen(a.array);</span>
};
&nbsp;
/**
 * @function
 * @param  {clay.Quaternion} a
 * @return {number}
 */
Quaternion.squaredLength = Quaternion.sqrLen;
&nbsp;
/**
 * @param  {clay.Quaternion} out
 * @param  {clay.Matrix3}    m
 * @return {clay.Quaternion}
 */
Quaternion.fromMat3 = function (out, m) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    quat.fromMat3(out.array, m.array);</span>
<span class="cstat-no" title="statement not covered" >    out._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >    return out;</span>
};
&nbsp;
/**
 * @param  {clay.Quaternion} out
 * @param  {clay.Vector3}    view
 * @param  {clay.Vector3}    right
 * @param  {clay.Vector3}    up
 * @return {clay.Quaternion}
 */
Quaternion.setAxes = function (out, view, right, up) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    quat.setAxes(out.array, view.array, right.array, up.array);</span>
<span class="cstat-no" title="statement not covered" >    out._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >    return out;</span>
};
&nbsp;
/**
 * @param  {clay.Quaternion} out
 * @param  {clay.Vector3}    a
 * @param  {clay.Vector3}    b
 * @return {clay.Quaternion}
 */
Quaternion.rotationTo = function (out, a, b) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    quat.rotationTo(out.array, a.array, b.array);</span>
<span class="cstat-no" title="statement not covered" >    out._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >    return out;</span>
};
&nbsp;
/**
 * Set quaternion from euler
 * @param {clay.Quaternion} out
 * @param {clay.Vector3} v
 * @param {String} order
 */
Quaternion.fromEuler = function (out, v, order) <span class="fstat-no" title="function not covered" >{</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    out._dirty = true;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    v = v.array;</span>
    var target = <span class="cstat-no" title="statement not covered" >out.array;</span>
    var c1 = <span class="cstat-no" title="statement not covered" >Math.cos(v[0] / 2);</span>
    var c2 = <span class="cstat-no" title="statement not covered" >Math.cos(v[1] / 2);</span>
    var c3 = <span class="cstat-no" title="statement not covered" >Math.cos(v[2] / 2);</span>
    var s1 = <span class="cstat-no" title="statement not covered" >Math.sin(v[0] / 2);</span>
    var s2 = <span class="cstat-no" title="statement not covered" >Math.sin(v[1] / 2);</span>
    var s3 = <span class="cstat-no" title="statement not covered" >Math.sin(v[2] / 2);</span>
&nbsp;
    var order = <span class="cstat-no" title="statement not covered" >(order || 'XYZ').toUpperCase();</span>
&nbsp;
    // http://www.mathworks.com/matlabcentral/fileexchange/
    //  20696-function-to-convert-between-dcm-euler-angles-quaternions-and-euler-vectors/
    //  content/SpinCalc.m
&nbsp;
<span class="cstat-no" title="statement not covered" >    switch (order) {</span>
        case 'XYZ':
<span class="cstat-no" title="statement not covered" >            target[0] = s1 * c2 * c3 + c1 * s2 * s3;</span>
<span class="cstat-no" title="statement not covered" >            target[1] = c1 * s2 * c3 - s1 * c2 * s3;</span>
<span class="cstat-no" title="statement not covered" >            target[2] = c1 * c2 * s3 + s1 * s2 * c3;</span>
<span class="cstat-no" title="statement not covered" >            target[3] = c1 * c2 * c3 - s1 * s2 * s3;</span>
<span class="cstat-no" title="statement not covered" >            break;</span>
        case 'YXZ':
<span class="cstat-no" title="statement not covered" >            target[0] = s1 * c2 * c3 + c1 * s2 * s3;</span>
<span class="cstat-no" title="statement not covered" >            target[1] = c1 * s2 * c3 - s1 * c2 * s3;</span>
<span class="cstat-no" title="statement not covered" >            target[2] = c1 * c2 * s3 - s1 * s2 * c3;</span>
<span class="cstat-no" title="statement not covered" >            target[3] = c1 * c2 * c3 + s1 * s2 * s3;</span>
<span class="cstat-no" title="statement not covered" >            break;</span>
        case 'ZXY':
<span class="cstat-no" title="statement not covered" >            target[0] = s1 * c2 * c3 - c1 * s2 * s3;</span>
<span class="cstat-no" title="statement not covered" >            target[1] = c1 * s2 * c3 + s1 * c2 * s3;</span>
<span class="cstat-no" title="statement not covered" >            target[2] = c1 * c2 * s3 + s1 * s2 * c3;</span>
<span class="cstat-no" title="statement not covered" >            target[3] = c1 * c2 * c3 - s1 * s2 * s3;</span>
<span class="cstat-no" title="statement not covered" >            break;</span>
        case 'ZYX':
<span class="cstat-no" title="statement not covered" >            target[0] = s1 * c2 * c3 - c1 * s2 * s3;</span>
<span class="cstat-no" title="statement not covered" >            target[1] = c1 * s2 * c3 + s1 * c2 * s3;</span>
<span class="cstat-no" title="statement not covered" >            target[2] = c1 * c2 * s3 - s1 * s2 * c3;</span>
<span class="cstat-no" title="statement not covered" >            target[3] = c1 * c2 * c3 + s1 * s2 * s3;</span>
<span class="cstat-no" title="statement not covered" >            break;</span>
        case 'YZX':
<span class="cstat-no" title="statement not covered" >            target[0] = s1 * c2 * c3 + c1 * s2 * s3;</span>
<span class="cstat-no" title="statement not covered" >            target[1] = c1 * s2 * c3 + s1 * c2 * s3;</span>
<span class="cstat-no" title="statement not covered" >            target[2] = c1 * c2 * s3 - s1 * s2 * c3;</span>
<span class="cstat-no" title="statement not covered" >            target[3] = c1 * c2 * c3 - s1 * s2 * s3;</span>
<span class="cstat-no" title="statement not covered" >            break;</span>
        case 'XZY':
<span class="cstat-no" title="statement not covered" >            target[0] = s1 * c2 * c3 - c1 * s2 * s3;</span>
<span class="cstat-no" title="statement not covered" >            target[1] = c1 * s2 * c3 - s1 * c2 * s3;</span>
<span class="cstat-no" title="statement not covered" >            target[2] = c1 * c2 * s3 + s1 * s2 * c3;</span>
<span class="cstat-no" title="statement not covered" >            target[3] = c1 * c2 * c3 + s1 * s2 * s3;</span>
<span class="cstat-no" title="statement not covered" >            break;</span>
    }
};
&nbsp;
export default Quaternion;
&nbsp;</pre></td></tr>
</table></pre>
<div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Fri Feb 23 2018 01:09:23 GMT+0800 (CST)
</div>
</div>
<script src="../../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../../sorter.js"></script>
</body>
</html>
