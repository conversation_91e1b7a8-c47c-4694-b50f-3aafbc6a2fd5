<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for src/math/BoundingBox.js</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../../index.html">all files</a> / <a href="index.html">src/math/</a> BoundingBox.js
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">78.13% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>125/160</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">32.61% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>15/46</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">73.33% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>11/15</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">79.58% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>113/142</span>
      </div>
    </div>
  </div>
  <div class='status-line medium'></div>
<pre><table class="coverage">
<tr><td class="line-count quiet">1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
116
117
118
119
120
121
122
123
124
125
126
127
128
129
130
131
132
133
134
135
136
137
138
139
140
141
142
143
144
145
146
147
148
149
150
151
152
153
154
155
156
157
158
159
160
161
162
163
164
165
166
167
168
169
170
171
172
173
174
175
176
177
178
179
180
181
182
183
184
185
186
187
188
189
190
191
192
193
194
195
196
197
198
199
200
201
202
203
204
205
206
207
208
209
210
211
212
213
214
215
216
217
218
219
220
221
222
223
224
225
226
227
228
229
230
231
232
233
234
235
236
237
238
239
240
241
242
243
244
245
246
247
248
249
250
251
252
253
254
255
256
257
258
259
260
261
262
263
264
265
266
267
268
269
270
271
272
273
274
275
276
277
278
279
280
281
282
283
284
285
286
287
288
289
290
291
292
293
294
295
296
297
298
299
300
301
302
303
304
305
306
307
308
309
310
311
312</td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">375×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">375×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">375×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">37×</span>
<span class="cline-any cline-yes">37×</span>
<span class="cline-any cline-yes">37×</span>
<span class="cline-any cline-yes">37×</span>
<span class="cline-any cline-yes">37×</span>
<span class="cline-any cline-yes">37×</span>
<span class="cline-any cline-yes">37×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">33×</span>
<span class="cline-any cline-yes">33×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">33×</span>
<span class="cline-any cline-yes">33×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">33×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">13×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">46×</span>
<span class="cline-any cline-yes">46×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">46×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">46×</span>
<span class="cline-any cline-yes">46×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">46×</span>
<span class="cline-any cline-yes">46×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">46×</span>
<span class="cline-any cline-yes">46×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">46×</span>
<span class="cline-any cline-yes">46×</span>
<span class="cline-any cline-yes">46×</span>
<span class="cline-any cline-yes">46×</span>
<span class="cline-any cline-yes">46×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">46×</span>
<span class="cline-any cline-yes">46×</span>
<span class="cline-any cline-yes">46×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">46×</span>
<span class="cline-any cline-yes">46×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">46×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">29×</span>
<span class="cline-any cline-yes">29×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">29×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">29×</span>
<span class="cline-any cline-yes">29×</span>
<span class="cline-any cline-yes">29×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">29×</span>
<span class="cline-any cline-yes">29×</span>
<span class="cline-any cline-yes">29×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">29×</span>
<span class="cline-any cline-yes">29×</span>
<span class="cline-any cline-yes">29×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">29×</span>
<span class="cline-any cline-yes">8×</span>
<span class="cline-any cline-yes">8×</span>
<span class="cline-any cline-yes">8×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">8×</span>
<span class="cline-any cline-yes">8×</span>
<span class="cline-any cline-yes">8×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">21×</span>
<span class="cline-any cline-yes">21×</span>
<span class="cline-any cline-yes">21×</span>
<span class="cline-any cline-yes">21×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">21×</span>
<span class="cline-any cline-yes">21×</span>
<span class="cline-any cline-yes">21×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">21×</span>
<span class="cline-any cline-yes">21×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">29×</span>
<span class="cline-any cline-yes">29×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">29×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-yes">16×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">15×</span>
<span class="cline-any cline-yes">15×</span>
<span class="cline-any cline-yes">15×</span>
<span class="cline-any cline-yes">15×</span>
<span class="cline-any cline-yes">15×</span>
<span class="cline-any cline-yes">15×</span>
<span class="cline-any cline-yes">15×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">import Vector3 from './Vector3';
import glMatrix from '../dep/glmatrix';
var vec3 = glMatrix.vec3;
&nbsp;
var vec3Copy = vec3.copy;
var vec3Set = vec3.set;
&nbsp;
/**
 * Axis aligned bounding box
 * @constructor
 * @alias clay.BoundingBox
 * @param {clay.Vector3} [min]
 * @param {clay.Vector3} [max]
 */
var BoundingBox = function (min, max) {
&nbsp;
    /**
     * Minimum coords of bounding box
     * @type {clay.Vector3}
     */
    this.min = min || new Vector3(Infinity, Infinity, Infinity);
&nbsp;
    /**
     * Maximum coords of bounding box
     * @type {clay.Vector3}
     */
    this.max = max || new Vector3(-Infinity, -Infinity, -Infinity);
&nbsp;
    this.vertices = null;
};
&nbsp;
BoundingBox.prototype = {
&nbsp;
    constructor: BoundingBox,
    /**
     * Update min and max coords from a vertices array
     * @param  {array} vertices
     */
    updateFromVertices: function (vertices) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        if (vertices.length &gt; 0) {</span>
            var min = <span class="cstat-no" title="statement not covered" >this.min;</span>
            var max = <span class="cstat-no" title="statement not covered" >this.max;</span>
            var minArr = <span class="cstat-no" title="statement not covered" >min.array;</span>
            var maxArr = <span class="cstat-no" title="statement not covered" >max.array;</span>
<span class="cstat-no" title="statement not covered" >            vec3Copy(minArr, vertices[0]);</span>
<span class="cstat-no" title="statement not covered" >            vec3Copy(maxArr, vertices[0]);</span>
<span class="cstat-no" title="statement not covered" >            for (var i = 1; i &lt; vertices.length; i++) {</span>
                var vertex = <span class="cstat-no" title="statement not covered" >vertices[i];</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >                if (vertex[0] &lt; minArr[0]) { <span class="cstat-no" title="statement not covered" >minArr[0] = vertex[0]; </span>}</span>
<span class="cstat-no" title="statement not covered" >                if (vertex[1] &lt; minArr[1]) { <span class="cstat-no" title="statement not covered" >minArr[1] = vertex[1]; </span>}</span>
<span class="cstat-no" title="statement not covered" >                if (vertex[2] &lt; minArr[2]) { <span class="cstat-no" title="statement not covered" >minArr[2] = vertex[2]; </span>}</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >                if (vertex[0] &gt; maxArr[0]) { <span class="cstat-no" title="statement not covered" >maxArr[0] = vertex[0]; </span>}</span>
<span class="cstat-no" title="statement not covered" >                if (vertex[1] &gt; maxArr[1]) { <span class="cstat-no" title="statement not covered" >maxArr[1] = vertex[1]; </span>}</span>
<span class="cstat-no" title="statement not covered" >                if (vertex[2] &gt; maxArr[2]) { <span class="cstat-no" title="statement not covered" >maxArr[2] = vertex[2]; </span>}</span>
            }
<span class="cstat-no" title="statement not covered" >            min._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >            max._dirty = true;</span>
        }
    },
&nbsp;
    /**
     * Union operation with another bounding box
     * @param  {clay.BoundingBox} bbox
     */
    union: function (bbox) {
        var min = this.min;
        var max = this.max;
        vec3.min(min.array, min.array, bbox.min.array);
        vec3.max(max.array, max.array, bbox.max.array);
        min._dirty = true;
        max._dirty = true;
        return this;
    },
&nbsp;
    /**
     * Intersection operation with another bounding box
     * @param  {clay.BoundingBox} bbox
     */
    intersection: function (bbox) {
        var min = this.min;
        var max = this.max;
        vec3.max(min.array, min.array, bbox.min.array);
        vec3.min(max.array, max.array, bbox.max.array);
        min._dirty = true;
        max._dirty = true;
        return this;
    },
&nbsp;
    /**
     * If intersect with another bounding box
     * @param  {clay.BoundingBox} bbox
     * @return {boolean}
     */
    intersectBoundingBox: function (bbox) {
        var _min = this.min.array;
        var _max = this.max.array;
&nbsp;
        var _min2 = bbox.min.array;
        var _max2 = bbox.max.array;
&nbsp;
        return ! (_min[0] &gt; _max2[0] || _min[1] &gt; _max2[1] || _min[2] &gt; _max2[2]
            || _max[0] &lt; _min2[0] || _max[1] &lt; _min2[1] || _max[2] &lt; _min2[2]);
    },
&nbsp;
    /**
     * If contain another bounding box entirely
     * @param  {clay.BoundingBox} bbox
     * @return {boolean}
     */
    containBoundingBox: function (bbox) <span class="fstat-no" title="function not covered" >{</span>
&nbsp;
        var _min = <span class="cstat-no" title="statement not covered" >this.min.array;</span>
        var _max = <span class="cstat-no" title="statement not covered" >this.max.array;</span>
&nbsp;
        var _min2 = <span class="cstat-no" title="statement not covered" >bbox.min.array;</span>
        var _max2 = <span class="cstat-no" title="statement not covered" >bbox.max.array;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        return _min[0] &lt;= _min2[0] &amp;&amp; _min[1] &lt;= _min2[1] &amp;&amp; _min[2] &lt;= _min2[2]</span>
            &amp;&amp; _max[0] &gt;= _max2[0] &amp;&amp; _max[1] &gt;= _max2[1] &amp;&amp; _max[2] &gt;= _max2[2];
    },
&nbsp;
    /**
     * If contain point entirely
     * @param  {clay.Vector3} point
     * @return {boolean}
     */
    containPoint: function (p) <span class="fstat-no" title="function not covered" >{</span>
        var _min = <span class="cstat-no" title="statement not covered" >this.min.array;</span>
        var _max = <span class="cstat-no" title="statement not covered" >this.max.array;</span>
&nbsp;
        var _p = <span class="cstat-no" title="statement not covered" >p.array;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        return _min[0] &lt;= _p[0] &amp;&amp; _min[1] &lt;= _p[1] &amp;&amp; _min[2] &lt;= _p[2]</span>
            &amp;&amp; _max[0] &gt;= _p[0] &amp;&amp; _max[1] &gt;= _p[1] &amp;&amp; _max[2] &gt;= _p[2];
    },
&nbsp;
    /**
     * If bounding box is finite
     */
    isFinite: function () {
        var _min = this.min.array;
        var _max = this.max.array;
        return isFinite(_min[0]) &amp;&amp; <span class="branch-1 cbranch-no" title="branch not covered" >isFinite(_min[1]) </span>&amp;&amp; <span class="branch-2 cbranch-no" title="branch not covered" >isFinite(_min[2])</span>
            &amp;&amp; <span class="branch-3 cbranch-no" title="branch not covered" >isFinite(_max[0]) </span>&amp;&amp; <span class="branch-4 cbranch-no" title="branch not covered" >isFinite(_max[1]) </span>&amp;&amp; <span class="branch-5 cbranch-no" title="branch not covered" >isFinite(_max[2]);</span>
    },
&nbsp;
    /**
     * Apply an affine transform matrix to the bounding box
     * @param  {clay.Matrix4} matrix
     */
    applyTransform: function (matrix) {
        this.transformFrom(this, matrix);
    },
&nbsp;
    /**
     * Get from another bounding box and an affine transform matrix.
     * @param {clay.BoundingBox} source
     * @param {clay.Matrix4} matrix
     */
    transformFrom: (function () {
        // http://dev.theomader.com/transform-bounding-boxes/
        var xa = vec3.create();
        var xb = vec3.create();
        var ya = vec3.create();
        var yb = vec3.create();
        var za = vec3.create();
        var zb = vec3.create();
&nbsp;
        return function (source, matrix) {
            var min = source.min.array;
            var max = source.max.array;
&nbsp;
            var m = matrix.array;
&nbsp;
            xa[0] = m[0] * min[0]; xa[1] = m[1] * min[0]; xa[2] = m[2] * min[0];
            xb[0] = m[0] * max[0]; xb[1] = m[1] * max[0]; xb[2] = m[2] * max[0];
&nbsp;
            ya[0] = m[4] * min[1]; ya[1] = m[5] * min[1]; ya[2] = m[6] * min[1];
            yb[0] = m[4] * max[1]; yb[1] = m[5] * max[1]; yb[2] = m[6] * max[1];
&nbsp;
            za[0] = m[8] * min[2]; za[1] = m[9] * min[2]; za[2] = m[10] * min[2];
            zb[0] = m[8] * max[2]; zb[1] = m[9] * max[2]; zb[2] = m[10] * max[2];
&nbsp;
            min = this.min.array;
            max = this.max.array;
            min[0] = Math.min(xa[0], xb[0]) + Math.min(ya[0], yb[0]) + Math.min(za[0], zb[0]) + m[12];
            min[1] = Math.min(xa[1], xb[1]) + Math.min(ya[1], yb[1]) + Math.min(za[1], zb[1]) + m[13];
            min[2] = Math.min(xa[2], xb[2]) + Math.min(ya[2], yb[2]) + Math.min(za[2], zb[2]) + m[14];
&nbsp;
            max[0] = Math.max(xa[0], xb[0]) + Math.max(ya[0], yb[0]) + Math.max(za[0], zb[0]) + m[12];
            max[1] = Math.max(xa[1], xb[1]) + Math.max(ya[1], yb[1]) + Math.max(za[1], zb[1]) + m[13];
            max[2] = Math.max(xa[2], xb[2]) + Math.max(ya[2], yb[2]) + Math.max(za[2], zb[2]) + m[14];
&nbsp;
            this.min._dirty = true;
            this.max._dirty = true;
&nbsp;
            return this;
        };
    })(),
&nbsp;
    /**
     * Apply a projection matrix to the bounding box
     * @param  {clay.Matrix4} matrix
     */
    applyProjection: function (matrix) {
        var min = this.min.array;
        var max = this.max.array;
&nbsp;
        var m = matrix.array;
        // min in min z
        var v10 = min[0];
        var v11 = min[1];
        var v12 = min[2];
        // max in min z
        var v20 = max[0];
        var v21 = max[1];
        var v22 = min[2];
        // max in max z
        var v30 = max[0];
        var v31 = max[1];
        var v32 = max[2];
&nbsp;
        if (m[15] === 1) {  // Orthographic projection
            min[0] = m[0] * v10 + m[12];
            min[1] = m[5] * v11 + m[13];
            max[2] = m[10] * v12 + m[14];
&nbsp;
            max[0] = m[0] * v30 + m[12];
            max[1] = m[5] * v31 + m[13];
            min[2] = m[10] * v32 + m[14];
        }
        else {
            var w = -1 / v12;
            min[0] = m[0] * v10 * w;
            min[1] = m[5] * v11 * w;
            max[2] = (m[10] * v12 + m[14]) * w;
&nbsp;
            w = -1 / v22;
            max[0] = m[0] * v20 * w;
            max[1] = m[5] * v21 * w;
&nbsp;
            w = -1 / v32;
            min[2] = (m[10] * v32 + m[14]) * w;
        }
        this.min._dirty = true;
        this.max._dirty = true;
&nbsp;
        return this;
    },
&nbsp;
    updateVertices: function () {
        var vertices = this.vertices;
        if (!vertices) {
            // Cube vertices
            vertices = [];
            for (var i = 0; i &lt; 8; i++) {
                vertices[i] = vec3.fromValues(0, 0, 0);
            }
&nbsp;
            /**
             * Eight coords of bounding box
             * @type {Float32Array[]}
             */
            this.vertices = vertices;
        }
        var min = this.min.array;
        var max = this.max.array;
        //--- min z
        // min x
        vec3Set(vertices[0], min[0], min[1], min[2]);
        vec3Set(vertices[1], min[0], max[1], min[2]);
        // max x
        vec3Set(vertices[2], max[0], min[1], min[2]);
        vec3Set(vertices[3], max[0], max[1], min[2]);
&nbsp;
        //-- max z
        vec3Set(vertices[4], min[0], min[1], max[2]);
        vec3Set(vertices[5], min[0], max[1], max[2]);
        vec3Set(vertices[6], max[0], min[1], max[2]);
        vec3Set(vertices[7], max[0], max[1], max[2]);
&nbsp;
        return this;
    },
    /**
     * Copy values from another bounding box
     * @param  {clay.BoundingBox} bbox
     */
    copy: function (bbox) {
        var min = this.min;
        var max = this.max;
        vec3Copy(min.array, bbox.min.array);
        vec3Copy(max.array, bbox.max.array);
        min._dirty = true;
        max._dirty = true;
        return this;
    },
&nbsp;
    /**
     * Clone a new bounding box
     * @return {clay.BoundingBox}
     */
    clone: function () <span class="fstat-no" title="function not covered" >{</span>
        var boundingBox = <span class="cstat-no" title="statement not covered" >new BoundingBox();</span>
<span class="cstat-no" title="statement not covered" >        boundingBox.copy(this);</span>
<span class="cstat-no" title="statement not covered" >        return boundingBox;</span>
    }
};
&nbsp;
export default BoundingBox;
&nbsp;</pre></td></tr>
</table></pre>
<div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Fri Feb 23 2018 01:09:23 GMT+0800 (CST)
</div>
</div>
<script src="../../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../../sorter.js"></script>
</body>
</html>
