<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for src/plugin/GestureMgr.js</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../../index.html">all files</a> / <a href="index.html">src/plugin/</a> GestureMgr.js
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">7.69% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>3/39</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>0/22</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>0/8</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">7.69% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>3/39</span>
      </div>
    </div>
  </div>
  <div class='status-line low'></div>
<pre><table class="coverage">
<tr><td class="line-count quiet">1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105</td><td class="line-coverage quiet"><span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">var GestureMgr = function () <span class="fstat-no" title="function not covered" >{</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    this._track = [];</span>
};
&nbsp;
GestureMgr.prototype = {
&nbsp;
    constructor: GestureMgr,
&nbsp;
    recognize: function (event, target, root) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        this._doTrack(event, target, root);</span>
<span class="cstat-no" title="statement not covered" >        return this._recognize(event);</span>
    },
&nbsp;
    clear: function () <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        this._track.length = 0;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    },
&nbsp;
    _doTrack: function (event, target, root) <span class="fstat-no" title="function not covered" >{</span>
        var touches = <span class="cstat-no" title="statement not covered" >event.targetTouches;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        if (!touches) {</span>
<span class="cstat-no" title="statement not covered" >            return;</span>
        }
&nbsp;
        var trackItem = <span class="cstat-no" title="statement not covered" >{</span>
            points: [],
            touches: [],
            target: target,
            event: event
        };
&nbsp;
<span class="cstat-no" title="statement not covered" >        for (var i = 0, len = touches.length; i &lt; len; i++) {</span>
            var touch = <span class="cstat-no" title="statement not covered" >touches[i];</span>
<span class="cstat-no" title="statement not covered" >            trackItem.points.push([touch.clientX, touch.clientY]);</span>
<span class="cstat-no" title="statement not covered" >            trackItem.touches.push(touch);</span>
        }
&nbsp;
<span class="cstat-no" title="statement not covered" >        this._track.push(trackItem);</span>
    },
&nbsp;
    _recognize: function (event) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        for (var eventName in recognizers) {</span>
<span class="cstat-no" title="statement not covered" >            if (recognizers.hasOwnProperty(eventName)) {</span>
                var gestureInfo = <span class="cstat-no" title="statement not covered" >recognizers[eventName](this._track, event);</span>
<span class="cstat-no" title="statement not covered" >                if (gestureInfo) {</span>
<span class="cstat-no" title="statement not covered" >                    return gestureInfo;</span>
                }
            }
        }
    }
};
&nbsp;
function dist(pointPair) <span class="fstat-no" title="function not covered" >{</span>
    var dx = <span class="cstat-no" title="statement not covered" >pointPair[1][0] - pointPair[0][0];</span>
    var dy = <span class="cstat-no" title="statement not covered" >pointPair[1][1] - pointPair[0][1];</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return Math.sqrt(dx * dx + dy * dy);</span>
}
&nbsp;
function center(pointPair) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    return [</span>
        (pointPair[0][0] + pointPair[1][0]) / 2,
        (pointPair[0][1] + pointPair[1][1]) / 2
    ];
}
&nbsp;
var recognizers = {
&nbsp;
    pinch: function (track, event) <span class="fstat-no" title="function not covered" >{</span>
        var trackLen = <span class="cstat-no" title="statement not covered" >track.length;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        if (!trackLen) {</span>
<span class="cstat-no" title="statement not covered" >            return;</span>
        }
&nbsp;
        var pinchEnd = <span class="cstat-no" title="statement not covered" >(track[trackLen - 1] || {}).points;</span>
        var pinchPre = <span class="cstat-no" title="statement not covered" >(track[trackLen - 2] || {}).points || pinchEnd;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        if (pinchPre</span>
            &amp;&amp; pinchPre.length &gt; 1
            &amp;&amp; pinchEnd
            &amp;&amp; pinchEnd.length &gt; 1
        ) {
            var pinchScale = <span class="cstat-no" title="statement not covered" >dist(pinchEnd) / dist(pinchPre);</span>
<span class="cstat-no" title="statement not covered" >            !isFinite(pinchScale) &amp;&amp; (pinchScale = 1);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >            event.pinchScale = pinchScale;</span>
&nbsp;
            var pinchCenter = <span class="cstat-no" title="statement not covered" >center(pinchEnd);</span>
<span class="cstat-no" title="statement not covered" >            event.pinchX = pinchCenter[0];</span>
<span class="cstat-no" title="statement not covered" >            event.pinchY = pinchCenter[1];</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >            return {</span>
                type: 'pinch',
                target: track[0].target,
                event: event
            };
        }
    }
};
&nbsp;
export default GestureMgr;
&nbsp;</pre></td></tr>
</table></pre>
<div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Fri Feb 23 2018 01:09:23 GMT+0800 (CST)
</div>
</div>
<script src="../../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../../sorter.js"></script>
</body>
</html>
