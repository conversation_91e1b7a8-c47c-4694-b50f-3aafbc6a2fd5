-- 航班舱位可视化模块数据库表结构

-- 乘客信息表
DROP TABLE IF EXISTS `ky_passenger`;
CREATE TABLE `ky_passenger` (
  `passenger_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '乘客ID',
  `flight_id` bigint(20) DEFAULT NULL COMMENT '航班ID',
  `flight_number` varchar(20) DEFAULT NULL COMMENT '航班号',
  `passenger_name` varchar(100) NOT NULL COMMENT '乘客姓名',
  `id_card` varchar(18) DEFAULT NULL COMMENT '身份证号',
  `passport` varchar(50) DEFAULT NULL COMMENT '护照号',
  `gender` char(1) DEFAULT NULL COMMENT '性别（0女 1男）',
  `age` int(3) DEFAULT NULL COMMENT '年龄',
  `nationality` varchar(50) DEFAULT '中国' COMMENT '国籍',
  `phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `seat_number` varchar(10) DEFAULT NULL COMMENT '座位号',
  `cabin_class` char(1) DEFAULT '0' COMMENT '舱位等级（0经济舱 1商务舱 2头等舱）',
  `ticket_status` char(1) DEFAULT '0' COMMENT '票务状态（0已订票 1已值机 2已登机 3已起飞）',
  `is_key_person` char(1) DEFAULT '0' COMMENT '是否重点人员（0否 1是）',
  `key_person_type` char(1) DEFAULT NULL COMMENT '重点人员类型（1涉恐 2涉毒 3涉黑 4逃犯 5精神病 6其他危险 7VIP）',
  `risk_level` char(1) DEFAULT '1' COMMENT '风险等级（1低风险 2中风险 3高风险）',
  `boarding_time` datetime DEFAULT NULL COMMENT '登机时间',
  `checkin_time` datetime DEFAULT NULL COMMENT '值机时间',
  `special_requests` varchar(500) DEFAULT NULL COMMENT '特殊需求',
  `emergency_contact` varchar(100) DEFAULT NULL COMMENT '紧急联系人',
  `emergency_phone` varchar(20) DEFAULT NULL COMMENT '紧急联系电话',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`passenger_id`),
  KEY `idx_flight_id` (`flight_id`),
  KEY `idx_flight_number` (`flight_number`),
  KEY `idx_id_card` (`id_card`),
  KEY `idx_seat_number` (`seat_number`),
  KEY `idx_key_person` (`is_key_person`, `key_person_type`),
  KEY `idx_ticket_status` (`ticket_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='乘客信息表';

-- 乘客历史记录表
DROP TABLE IF EXISTS `ky_passenger_history`;
CREATE TABLE `ky_passenger_history` (
  `history_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '历史记录ID',
  `passenger_id` bigint(20) DEFAULT NULL COMMENT '乘客ID',
  `id_card` varchar(18) DEFAULT NULL COMMENT '身份证号',
  `passenger_name` varchar(100) DEFAULT NULL COMMENT '乘客姓名',
  `flight_number` varchar(20) DEFAULT NULL COMMENT '航班号',
  `origin` varchar(100) DEFAULT NULL COMMENT '起点',
  `destination` varchar(100) DEFAULT NULL COMMENT '终点',
  `flight_date` date DEFAULT NULL COMMENT '航班日期',
  `seat_number` varchar(10) DEFAULT NULL COMMENT '座位号',
  `cabin_class` char(1) DEFAULT NULL COMMENT '舱位等级',
  `record_type` char(1) DEFAULT '0' COMMENT '记录类型（0正常记录 1异常行为 2安全事件 3违规记录）',
  `event_description` text COMMENT '事件描述',
  `handle_result` varchar(500) DEFAULT NULL COMMENT '处理结果',
  `risk_assessment` varchar(200) DEFAULT NULL COMMENT '风险评估',
  `case_number` varchar(50) DEFAULT NULL COMMENT '案件编号',
  `record_source` varchar(100) DEFAULT NULL COMMENT '记录来源',
  `is_verified` char(1) DEFAULT '0' COMMENT '是否已核实（0未核实 1已核实）',
  `verify_person` varchar(100) DEFAULT NULL COMMENT '核实人',
  `verify_time` datetime DEFAULT NULL COMMENT '核实时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`history_id`),
  KEY `idx_passenger_id` (`passenger_id`),
  KEY `idx_id_card` (`id_card`),
  KEY `idx_flight_number` (`flight_number`),
  KEY `idx_flight_date` (`flight_date`),
  KEY `idx_record_type` (`record_type`),
  KEY `idx_is_verified` (`is_verified`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='乘客历史记录表';

-- 插入测试数据
-- 乘客信息测试数据
INSERT INTO `ky_passenger` (`flight_id`, `flight_number`, `passenger_name`, `id_card`, `gender`, `age`, `nationality`, `phone`, `seat_number`, `cabin_class`, `ticket_status`, `is_key_person`, `key_person_type`, `risk_level`, `special_requests`, `emergency_contact`, `emergency_phone`, `remark`) VALUES
(1, 'CZ3539', '张三', '320101199001011234', '1', 34, '中国', '13800138001', '1A', '2', '2', '0', NULL, '1', NULL, '李四', '13800138002', '正常乘客'),
(1, 'CZ3539', '李明', '320101199002021235', '1', 33, '中国', '13800138003', '1B', '2', '2', '1', '1', '3', '需要特别关注', '王五', '13800138004', '涉恐重点人员'),
(1, 'CZ3539', '王芳', '320101199003031236', '0', 32, '中国', '13800138005', '2A', '2', '1', '0', NULL, '1', NULL, '赵六', '13800138006', '正常乘客'),
(1, 'CZ3539', '刘强', '320101199004041237', '1', 31, '中国', '13800138007', '3A', '1', '2', '1', '2', '2', '涉毒人员', '孙七', '13800138008', '涉毒重点人员'),
(1, 'CZ3539', '陈美', '320101199005051238', '0', 30, '中国', '13800138009', '10A', '0', '2', '0', NULL, '1', NULL, '周八', '13800138010', '正常乘客'),
(1, 'CZ3539', '杨峰', '320101199006061239', '1', 29, '中国', '13800138011', '10B', '0', '1', '1', '4', '3', '在逃人员', '吴九', '13800138012', '逃犯重点人员'),
(1, 'CZ3539', '赵丽', '320101199007071240', '0', 28, '中国', '13800138013', '15C', '0', '2', '0', NULL, '1', NULL, '郑十', '13800138014', '正常乘客'),
(1, 'CZ3539', '孙伟', '320101199008081241', '1', 27, '中国', '13800138015', '20A', '0', '2', '1', '7', '1', 'VIP客户', '钱一', '13800138016', 'VIP重要客户');

-- 乘客历史记录测试数据
INSERT INTO `ky_passenger_history` (`passenger_id`, `id_card`, `passenger_name`, `flight_number`, `origin`, `destination`, `flight_date`, `seat_number`, `cabin_class`, `record_type`, `event_description`, `handle_result`, `risk_assessment`, `case_number`, `record_source`, `is_verified`, `verify_person`, `verify_time`) VALUES
(2, '320101199002021235', '李明', 'CZ3539', '南京', '北京', '2024-01-15', '1B', '2', '2', '在安检时发现可疑物品', '已移交公安部门处理', '高风险', 'AJ20240115001', '机场安检', '1', '安检员张三', '2024-01-15 10:30:00'),
(2, '320101199002021235', '李明', 'MU5678', '上海', '广州', '2023-12-20', '5A', '1', '1', '在候机厅行为异常', '已进行询问和记录', '中风险', NULL, '候机厅监控', '1', '安保人员李四', '2023-12-20 14:20:00'),
(4, '320101199004041237', '刘强', 'CA1234', '北京', '深圳', '2023-11-10', '8C', '0', '2', '携带违禁药品', '已没收并记录', '高风险', 'AJ20231110002', '海关检查', '1', '海关人员王五', '2023-11-10 16:45:00'),
(6, '320101199006061239', '杨峰', 'HU9876', '成都', '西安', '2023-10-05', '12B', '0', '1', '与其他乘客发生冲突', '已调解处理', '中风险', NULL, '乘务员报告', '1', '乘务长赵六', '2023-10-05 11:15:00'),
(8, '320101199008081241', '孙伟', 'CZ8888', '深圳', '上海', '2023-09-20', '2A', '2', '0', 'VIP客户正常出行', '正常服务', '低风险', NULL, '客服记录', '1', '客服经理孙七', '2023-09-20 09:00:00');

-- 创建索引优化查询性能
CREATE INDEX idx_passenger_flight_seat ON ky_passenger(flight_id, seat_number);
CREATE INDEX idx_passenger_key_risk ON ky_passenger(is_key_person, risk_level);
CREATE INDEX idx_history_id_card_date ON ky_passenger_history(id_card, flight_date);
CREATE INDEX idx_history_record_type_date ON ky_passenger_history(record_type, flight_date);
