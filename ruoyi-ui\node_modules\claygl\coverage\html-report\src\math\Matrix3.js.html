<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for src/math/Matrix3.js</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../../index.html">all files</a> / <a href="index.html">src/math/</a> Matrix3.js
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">15.65% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>18/115</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">100% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>0/0</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>0/36</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">15.65% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>18/115</span>
      </div>
    </div>
  </div>
  <div class='status-line low'></div>
<pre><table class="coverage">
<tr><td class="line-count quiet">1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
116
117
118
119
120
121
122
123
124
125
126
127
128
129
130
131
132
133
134
135
136
137
138
139
140
141
142
143
144
145
146
147
148
149
150
151
152
153
154
155
156
157
158
159
160
161
162
163
164
165
166
167
168
169
170
171
172
173
174
175
176
177
178
179
180
181
182
183
184
185
186
187
188
189
190
191
192
193
194
195
196
197
198
199
200
201
202
203
204
205
206
207
208
209
210
211
212
213
214
215
216
217
218
219
220
221
222
223
224
225
226
227
228
229
230
231
232
233
234
235
236
237
238
239
240
241
242
243
244
245
246
247
248
249
250
251
252
253
254
255
256
257
258
259
260
261
262
263
264
265
266
267
268
269
270
271
272
273
274
275
276
277
278
279
280
281
282
283
284
285
286
287
288
289
290
291
292
293
294
295
296
297
298
299
300
301
302
303
304
305
306
307
308
309
310
311
312
313
314
315
316
317
318
319
320
321
322
323
324
325
326
327
328
329
330
331
332
333
334
335
336
337
338
339
340
341
342
343
344
345
346
347
348
349
350
351
352
353
354
355
356
357
358
359
360
361
362
363
364
365
366
367
368
369
370
371
372
373
374
375
376
377
378
379
380
381
382
383
384
385
386
387
388
389
390
391
392
393
394
395
396
397
398</td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">import glMatrix from '../dep/glmatrix';
var mat3 = glMatrix.mat3;
&nbsp;
/**
 * @constructor
 * @alias clay.Matrix3
 */
var Matrix3 = function () <span class="fstat-no" title="function not covered" >{</span>
&nbsp;
    /**
     * Storage of Matrix3
     * @name array
     * @type {Float32Array}
     * @memberOf clay.Matrix3#
     */
<span class="cstat-no" title="statement not covered" >    this.array = mat3.create();</span>
&nbsp;
    /**
     * @name _dirty
     * @type {boolean}
     * @memberOf clay.Matrix3#
     */
<span class="cstat-no" title="statement not covered" >    this._dirty = true;</span>
};
&nbsp;
Matrix3.prototype = {
&nbsp;
    constructor: Matrix3,
&nbsp;
    /**
     * Set components from array
     * @param  {Float32Array|number[]} arr
     */
    setArray: function (arr) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        for (var i = 0; i &lt; this.array.length; i++) {</span>
<span class="cstat-no" title="statement not covered" >            this.array[i] = arr[i];</span>
        }
<span class="cstat-no" title="statement not covered" >        this._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    },
    /**
     * Calculate the adjugate of self, in-place
     * @return {clay.Matrix3}
     */
    adjoint: function () <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        mat3.adjoint(this.array, this.array);</span>
<span class="cstat-no" title="statement not covered" >        this._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    },
&nbsp;
    /**
     * Clone a new Matrix3
     * @return {clay.Matrix3}
     */
    clone: function () <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        return (new Matrix3()).copy(this);</span>
    },
&nbsp;
    /**
     * Copy from b
     * @param  {clay.Matrix3} b
     * @return {clay.Matrix3}
     */
    copy: function (b) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        mat3.copy(this.array, b.array);</span>
<span class="cstat-no" title="statement not covered" >        this._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    },
&nbsp;
    /**
     * Calculate matrix determinant
     * @return {number}
     */
    determinant: function () <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        return mat3.determinant(this.array);</span>
    },
&nbsp;
    /**
     * Copy the values from Matrix2d a
     * @param  {clay.Matrix2d} a
     * @return {clay.Matrix3}
     */
    fromMat2d: function (a) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        mat3.fromMat2d(this.array, a.array);</span>
<span class="cstat-no" title="statement not covered" >        this._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    },
&nbsp;
    /**
     * Copies the upper-left 3x3 values of Matrix4
     * @param  {clay.Matrix4} a
     * @return {clay.Matrix3}
     */
    fromMat4: function (a) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        mat3.fromMat4(this.array, a.array);</span>
<span class="cstat-no" title="statement not covered" >        this._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    },
&nbsp;
    /**
     * Calculates a rotation matrix from the given quaternion
     * @param  {clay.Quaternion} q
     * @return {clay.Matrix3}
     */
    fromQuat: function (q) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        mat3.fromQuat(this.array, q.array);</span>
<span class="cstat-no" title="statement not covered" >        this._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    },
&nbsp;
    /**
     * Set to a identity matrix
     * @return {clay.Matrix3}
     */
    identity: function () <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        mat3.identity(this.array);</span>
<span class="cstat-no" title="statement not covered" >        this._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    },
&nbsp;
    /**
     * Invert self
     * @return {clay.Matrix3}
     */
    invert: function () <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        mat3.invert(this.array, this.array);</span>
<span class="cstat-no" title="statement not covered" >        this._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    },
&nbsp;
    /**
     * Alias for mutiply
     * @param  {clay.Matrix3} b
     * @return {clay.Matrix3}
     */
    mul: function (b) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        mat3.mul(this.array, this.array, b.array);</span>
<span class="cstat-no" title="statement not covered" >        this._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    },
&nbsp;
    /**
     * Alias for multiplyLeft
     * @param  {clay.Matrix3} a
     * @return {clay.Matrix3}
     */
    mulLeft: function (a) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        mat3.mul(this.array, a.array, this.array);</span>
<span class="cstat-no" title="statement not covered" >        this._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    },
&nbsp;
    /**
     * Multiply self and b
     * @param  {clay.Matrix3} b
     * @return {clay.Matrix3}
     */
    multiply: function (b) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        mat3.multiply(this.array, this.array, b.array);</span>
<span class="cstat-no" title="statement not covered" >        this._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    },
&nbsp;
    /**
     * Multiply a and self, a is on the left
     * @param  {clay.Matrix3} a
     * @return {clay.Matrix3}
     */
    multiplyLeft: function (a) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        mat3.multiply(this.array, a.array, this.array);</span>
<span class="cstat-no" title="statement not covered" >        this._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    },
&nbsp;
    /**
     * Rotate self by a given radian
     * @param  {number}   rad
     * @return {clay.Matrix3}
     */
    rotate: function (rad) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        mat3.rotate(this.array, this.array, rad);</span>
<span class="cstat-no" title="statement not covered" >        this._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    },
&nbsp;
    /**
     * Scale self by s
     * @param  {clay.Vector2}  s
     * @return {clay.Matrix3}
     */
    scale: function (v) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        mat3.scale(this.array, this.array, v.array);</span>
<span class="cstat-no" title="statement not covered" >        this._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    },
&nbsp;
    /**
     * Translate self by v
     * @param  {clay.Vector2}  v
     * @return {clay.Matrix3}
     */
    translate: function (v) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        mat3.translate(this.array, this.array, v.array);</span>
<span class="cstat-no" title="statement not covered" >        this._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    },
    /**
     * Calculates a 3x3 normal matrix (transpose inverse) from the 4x4 matrix
     * @param {clay.Matrix4} a
     */
    normalFromMat4: function (a) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        mat3.normalFromMat4(this.array, a.array);</span>
<span class="cstat-no" title="statement not covered" >        this._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    },
&nbsp;
    /**
     * Transpose self, in-place.
     * @return {clay.Matrix2}
     */
    transpose: function () <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        mat3.transpose(this.array, this.array);</span>
<span class="cstat-no" title="statement not covered" >        this._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    },
&nbsp;
    toString: function () <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        return '[' + Array.prototype.join.call(this.array, ',') + ']';</span>
    },
&nbsp;
    toArray: function () <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        return Array.prototype.slice.call(this.array);</span>
    }
};
/**
 * @param  {clay.Matrix3} out
 * @param  {clay.Matrix3} a
 * @return {clay.Matrix3}
 */
Matrix3.adjoint = function (out, a) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    mat3.adjoint(out.array, a.array);</span>
<span class="cstat-no" title="statement not covered" >    out._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >    return out;</span>
};
&nbsp;
/**
 * @param  {clay.Matrix3} out
 * @param  {clay.Matrix3} a
 * @return {clay.Matrix3}
 */
Matrix3.copy = function (out, a) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    mat3.copy(out.array, a.array);</span>
<span class="cstat-no" title="statement not covered" >    out._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >    return out;</span>
};
&nbsp;
/**
 * @param  {clay.Matrix3} a
 * @return {number}
 */
Matrix3.determinant = function (a) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    return mat3.determinant(a.array);</span>
};
&nbsp;
/**
 * @param  {clay.Matrix3} out
 * @return {clay.Matrix3}
 */
Matrix3.identity = function (out) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    mat3.identity(out.array);</span>
<span class="cstat-no" title="statement not covered" >    out._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >    return out;</span>
};
&nbsp;
/**
 * @param  {clay.Matrix3} out
 * @param  {clay.Matrix3} a
 * @return {clay.Matrix3}
 */
Matrix3.invert = function (out, a) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    mat3.invert(out.array, a.array);</span>
<span class="cstat-no" title="statement not covered" >    return out;</span>
};
&nbsp;
/**
 * @param  {clay.Matrix3} out
 * @param  {clay.Matrix3} a
 * @param  {clay.Matrix3} b
 * @return {clay.Matrix3}
 */
Matrix3.mul = function (out, a, b) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    mat3.mul(out.array, a.array, b.array);</span>
<span class="cstat-no" title="statement not covered" >    out._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >    return out;</span>
};
&nbsp;
/**
 * @function
 * @param  {clay.Matrix3} out
 * @param  {clay.Matrix3} a
 * @param  {clay.Matrix3} b
 * @return {clay.Matrix3}
 */
Matrix3.multiply = Matrix3.mul;
&nbsp;
/**
 * @param  {clay.Matrix3}  out
 * @param  {clay.Matrix2d} a
 * @return {clay.Matrix3}
 */
Matrix3.fromMat2d = function (out, a) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    mat3.fromMat2d(out.array, a.array);</span>
<span class="cstat-no" title="statement not covered" >    out._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >    return out;</span>
};
&nbsp;
/**
 * @param  {clay.Matrix3} out
 * @param  {clay.Matrix4} a
 * @return {clay.Matrix3}
 */
Matrix3.fromMat4 = function (out, a) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    mat3.fromMat4(out.array, a.array);</span>
<span class="cstat-no" title="statement not covered" >    out._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >    return out;</span>
};
&nbsp;
/**
 * @param  {clay.Matrix3}    out
 * @param  {clay.Quaternion} a
 * @return {clay.Matrix3}
 */
Matrix3.fromQuat = function (out, q) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    mat3.fromQuat(out.array, q.array);</span>
<span class="cstat-no" title="statement not covered" >    out._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >    return out;</span>
};
&nbsp;
/**
 * @param  {clay.Matrix3} out
 * @param  {clay.Matrix4} a
 * @return {clay.Matrix3}
 */
Matrix3.normalFromMat4 = function (out, a) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    mat3.normalFromMat4(out.array, a.array);</span>
<span class="cstat-no" title="statement not covered" >    out._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >    return out;</span>
};
&nbsp;
/**
 * @param  {clay.Matrix3} out
 * @param  {clay.Matrix3} a
 * @param  {number}  rad
 * @return {clay.Matrix3}
 */
Matrix3.rotate = function (out, a, rad) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    mat3.rotate(out.array, a.array, rad);</span>
<span class="cstat-no" title="statement not covered" >    out._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >    return out;</span>
};
&nbsp;
/**
 * @param  {clay.Matrix3} out
 * @param  {clay.Matrix3} a
 * @param  {clay.Vector2} v
 * @return {clay.Matrix3}
 */
Matrix3.scale = function (out, a, v) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    mat3.scale(out.array, a.array, v.array);</span>
<span class="cstat-no" title="statement not covered" >    out._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >    return out;</span>
};
&nbsp;
/**
 * @param  {clay.Matrix3} out
 * @param  {clay.Matrix3} a
 * @return {clay.Matrix3}
 */
Matrix3.transpose = function (out, a) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    mat3.transpose(out.array, a.array);</span>
<span class="cstat-no" title="statement not covered" >    out._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >    return out;</span>
};
&nbsp;
/**
 * @param  {clay.Matrix3} out
 * @param  {clay.Matrix3} a
 * @param  {clay.Vector2} v
 * @return {clay.Matrix3}
 */
Matrix3.translate = function (out, a, v) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    mat3.translate(out.array, a.array, v.array);</span>
<span class="cstat-no" title="statement not covered" >    out._dirty = true;</span>
<span class="cstat-no" title="statement not covered" >    return out;</span>
};
&nbsp;
export default Matrix3;
&nbsp;</pre></td></tr>
</table></pre>
<div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Fri Feb 23 2018 01:09:23 GMT+0800 (CST)
</div>
</div>
<script src="../../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../../sorter.js"></script>
</body>
</html>
