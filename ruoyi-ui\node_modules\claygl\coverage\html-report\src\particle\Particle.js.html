<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for src/particle/Particle.js</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../../index.html">all files</a> / <a href="index.html">src/particle/</a> Particle.js
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">18.75% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>3/16</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>0/4</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>0/2</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">18.75% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>3/16</span>
      </div>
    </div>
  </div>
  <div class='status-line low'></div>
<pre><table class="coverage">
<tr><td class="line-count quiet">1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71</td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">import Vector3 from '../math/Vector3';
import glMatrix from '../dep/glmatrix';
var vec3 = glMatrix.vec3;
&nbsp;
/**
 * @constructor
 * @alias clay.particle.Particle
 */
var Particle = function() <span class="fstat-no" title="function not covered" >{</span>
    /**
     * @type {clay.Vector3}
     */
<span class="cstat-no" title="statement not covered" >    this.position = new Vector3();</span>
&nbsp;
    /**
     * Use euler angle to represent particle rotation
     * @type {clay.Vector3}
     */
<span class="cstat-no" title="statement not covered" >    this.rotation = new Vector3();</span>
&nbsp;
    /**
     * @type {?clay.Vector3}
     */
<span class="cstat-no" title="statement not covered" >    this.velocity = null;</span>
&nbsp;
    /**
     * @type {?clay.Vector3}
     */
<span class="cstat-no" title="statement not covered" >    this.angularVelocity = null;</span>
&nbsp;
    /**
     * @type {number}
     */
<span class="cstat-no" title="statement not covered" >    this.life = 1;</span>
&nbsp;
    /**
     * @type {number}
     */
<span class="cstat-no" title="statement not covered" >    this.age = 0;</span>
&nbsp;
    /**
     * @type {number}
     */
<span class="cstat-no" title="statement not covered" >    this.spriteSize = 1;</span>
&nbsp;
    /**
     * @type {number}
     */
<span class="cstat-no" title="statement not covered" >    this.weight = 1;</span>
&nbsp;
    /**
     * @type {clay.particle.Emitter}
     */
<span class="cstat-no" title="statement not covered" >    this.emitter = null;</span>
};
&nbsp;
/**
 * Update particle position
 * @param  {number} deltaTime
 */
Particle.prototype.update = function(deltaTime) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    if (this.velocity) {</span>
<span class="cstat-no" title="statement not covered" >        vec3.scaleAndAdd(this.position.array, this.position.array, this.velocity.array, deltaTime);</span>
    }
<span class="cstat-no" title="statement not covered" >    if (this.angularVelocity) {</span>
<span class="cstat-no" title="statement not covered" >        vec3.scaleAndAdd(this.rotation.array, this.rotation.array, this.angularVelocity.array, deltaTime);</span>
    }
};
&nbsp;
export default Particle;
&nbsp;</pre></td></tr>
</table></pre>
<div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Fri Feb 23 2018 01:09:23 GMT+0800 (CST)
</div>
</div>
<script src="../../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../../sorter.js"></script>
</body>
</html>
