package com.ruoyi.ky.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 乘客信息对象 ky_passenger
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class Passenger extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 乘客ID */
    private Long passengerId;

    /** 航班ID */
    @Excel(name = "航班ID")
    private Long flightId;

    /** 航班号 */
    @Excel(name = "航班号")
    private String flightNumber;

    /** 乘客姓名 */
    @Excel(name = "乘客姓名")
    private String passengerName;

    /** 身份证号 */
    @Excel(name = "身份证号")
    private String idCard;

    /** 护照号 */
    @Excel(name = "护照号")
    private String passport;

    /** 性别（0男 1女） */
    @Excel(name = "性别", readConverterExp = "0=男,1=女")
    private String gender;

    /** 年龄 */
    @Excel(name = "年龄")
    private Integer age;

    /** 国籍 */
    @Excel(name = "国籍")
    private String nationality;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String phone;

    /** 座位号 */
    @Excel(name = "座位号")
    private String seatNumber;

    /** 舱位等级（0经济舱 1商务舱 2头等舱） */
    @Excel(name = "舱位等级", readConverterExp = "0=经济舱,1=商务舱,2=头等舱")
    private String cabinClass;

    /** 票务状态（0已订票 1已值机 2已登机 3已起飞） */
    @Excel(name = "票务状态", readConverterExp = "0=已订票,1=已值机,2=已登机,3=已起飞")
    private String ticketStatus;

    /** 是否重点人员（0否 1是） */
    @Excel(name = "是否重点人员", readConverterExp = "0=否,1=是")
    private String isKeyPerson;

    /** 重点人员类型（1涉恐 2涉毒 3涉黑 4逃犯 5精神病 6其他危险 7VIP） */
    @Excel(name = "重点人员类型")
    private String keyPersonType;

    /** 风险等级（1低风险 2中风险 3高风险） */
    @Excel(name = "风险等级")
    private String riskLevel;

    /** 登机时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "登机时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date boardingTime;

    /** 值机时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "值机时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date checkinTime;

    /** 特殊需求 */
    @Excel(name = "特殊需求")
    private String specialRequests;

    /** 紧急联系人 */
    @Excel(name = "紧急联系人")
    private String emergencyContact;

    /** 紧急联系人电话 */
    @Excel(name = "紧急联系人电话")
    private String emergencyPhone;

    public void setPassengerId(Long passengerId) 
    {
        this.passengerId = passengerId;
    }

    public Long getPassengerId() 
    {
        return passengerId;
    }
    public void setFlightId(Long flightId) 
    {
        this.flightId = flightId;
    }

    public Long getFlightId() 
    {
        return flightId;
    }
    public void setFlightNumber(String flightNumber) 
    {
        this.flightNumber = flightNumber;
    }

    public String getFlightNumber() 
    {
        return flightNumber;
    }
    public void setPassengerName(String passengerName) 
    {
        this.passengerName = passengerName;
    }

    public String getPassengerName() 
    {
        return passengerName;
    }
    public void setIdCard(String idCard) 
    {
        this.idCard = idCard;
    }

    public String getIdCard() 
    {
        return idCard;
    }
    public void setPassport(String passport) 
    {
        this.passport = passport;
    }

    public String getPassport() 
    {
        return passport;
    }
    public void setGender(String gender) 
    {
        this.gender = gender;
    }

    public String getGender() 
    {
        return gender;
    }
    public void setAge(Integer age) 
    {
        this.age = age;
    }

    public Integer getAge() 
    {
        return age;
    }
    public void setNationality(String nationality) 
    {
        this.nationality = nationality;
    }

    public String getNationality() 
    {
        return nationality;
    }
    public void setPhone(String phone) 
    {
        this.phone = phone;
    }

    public String getPhone() 
    {
        return phone;
    }
    public void setSeatNumber(String seatNumber) 
    {
        this.seatNumber = seatNumber;
    }

    public String getSeatNumber() 
    {
        return seatNumber;
    }
    public void setCabinClass(String cabinClass) 
    {
        this.cabinClass = cabinClass;
    }

    public String getCabinClass() 
    {
        return cabinClass;
    }
    public void setTicketStatus(String ticketStatus) 
    {
        this.ticketStatus = ticketStatus;
    }

    public String getTicketStatus() 
    {
        return ticketStatus;
    }
    public void setIsKeyPerson(String isKeyPerson) 
    {
        this.isKeyPerson = isKeyPerson;
    }

    public String getIsKeyPerson() 
    {
        return isKeyPerson;
    }
    public void setKeyPersonType(String keyPersonType) 
    {
        this.keyPersonType = keyPersonType;
    }

    public String getKeyPersonType() 
    {
        return keyPersonType;
    }
    public void setRiskLevel(String riskLevel) 
    {
        this.riskLevel = riskLevel;
    }

    public String getRiskLevel() 
    {
        return riskLevel;
    }
    public void setBoardingTime(Date boardingTime) 
    {
        this.boardingTime = boardingTime;
    }

    public Date getBoardingTime() 
    {
        return boardingTime;
    }
    public void setCheckinTime(Date checkinTime) 
    {
        this.checkinTime = checkinTime;
    }

    public Date getCheckinTime() 
    {
        return checkinTime;
    }
    public void setSpecialRequests(String specialRequests) 
    {
        this.specialRequests = specialRequests;
    }

    public String getSpecialRequests() 
    {
        return specialRequests;
    }
    public void setEmergencyContact(String emergencyContact) 
    {
        this.emergencyContact = emergencyContact;
    }

    public String getEmergencyContact() 
    {
        return emergencyContact;
    }
    public void setEmergencyPhone(String emergencyPhone) 
    {
        this.emergencyPhone = emergencyPhone;
    }

    public String getEmergencyPhone() 
    {
        return emergencyPhone;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("passengerId", getPassengerId())
            .append("flightId", getFlightId())
            .append("flightNumber", getFlightNumber())
            .append("passengerName", getPassengerName())
            .append("idCard", getIdCard())
            .append("passport", getPassport())
            .append("gender", getGender())
            .append("age", getAge())
            .append("nationality", getNationality())
            .append("phone", getPhone())
            .append("seatNumber", getSeatNumber())
            .append("cabinClass", getCabinClass())
            .append("ticketStatus", getTicketStatus())
            .append("isKeyPerson", getIsKeyPerson())
            .append("keyPersonType", getKeyPersonType())
            .append("riskLevel", getRiskLevel())
            .append("boardingTime", getBoardingTime())
            .append("checkinTime", getCheckinTime())
            .append("specialRequests", getSpecialRequests())
            .append("emergencyContact", getEmergencyContact())
            .append("emergencyPhone", getEmergencyPhone())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
