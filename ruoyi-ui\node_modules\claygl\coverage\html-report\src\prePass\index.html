<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for src/prePass/</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../../index.html">all files</a> src/prePass/
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">71.84% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>324/451</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">57.04% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>81/142</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">77.42% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>24/31</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">72% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>324/450</span>
      </div>
    </div>
  </div>
  <div class='status-line medium'></div>
<div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file high" data-value="EnvironmentMap.js"><a href="EnvironmentMap.js.html">EnvironmentMap.js</a></td>
	<td data-value="86.11" class="pic high"><div class="chart"><div class="cover-fill" style="width: 86%;"></div><div class="cover-empty" style="width:14%;"></div></div></td>
	<td data-value="86.11" class="pct high">86.11%</td>
	<td data-value="36" class="abs high">31/36</td>
	<td data-value="50" class="pct medium">50%</td>
	<td data-value="4" class="abs medium">2/4</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="4" class="abs high">4/4</td>
	<td data-value="86.11" class="pct high">86.11%</td>
	<td data-value="36" class="abs high">31/36</td>
	</tr>

<tr>
	<td class="file medium" data-value="ShadowMap.js"><a href="ShadowMap.js.html">ShadowMap.js</a></td>
	<td data-value="70.6" class="pic medium"><div class="chart"><div class="cover-fill" style="width: 70%;"></div><div class="cover-empty" style="width:30%;"></div></div></td>
	<td data-value="70.6" class="pct medium">70.6%</td>
	<td data-value="415" class="abs medium">293/415</td>
	<td data-value="57.25" class="pct medium">57.25%</td>
	<td data-value="138" class="abs medium">79/138</td>
	<td data-value="74.07" class="pct medium">74.07%</td>
	<td data-value="27" class="abs medium">20/27</td>
	<td data-value="70.77" class="pct medium">70.77%</td>
	<td data-value="414" class="abs medium">293/414</td>
	</tr>

</tbody>
</table>
</div><div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Fri Feb 23 2018 01:09:23 GMT+0800 (CST)
</div>
</div>
<script src="../../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../../sorter.js"></script>
</body>
</html>
