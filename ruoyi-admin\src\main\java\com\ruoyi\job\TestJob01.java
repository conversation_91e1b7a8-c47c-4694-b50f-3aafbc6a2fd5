//package com.ruoyi.job;
//
//import com.github.kangarooxin.spring.boot.starter.elastic.job3.annotation.ElasticJobScheduler;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.shardingsphere.elasticjob.api.ShardingContext;
//import org.apache.shardingsphere.elasticjob.simple.job.SimpleJob;
//
//@Slf4j
//@ElasticJobScheduler(name = "applyTestJob1", cron = "0/10 * * * * ? *")
//public class TestJob01 implements SimpleJob {
//    @Override
//    public void execute(ShardingContext shardingContext) {
//        log.info("ApplyJobTest start, time:{}", System.nanoTime());
//
//        try {
//            Thread.sleep(5000);
//        } catch (InterruptedException e) {
//            e.printStackTrace();
//        }
//
//        log.info("ApplyJobTest end, time:{}", System.nanoTime());
//    }
//}
