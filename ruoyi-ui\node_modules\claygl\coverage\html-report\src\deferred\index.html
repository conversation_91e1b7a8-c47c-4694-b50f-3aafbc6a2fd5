<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for src/deferred/</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../../index.html">all files</a> src/deferred/
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">4.7% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>22/468</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">0% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>0/179</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">5.41% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>2/37</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">4.7% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>22/468</span>
      </div>
    </div>
  </div>
  <div class='status-line low'></div>
<div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="GBuffer.js"><a href="GBuffer.js.html">GBuffer.js</a></td>
	<td data-value="1.54" class="pic low"><div class="chart"><div class="cover-fill" style="width: 1%;"></div><div class="cover-empty" style="width:99%;"></div></div></td>
	<td data-value="1.54" class="pct low">1.54%</td>
	<td data-value="195" class="abs low">3/195</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="84" class="abs low">0/84</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="18" class="abs low">0/18</td>
	<td data-value="1.54" class="pct low">1.54%</td>
	<td data-value="195" class="abs low">3/195</td>
	</tr>

<tr>
	<td class="file low" data-value="Renderer.js"><a href="Renderer.js.html">Renderer.js</a></td>
	<td data-value="6.96" class="pic low"><div class="chart"><div class="cover-fill" style="width: 6%;"></div><div class="cover-empty" style="width:94%;"></div></div></td>
	<td data-value="6.96" class="pct low">6.96%</td>
	<td data-value="273" class="abs low">19/273</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="95" class="abs low">0/95</td>
	<td data-value="10.53" class="pct low">10.53%</td>
	<td data-value="19" class="abs low">2/19</td>
	<td data-value="6.96" class="pct low">6.96%</td>
	<td data-value="273" class="abs low">19/273</td>
	</tr>

</tbody>
</table>
</div><div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Fri Feb 23 2018 01:09:23 GMT+0800 (CST)
</div>
</div>
<script src="../../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../../sorter.js"></script>
</body>
</html>
