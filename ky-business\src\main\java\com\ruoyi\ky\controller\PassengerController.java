package com.ruoyi.ky.controller;

import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.ky.domain.Passenger;
import com.ruoyi.ky.service.IPassengerService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 乘客信息Controller
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@RestController
@RequestMapping("/ky/passenger")
public class PassengerController extends BaseController
{
    @Autowired
    private IPassengerService passengerService;

    /**
     * 查询乘客信息列表
     */
    @PreAuthorize("@ss.hasPermi('ky:passenger:list')")
    @GetMapping("/list")
    public TableDataInfo list(Passenger passenger)
    {
        startPage();
        List<Passenger> list = passengerService.selectPassengerList(passenger);
        return getDataTable(list);
    }

    /**
     * 根据航班ID查询乘客列表
     */
    @PreAuthorize("@ss.hasPermi('ky:passenger:list')")
    @GetMapping("/flight/{flightId}")
    public AjaxResult getPassengersByFlightId(@PathVariable("flightId") Long flightId)
    {
        List<Passenger> list = passengerService.selectPassengerListByFlightId(flightId);
        return AjaxResult.success(list);
    }

    /**
     * 根据航班号查询乘客列表
     */
    @PreAuthorize("@ss.hasPermi('ky:passenger:list')")
    @GetMapping("/flightNumber/{flightNumber}")
    public AjaxResult getPassengersByFlightNumber(@PathVariable("flightNumber") String flightNumber)
    {
        List<Passenger> list = passengerService.selectPassengerListByFlightNumber(flightNumber);
        return AjaxResult.success(list);
    }

    /**
     * 查询重点人员乘客列表
     */
    @PreAuthorize("@ss.hasPermi('ky:passenger:list')")
    @GetMapping("/keyPersons")
    public TableDataInfo getKeyPersons(Passenger passenger)
    {
        startPage();
        List<Passenger> list = passengerService.selectKeyPersonList(passenger);
        return getDataTable(list);
    }

    /**
     * 根据座位号查询乘客信息
     */
    @PreAuthorize("@ss.hasPermi('ky:passenger:query')")
    @GetMapping("/seat/{flightId}/{seatNumber}")
    public AjaxResult getPassengerBySeat(@PathVariable("flightId") Long flightId, 
                                        @PathVariable("seatNumber") String seatNumber)
    {
        Passenger passenger = passengerService.selectPassengerBySeat(flightId, seatNumber);
        return AjaxResult.success(passenger);
    }

    /**
     * 获取航班舱位分布数据
     */
    @PreAuthorize("@ss.hasPermi('ky:passenger:list')")
    @GetMapping("/cabin/layout/{flightId}")
    public AjaxResult getCabinLayoutData(@PathVariable("flightId") Long flightId)
    {
        Map<String, Object> data = passengerService.getCabinLayoutData(flightId);
        return AjaxResult.success(data);
    }

    /**
     * 获取航班舱位统计信息
     */
    @PreAuthorize("@ss.hasPermi('ky:passenger:list')")
    @GetMapping("/cabin/statistics/{flightId}")
    public AjaxResult getCabinStatistics(@PathVariable("flightId") Long flightId)
    {
        Map<String, Object> statistics = passengerService.getCabinStatistics(flightId);
        return AjaxResult.success(statistics);
    }

    /**
     * 获取重点人员统计信息
     */
    @PreAuthorize("@ss.hasPermi('ky:passenger:list')")
    @GetMapping("/keyPerson/statistics/{flightId}")
    public AjaxResult getKeyPersonStatistics(@PathVariable("flightId") Long flightId)
    {
        Map<String, Object> statistics = passengerService.getKeyPersonStatistics(flightId);
        return AjaxResult.success(statistics);
    }

    /**
     * 导出乘客信息列表
     */
    @PreAuthorize("@ss.hasPermi('ky:passenger:export')")
    @Log(title = "乘客信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Passenger passenger)
    {
        List<Passenger> list = passengerService.selectPassengerList(passenger);
        ExcelUtil<Passenger> util = new ExcelUtil<Passenger>(Passenger.class);
        util.exportExcel(response, list, "乘客信息数据");
    }

    /**
     * 获取乘客信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('ky:passenger:query')")
    @GetMapping(value = "/{passengerId}")
    public AjaxResult getInfo(@PathVariable("passengerId") Long passengerId)
    {
        return AjaxResult.success(passengerService.selectPassengerByPassengerId(passengerId));
    }

    /**
     * 根据身份证号查询乘客信息
     */
    @PreAuthorize("@ss.hasPermi('ky:passenger:query')")
    @GetMapping("/idCard/{idCard}")
    public AjaxResult getPassengerByIdCard(@PathVariable("idCard") String idCard)
    {
        Passenger passenger = passengerService.selectPassengerByIdCard(idCard);
        return AjaxResult.success(passenger);
    }

    /**
     * 新增乘客信息
     */
    @PreAuthorize("@ss.hasPermi('ky:passenger:add')")
    @Log(title = "乘客信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Passenger passenger)
    {
        return toAjax(passengerService.insertPassenger(passenger));
    }

    /**
     * 修改乘客信息
     */
    @PreAuthorize("@ss.hasPermi('ky:passenger:edit')")
    @Log(title = "乘客信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Passenger passenger)
    {
        return toAjax(passengerService.updatePassenger(passenger));
    }

    /**
     * 更新乘客票务状态
     */
    @PreAuthorize("@ss.hasPermi('ky:passenger:edit')")
    @Log(title = "更新票务状态", businessType = BusinessType.UPDATE)
    @PutMapping("/ticketStatus/{passengerId}/{ticketStatus}")
    public AjaxResult updateTicketStatus(@PathVariable("passengerId") Long passengerId,
                                       @PathVariable("ticketStatus") String ticketStatus)
    {
        return toAjax(passengerService.updatePassengerTicketStatus(passengerId, ticketStatus));
    }

    /**
     * 批量更新航班乘客登机状态
     */
    @PreAuthorize("@ss.hasPermi('ky:passenger:edit')")
    @Log(title = "批量更新登机状态", businessType = BusinessType.UPDATE)
    @PutMapping("/batchTicketStatus/{flightId}/{ticketStatus}")
    public AjaxResult batchUpdateTicketStatus(@PathVariable("flightId") Long flightId,
                                            @PathVariable("ticketStatus") String ticketStatus)
    {
        return toAjax(passengerService.batchUpdateTicketStatus(flightId, ticketStatus));
    }

    /**
     * 删除乘客信息
     */
    @PreAuthorize("@ss.hasPermi('ky:passenger:remove')")
    @Log(title = "乘客信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{passengerIds}")
    public AjaxResult remove(@PathVariable Long[] passengerIds)
    {
        return toAjax(passengerService.deletePassengerByPassengerIds(passengerIds));
    }
}
