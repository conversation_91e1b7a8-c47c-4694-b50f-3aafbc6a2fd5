<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for src/animation/Animator.js</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../../index.html">all files</a> / <a href="index.html">src/animation/</a> Animator.js
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">52.4% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>109/208</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">39.47% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>45/114</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">57.69% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>15/26</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">52.4% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>109/208</span>
      </div>
    </div>
  </div>
  <div class='status-line medium'></div>
<pre><table class="coverage">
<tr><td class="line-count quiet">1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
116
117
118
119
120
121
122
123
124
125
126
127
128
129
130
131
132
133
134
135
136
137
138
139
140
141
142
143
144
145
146
147
148
149
150
151
152
153
154
155
156
157
158
159
160
161
162
163
164
165
166
167
168
169
170
171
172
173
174
175
176
177
178
179
180
181
182
183
184
185
186
187
188
189
190
191
192
193
194
195
196
197
198
199
200
201
202
203
204
205
206
207
208
209
210
211
212
213
214
215
216
217
218
219
220
221
222
223
224
225
226
227
228
229
230
231
232
233
234
235
236
237
238
239
240
241
242
243
244
245
246
247
248
249
250
251
252
253
254
255
256
257
258
259
260
261
262
263
264
265
266
267
268
269
270
271
272
273
274
275
276
277
278
279
280
281
282
283
284
285
286
287
288
289
290
291
292
293
294
295
296
297
298
299
300
301
302
303
304
305
306
307
308
309
310
311
312
313
314
315
316
317
318
319
320
321
322
323
324
325
326
327
328
329
330
331
332
333
334
335
336
337
338
339
340
341
342
343
344
345
346
347
348
349
350
351
352
353
354
355
356
357
358
359
360
361
362
363
364
365
366
367
368
369
370
371
372
373
374
375
376
377
378
379
380
381
382
383
384
385
386
387
388
389
390
391
392
393
394
395
396
397
398
399
400
401
402
403
404
405
406
407
408
409
410
411
412
413
414
415
416
417
418
419
420
421
422
423
424
425
426
427
428
429
430
431
432
433
434
435
436
437
438
439
440
441
442
443
444
445
446
447
448
449
450
451
452
453
454
455
456
457
458
459
460
461
462
463
464
465
466
467
468
469
470
471
472
473
474
475
476
477
478
479
480
481
482
483
484
485
486
487
488
489
490
491
492
493
494
495
496
497
498
499
500
501
502
503
504
505
506
507
508
509
510
511
512
513
514
515
516
517
518
519
520
521
522
523
524
525
526
527
528
529
530
531
532
533
534
535
536
537
538
539
540
541
542
543
544
545
546
547
548
549
550
551
552
553
554
555
556
557
558
559
560
561
562
563
564
565
566
567</td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">60×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">60×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">6×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-yes">5×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-yes">8×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">8×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">8×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">5×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">8×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">8×</span>
<span class="cline-any cline-yes">8×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-yes">5×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">5×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">60×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">60×</span>
<span class="cline-any cline-yes">122×</span>
<span class="cline-any cline-yes">45×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">60×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">60×</span>
<span class="cline-any cline-yes">60×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">60×</span>
<span class="cline-any cline-yes">60×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">60×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">60×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">60×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">60×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">60×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">60×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">60×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">60×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-yes">5×</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">5×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-yes">20×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">20×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1×</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">import Clip from './Clip';
import easingFuncs from './easing';
&nbsp;
var arraySlice = Array.prototype.slice;
&nbsp;
function defaultGetter(target, key) {
    return target[key];
}
function defaultSetter(target, key, value) {
    target[key] = value;
}
&nbsp;
function interpolateNumber(p0, p1, percent) {
    return (p1 - p0) * percent + p0;
}
&nbsp;
function interpolateArray(p0, p1, percent, out, arrDim) <span class="fstat-no" title="function not covered" >{</span>
    var len = <span class="cstat-no" title="statement not covered" >p0.length;</span>
<span class="cstat-no" title="statement not covered" >    if (arrDim == 1) {</span>
<span class="cstat-no" title="statement not covered" >        for (var i = 0; i &lt; len; i++) {</span>
<span class="cstat-no" title="statement not covered" >            out[i] = interpolateNumber(p0[i], p1[i], percent);</span>
        }
    }
    else {
        var len2 = <span class="cstat-no" title="statement not covered" >p0[0].length;</span>
<span class="cstat-no" title="statement not covered" >        for (var i = 0; i &lt; len; i++) {</span>
<span class="cstat-no" title="statement not covered" >            for (var j = 0; j &lt; len2; j++) {</span>
<span class="cstat-no" title="statement not covered" >                out[i][j] = interpolateNumber(</span>
                    p0[i][j], p1[i][j], percent
                );
            }
        }
    }
}
&nbsp;
function isArrayLike(data) {
    <span class="missing-if-branch" title="if path not taken" >I</span>if (typeof(data) == 'undefined') {
<span class="cstat-no" title="statement not covered" >        return false;</span>
    } else <span class="missing-if-branch" title="if path not taken" >I</span>if (typeof(data) == 'string') {
<span class="cstat-no" title="statement not covered" >        return false;</span>
    } else {
        return typeof(data.length) == 'number';
    }
}
&nbsp;
function cloneValue(value) {
    <span class="missing-if-branch" title="if path not taken" >I</span>if (isArrayLike(value)) {
        var len = <span class="cstat-no" title="statement not covered" >value.length;</span>
<span class="cstat-no" title="statement not covered" >        if (isArrayLike(value[0])) {</span>
            var ret = <span class="cstat-no" title="statement not covered" >[];</span>
<span class="cstat-no" title="statement not covered" >            for (var i = 0; i &lt; len; i++) {</span>
<span class="cstat-no" title="statement not covered" >                ret.push(arraySlice.call(value[i]));</span>
            }
<span class="cstat-no" title="statement not covered" >            return ret;</span>
        } else {
<span class="cstat-no" title="statement not covered" >            return arraySlice.call(value);</span>
        }
    } else {
        return value;
    }
}
&nbsp;
function catmullRomInterpolateArray(
    p0, p1, p2, p3, t, t2, t3, out, arrDim
) <span class="fstat-no" title="function not covered" >{</span>
    var len = <span class="cstat-no" title="statement not covered" >p0.length;</span>
<span class="cstat-no" title="statement not covered" >    if (arrDim == 1) {</span>
<span class="cstat-no" title="statement not covered" >        for (var i = 0; i &lt; len; i++) {</span>
<span class="cstat-no" title="statement not covered" >            out[i] = catmullRomInterpolate(</span>
                p0[i], p1[i], p2[i], p3[i], t, t2, t3
            );
        }
    } else {
        var len2 = <span class="cstat-no" title="statement not covered" >p0[0].length;</span>
<span class="cstat-no" title="statement not covered" >        for (var i = 0; i &lt; len; i++) {</span>
<span class="cstat-no" title="statement not covered" >            for (var j = 0; j &lt; len2; j++) {</span>
<span class="cstat-no" title="statement not covered" >                out[i][j] = catmullRomInterpolate(</span>
                    p0[i][j], p1[i][j], p2[i][j], p3[i][j],
                    t, t2, t3
                );
            }
        }
    }
}
&nbsp;
function catmullRomInterpolate(p0, p1, p2, p3, t, t2, t3) <span class="fstat-no" title="function not covered" >{</span>
    var v0 = <span class="cstat-no" title="statement not covered" >(p2 - p0) * 0.5;</span>
    var v1 = <span class="cstat-no" title="statement not covered" >(p3 - p1) * 0.5;</span>
<span class="cstat-no" title="statement not covered" >    return (2 * (p1 - p2) + v0 + v1) * t3</span>
            + (- 3 * (p1 - p2) - 2 * v0 - v1) * t2
            + v0 * t + p1;
}
&nbsp;
// arr0 is source array, arr1 is target array.
// Do some preprocess to avoid error happened when interpolating from arr0 to arr1
function fillArr(arr0, arr1, arrDim) <span class="fstat-no" title="function not covered" >{</span>
    var arr0Len = <span class="cstat-no" title="statement not covered" >arr0.length;</span>
    var arr1Len = <span class="cstat-no" title="statement not covered" >arr1.length;</span>
<span class="cstat-no" title="statement not covered" >    if (arr0Len !== arr1Len) {</span>
        // FIXME Not work for TypedArray
        var isPreviousLarger = <span class="cstat-no" title="statement not covered" >arr0Len &gt; arr1Len;</span>
<span class="cstat-no" title="statement not covered" >        if (isPreviousLarger) {</span>
            // Cut the previous
<span class="cstat-no" title="statement not covered" >            arr0.length = arr1Len;</span>
        }
        else {
            // Fill the previous
<span class="cstat-no" title="statement not covered" >            for (var i = arr0Len; i &lt; arr1Len; i++) {</span>
<span class="cstat-no" title="statement not covered" >                arr0.push(</span>
                    arrDim === 1 ? arr1[i] : arraySlice.call(arr1[i])
                );
            }
        }
    }
    // Handling NaN value
    var len2 = <span class="cstat-no" title="statement not covered" >arr0[0] &amp;&amp; arr0[0].length;</span>
<span class="cstat-no" title="statement not covered" >    for (var i = 0; i &lt; arr0.length; i++) {</span>
<span class="cstat-no" title="statement not covered" >        if (arrDim === 1) {</span>
<span class="cstat-no" title="statement not covered" >            if (isNaN(arr0[i])) {</span>
<span class="cstat-no" title="statement not covered" >                arr0[i] = arr1[i];</span>
            }
        }
        else {
<span class="cstat-no" title="statement not covered" >            for (var j = 0; j &lt; len2; j++) {</span>
<span class="cstat-no" title="statement not covered" >                if (isNaN(arr0[i][j])) {</span>
<span class="cstat-no" title="statement not covered" >                    arr0[i][j] = arr1[i][j];</span>
                }
            }
        }
    }
}
&nbsp;
function isArraySame(arr0, arr1, arrDim) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >    if (arr0 === arr1) {</span>
<span class="cstat-no" title="statement not covered" >        return true;</span>
    }
    var len = <span class="cstat-no" title="statement not covered" >arr0.length;</span>
<span class="cstat-no" title="statement not covered" >    if (len !== arr1.length) {</span>
<span class="cstat-no" title="statement not covered" >        return false;</span>
    }
<span class="cstat-no" title="statement not covered" >    if (arrDim === 1) {</span>
<span class="cstat-no" title="statement not covered" >        for (var i = 0; i &lt; len; i++) {</span>
<span class="cstat-no" title="statement not covered" >            if (arr0[i] !== arr1[i]) {</span>
<span class="cstat-no" title="statement not covered" >                return false;</span>
            }
        }
    }
    else {
        var len2 = <span class="cstat-no" title="statement not covered" >arr0[0].length;</span>
<span class="cstat-no" title="statement not covered" >        for (var i = 0; i &lt; len; i++) {</span>
<span class="cstat-no" title="statement not covered" >            for (var j = 0; j &lt; len2; j++) {</span>
<span class="cstat-no" title="statement not covered" >                if (arr0[i][j] !== arr1[i][j]) {</span>
<span class="cstat-no" title="statement not covered" >                    return false;</span>
                }
            }
        }
    }
<span class="cstat-no" title="statement not covered" >    return true;</span>
}
&nbsp;
function createTrackClip(animator, globalEasing, oneTrackDone, keyframes, propName, interpolater, maxTime) {
    var getter = animator._getter;
    var setter = animator._setter;
    var useSpline = globalEasing === 'spline';
&nbsp;
    var trackLen = keyframes.length;
    <span class="missing-if-branch" title="if path not taken" >I</span>if (!trackLen) {
<span class="cstat-no" title="statement not covered" >        return;</span>
    }
    // Guess data type
    var firstVal = keyframes[0].value;
    var isValueArray = isArrayLike(firstVal);
&nbsp;
    // For vertices morphing
    var arrDim = (
            isValueArray
            &amp;&amp; <span class="branch-1 cbranch-no" title="branch not covered" >isArrayLike(firstVal[0])</span>
        )
        ? <span class="branch-0 cbranch-no" title="branch not covered" >2 </span>: 1;
    // Sort keyframe as ascending
    keyframes.sort(function(a, b) {
        return a.time - b.time;
    });
&nbsp;
    // Percents of each keyframe
    var kfPercents = [];
    // Value of each keyframe
    var kfValues = [];
    // Easing funcs of each keyframe.
    var kfEasings = [];
&nbsp;
    var prevValue = keyframes[0].value;
    var isAllValueEqual = true;
    for (var i = 0; i &lt; trackLen; i++) {
        kfPercents.push(keyframes[i].time / maxTime);
&nbsp;
        // Assume value is a color when it is a string
        var value = keyframes[i].value;
&nbsp;
        // Check if value is equal, deep check if value is array
        if (!((isValueArray &amp;&amp; <span class="branch-1 cbranch-no" title="branch not covered" >isArraySame(value, prevValue, arrDim))</span>
            || (!isValueArray &amp;&amp; value === prevValue))) {
            isAllValueEqual = false;
        }
        prevValue = value;
&nbsp;
        kfValues.push(value);
        kfEasings.push(keyframes[i].easing);
    }
    <span class="missing-if-branch" title="if path not taken" >I</span>if (isAllValueEqual) {
<span class="cstat-no" title="statement not covered" >        return;</span>
    }
&nbsp;
    var lastValue = kfValues[trackLen - 1];
    // Polyfill array and NaN value
    for (var i = 0; i &lt; trackLen - 1; i++) {
        <span class="missing-if-branch" title="if path not taken" >I</span>if (isValueArray) {
<span class="cstat-no" title="statement not covered" >            fillArr(kfValues[i], lastValue, arrDim);</span>
        }
        else {
            <span class="missing-if-branch" title="if path not taken" >I</span>if (isNaN(kfValues[i]) &amp;&amp; <span class="branch-1 cbranch-no" title="branch not covered" >!isNaN(lastValue))</span> {
<span class="cstat-no" title="statement not covered" >                kfValues[i] = lastValue;</span>
            }
        }
    }
    isValueArray &amp;&amp; <span class="branch-1 cbranch-no" title="branch not covered" >fillArr(getter(animator._target, propName), lastValue, arrDim);</span>
&nbsp;
    // Cache the key of last frame to speed up when
    // animation playback is sequency
    var cacheKey = 0;
    var cachePercent = 0;
    var start;
    var i, w;
    var p0, p1, p2, p3;
&nbsp;
    var onframe = function(target, percent) {
        // Find the range keyframes
        // kf1-----kf2---------current--------kf3
        // find kf2(i) and kf3(i + 1) and do interpolation
        <span class="missing-if-branch" title="if path not taken" >I</span>if (percent &lt; cachePercent) {
            // Start from next key
<span class="cstat-no" title="statement not covered" >            start = Math.min(cacheKey + 1, trackLen - 1);</span>
<span class="cstat-no" title="statement not covered" >            for (i = start; i &gt;= 0; i--) {</span>
<span class="cstat-no" title="statement not covered" >                if (kfPercents[i] &lt;= percent) {</span>
<span class="cstat-no" title="statement not covered" >                    break;</span>
                }
            }
<span class="cstat-no" title="statement not covered" >            i = Math.min(i, trackLen - 2);</span>
        }
        else {
            for (i = cacheKey; i &lt; trackLen; i++) {
                if (kfPercents[i] &gt; percent) {
                    break;
                }
            }
            i = Math.min(i - 1, trackLen - 2);
        }
        cacheKey = i;
        cachePercent = percent;
&nbsp;
        var range = (kfPercents[i + 1] - kfPercents[i]);
        <span class="missing-if-branch" title="if path not taken" >I</span>if (range === 0) {
<span class="cstat-no" title="statement not covered" >            return;</span>
        }
        else {
            w = (percent - kfPercents[i]) / range;
            // Clamp 0 - 1
            w = Math.max(Math.min(1, w), 0);
        }
        w = kfEasings[i + 1](w);
&nbsp;
        <span class="missing-if-branch" title="if path not taken" >I</span>if (useSpline) {
<span class="cstat-no" title="statement not covered" >            p1 = kfValues[i];</span>
<span class="cstat-no" title="statement not covered" >            p0 = kfValues[i === 0 ? i : i - 1];</span>
<span class="cstat-no" title="statement not covered" >            p2 = kfValues[i &gt; trackLen - 2 ? trackLen - 1 : i + 1];</span>
<span class="cstat-no" title="statement not covered" >            p3 = kfValues[i &gt; trackLen - 3 ? trackLen - 1 : i + 2];</span>
<span class="cstat-no" title="statement not covered" >            if (interpolater) {</span>
<span class="cstat-no" title="statement not covered" >                setter(</span>
                    target,
                    propName,
                    interpolater(
                        getter(target, propName),
                        p0, p1, p2, p3, w
                    )
                );
            }
            else <span class="cstat-no" title="statement not covered" >if (isValueArray) {</span>
<span class="cstat-no" title="statement not covered" >                catmullRomInterpolateArray(</span>
                    p0, p1, p2, p3, w, w*w, w*w*w,
                    getter(target, propName),
                    arrDim
                );
            }
            else {
<span class="cstat-no" title="statement not covered" >                setter(</span>
                    target,
                    propName,
                    catmullRomInterpolate(p0, p1, p2, p3, w, w*w, w*w*w)
                );
            }
        }
        else {
            <span class="missing-if-branch" title="if path not taken" >I</span>if (interpolater) {
<span class="cstat-no" title="statement not covered" >                setter(</span>
                    target,
                    propName,
                    interpolater(
                        getter(target, propName),
                        kfValues[i],
                        kfValues[i + 1],
                        w
                    )
                );
            }
&nbsp;
            else <span class="missing-if-branch" title="if path not taken" >I</span>if (isValueArray) {
<span class="cstat-no" title="statement not covered" >                interpolateArray(</span>
                    kfValues[i], kfValues[i+1], w,
                    getter(target, propName),
                    arrDim
                );
            }
            else {
                setter(
                    target,
                    propName,
                    interpolateNumber(kfValues[i], kfValues[i+1], w)
                );
            }
        }
    };
&nbsp;
    var clip = new Clip({
        target: animator._target,
        life: maxTime,
        loop: animator._loop,
        delay: animator._delay,
        onframe: onframe,
        onfinish: oneTrackDone
    });
&nbsp;
    <span class="missing-if-branch" title="if path not taken" >I</span>if (globalEasing &amp;&amp; <span class="branch-1 cbranch-no" title="branch not covered" >globalEasing !== 'spline')</span> {
<span class="cstat-no" title="statement not covered" >        clip.setEasing(globalEasing);</span>
    }
&nbsp;
    return clip;
}
&nbsp;
/**
 * @description Animator object can only be created by Animation.prototype.animate method.
 * After created, we can use {@link clay.animation.Animator#when} to add all keyframes and {@link clay.animation.Animator#start} it.
 * Clips will be automatically created and added to the animation instance which created this deferred object.
 *
 * @constructor clay.animation.Animator
 *
 * @param {Object} target
 * @param {boolean} loop
 * @param {Function} getter
 * @param {Function} setter
 * @param {Function} interpolater
 */
function Animator(target, loop, getter, setter, interpolater) {
    this._tracks = {};
    this._target = target;
&nbsp;
    this._loop = loop || false;
&nbsp;
    this._getter = getter || defaultGetter;
    this._setter = setter || defaultSetter;
&nbsp;
    this._interpolater = interpolater || null;
&nbsp;
    this._delay = 0;
&nbsp;
    this._doneList = [];
&nbsp;
    this._onframeList = [];
&nbsp;
    this._clipList = [];
&nbsp;
    this._maxTime = 0;
&nbsp;
    this._lastKFTime = 0;
}
&nbsp;
function noopEasing(w) {
    return w;
}
&nbsp;
Animator.prototype = {
&nbsp;
    constructor: Animator,
&nbsp;
    /**
     * @param {number} time Keyframe time using millisecond
     * @param {Object} props A key-value object. Value can be number, 1d and 2d array
     * @param {string|Function} [easing]
     * @return {clay.animation.Animator}
     * @memberOf clay.animation.Animator.prototype
     */
    when: function (time, props, easing) {
&nbsp;
        this._maxTime = Math.max(time, this._maxTime);
&nbsp;
        easing = (typeof easing === 'function' ? <span class="branch-0 cbranch-no" title="branch not covered" >easing </span>: easingFuncs[easing]) || noopEasing;
        for (var propName in props) {
            if (!this._tracks[propName]) {
                this._tracks[propName] = [];
                // If time is 0
                //  Then props is given initialize value
                // Else
                //  Initialize value from current prop value
                <span class="missing-if-branch" title="else path not taken" >E</span>if (time !== 0) {
                    this._tracks[propName].push({
                        time: 0,
                        value: cloneValue(
                            this._getter(this._target, propName)
                        ),
                        easing: easing
                    });
                }
            }
            this._tracks[propName].push({
                time: parseInt(time),
                value: props[propName],
                easing: easing
            });
        }
        return this;
    },
    /**
     * @param {number} time During time since last keyframe
     * @param {Object} props A key-value object. Value can be number, 1d and 2d array
     * @param {string|Function} [easing]
     * @return {clay.animation.Animator}
     * @memberOf clay.animation.Animator.prototype
     */
    then: function (duringTime, props, easing) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        this.when(duringTime + this._lastKFTime, props, easing);</span>
<span class="cstat-no" title="statement not covered" >        this._lastKFTime += duringTime;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    },
    /**
     * callback when running animation
     * @param  {Function} callback callback have two args, animating target and current percent
     * @return {clay.animation.Animator}
     * @memberOf clay.animation.Animator.prototype
     */
    during: function (callback) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        this._onframeList.push(callback);</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    },
&nbsp;
    _doneCallback: function () {
        // Clear all tracks
        this._tracks = {};
        // Clear all clips
        this._clipList.length = 0;
&nbsp;
        var doneList = this._doneList;
        var len = doneList.length;
        for (var i = 0; i &lt; len; i++) {
<span class="cstat-no" title="statement not covered" >            doneList[i].call(this);</span>
        }
    },
    /**
     * Start the animation
     * @param  {string|Function} easing
     * @return {clay.animation.Animator}
     * @memberOf clay.animation.Animator.prototype
     */
    start: function (globalEasing) {
&nbsp;
        var self = this;
        var clipCount = 0;
&nbsp;
        var oneTrackDone = function() {
            clipCount--;
            if (clipCount === 0) {
                self._doneCallback();
            }
        };
&nbsp;
        var lastClip;
        var clipMaxTime = 0;
        for (var propName in this._tracks) {
            var clip = createTrackClip(
                this, globalEasing, oneTrackDone,
                this._tracks[propName], propName, self._interpolater, self._maxTime
            );
            <span class="missing-if-branch" title="else path not taken" >E</span>if (clip) {
                clipMaxTime = Math.max(clipMaxTime, clip.life);
                this._clipList.push(clip);
                clipCount++;
&nbsp;
                // If start after added to animation
                <span class="missing-if-branch" title="else path not taken" >E</span>if (this.animation) {
                    this.animation.addClip(clip);
                }
&nbsp;
                lastClip = clip;
            }
        }
&nbsp;
        // Add during callback on the last clip
        <span class="missing-if-branch" title="else path not taken" >E</span>if (lastClip) {
            var oldOnFrame = lastClip.onframe;
            lastClip.onframe = function (target, percent) {
                oldOnFrame(target, percent);
&nbsp;
                for (var i = 0; i &lt; self._onframeList.length; i++) {
<span class="cstat-no" title="statement not covered" >                    self._onframeList[i](target, percent);</span>
                }
            };
        }
&nbsp;
        <span class="missing-if-branch" title="if path not taken" >I</span>if (!clipCount) {
<span class="cstat-no" title="statement not covered" >            this._doneCallback();</span>
        }
        return this;
    },
&nbsp;
    /**
     * Stop the animation
     * @memberOf clay.animation.Animator.prototype
     */
    stop: function () <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        for (var i = 0; i &lt; this._clipList.length; i++) {</span>
            var clip = <span class="cstat-no" title="statement not covered" >this._clipList[i];</span>
<span class="cstat-no" title="statement not covered" >            this.animation.removeClip(clip);</span>
        }
<span class="cstat-no" title="statement not covered" >        this._clipList = [];</span>
    },
    /**
     * Delay given milliseconds
     * @param  {number} time
     * @return {clay.animation.Animator}
     * @memberOf clay.animation.Animator.prototype
     */
    delay: function (time)<span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        this._delay = time;</span>
<span class="cstat-no" title="statement not covered" >        return this;</span>
    },
    /**
     * Callback after animation finished
     * @param {Function} func
     * @return {clay.animation.Animator}
     * @memberOf clay.animation.Animator.prototype
     */
    done: function (func) <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        if (func) {</span>
<span class="cstat-no" title="statement not covered" >            this._doneList.push(func);</span>
        }
<span class="cstat-no" title="statement not covered" >        return this;</span>
    },
    /**
     * Get all clips created in start method.
     * @return {clay.animation.Clip[]}
     * @memberOf clay.animation.Animator.prototype
     */
    getClips: function () <span class="fstat-no" title="function not covered" >{</span>
<span class="cstat-no" title="statement not covered" >        return this._clipList;</span>
    }
};
&nbsp;
export default Animator;
&nbsp;</pre></td></tr>
</table></pre>
<div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Fri Feb 23 2018 01:09:23 GMT+0800 (CST)
</div>
</div>
<script src="../../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../../sorter.js"></script>
</body>
</html>
